package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryPauseAlarmDurationConfigReq;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryPauseAlarmDurationConfigRes;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.UpdatePauseAlarmDurationConfigReq;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.UpdatePauseAlarmDurationConfigRes;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface PauseAlarmConfigService {

    /**
     * 查询列表
     * @param queryPauseAlarmDurationConfigReq
     * @return
     */
    QueryPauseAlarmDurationConfigRes queryPauseAlarmDurationConfig(QueryPauseAlarmDurationConfigReq queryPauseAlarmDurationConfigReq);

    /**
     * 暂停报警时长限制修改
     * @param updatePauseAlarmDurationConfigReq
     * @return
     */
    UpdatePauseAlarmDurationConfigRes updatePauseAlarmDurationConfig(UpdatePauseAlarmDurationConfigReq updatePauseAlarmDurationConfigReq);
}
