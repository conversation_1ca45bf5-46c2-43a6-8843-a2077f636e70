package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.saicmobility.evcard.md.mddataproxy.api.CityDataList;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.md.mdordercenter.api.CarRentalTravelInfo;
import com.saicmobility.evcard.md.mdordercenter.api.MdOrderCenter;
import com.saicmobility.evcard.md.mdordercenter.api.SearchCarRentalTravelInfoReq;
import com.saicmobility.evcard.md.mdordercenter.api.SearchCarRentalTravelInfoRes;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.config.DubboRestProxyConfiguration;
import com.saicmobility.evcard.vlms.risk.dto.Address;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.terminal.*;
import com.saicmobility.evcard.vlms.risk.enums.ExcelTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.FileSourceEnum;
import com.saicmobility.evcard.vlms.risk.enums.TBoxEnum;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.service.ExportFileService;
import com.saicmobility.evcard.vlms.risk.service.VehicleTerminalService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.RedisUtils;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryCityConfigListReq;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryCityConfigListRes;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import krpc.rpc.core.ClientContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.saicmobility.evcard.vlms.risk.constant.RealFields.VEHICLE_REALDATA_KEY;
import static com.saicmobility.evcard.vlms.risk.constant.RealFields.VEHICLE_THIRD_REALDATA_KEY;

/**
 * <AUTHOR>
 * @date 2024-04-09
 */
@Service
@Slf4j
public class VehicleTerminalServiceImpl implements VehicleTerminalService {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private MdDataProxy mdDataProxy;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ExportFileService exportFileService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private RiskCommonServiceImpl riskCommonService;
    @Resource
    private MdOrderCenter mdOrderCenter;
    @Resource
    private DubboRestProxyConfiguration dubboRestProxyConfiguration;
    @Override
    public QueryTerminalInfoByVinRes queryTerminalInfoByVin(QueryTerminalInfoByVinReq queryTerminalInfoByVinReq) {
        String vin = queryTerminalInfoByVinReq.getVin();
        if(StringUtils.isEmpty(vin)){
            GetVehicleInfoByVehicleNoRes res = mdDataProxy.getVehicleInfoByVehicleNo(GetVehicleInfoByVehicleNoReq.newBuilder().setVehicleNo(queryTerminalInfoByVinReq.getVehicleNo()).build());
            if(res.getRetCode() == 0 && StringUtils.isNotEmpty(res.getVin())){
                vin = res.getVin();
            }else {
                return QueryTerminalInfoByVinRes.failed(-2557001,"车辆信息不存在！");
            }
        }
        if(StringUtils.isNotEmpty(vin)){
            BatchQueryVehicleTerminalDataRes batchQueryVehicleTerminalDataRes = mdDataProxy.batchQueryVehicleTerminalData(BatchQueryVehicleTerminalDataReq.newBuilder().addVinS(vin).build());
            QueryTerminalInfoByVinDTORes queryTerminalInfoByVinDTORes = QueryTerminalInfoByVinDTORes.toRes(batchQueryVehicleTerminalDataRes.getDataList());
            return PbConvertUtil.generateProtoBuffer(queryTerminalInfoByVinDTORes, QueryTerminalInfoByVinRes.class);
        }
        return QueryTerminalInfoByVinRes.ok();
    }

    @Override
    public QueryVehicleTrackRes queryVehicleTrack(QueryVehicleTrackReq queryVehicleTrackReq) {
        String vin = queryVehicleTrackReq.getVin();
        int queryType = queryVehicleTrackReq.getQueryType();
        String dataSource = queryVehicleTrackReq.getDataSource();
        if(StringUtils.isEmpty(dataSource)){
            return QueryVehicleTrackRes.failed(-2557002,"请选择终端！");
        }
        if(StringUtils.isEmpty(vin)){
            GetVehicleInfoByVehicleNoRes res = mdDataProxy.getVehicleInfoByVehicleNo(GetVehicleInfoByVehicleNoReq.newBuilder().setVehicleNo(queryVehicleTrackReq.getVehicleNo()).build());
            if(res.getRetCode() == 0 && StringUtils.isNotEmpty(res.getVin())){
                vin = res.getVin();
            }else {
                return QueryVehicleTrackRes.failed(-2557001,"车辆信息不存在！");
            }
        }
        QueryVehicleTrackDTORes queryVehicleTrackDTORes = new QueryVehicleTrackDTORes();
        if(queryType == 1){
            if(dataSource.equals(TBoxEnum.WAN_WEI.getType())){
                realTimeMonitorByWanWei(vin,queryVehicleTrackDTORes);
                return PbConvertUtil.generateProtoBuffer(queryVehicleTrackDTORes, QueryVehicleTrackRes.class);
            }
            realTimeMonitor(vin,dataSource,queryVehicleTrackDTORes);
        }else {
            queryVehicleTrackDTORes = queryVehicleHistoryTrack(vin,queryVehicleTrackReq.getDataSource(),queryVehicleTrackReq.getStartTime(),queryVehicleTrackReq.getEndTime());
        }
        return PbConvertUtil.generateProtoBuffer(queryVehicleTrackDTORes, QueryVehicleTrackRes.class);
    }



    private QueryVehicleTrackDTORes queryVehicleHistoryTrack(String vin, String dataSource, String startTime, String endTime) {
        VehicleStatusQueryBO vehicleStatusQueryBO = new VehicleStatusQueryBO();
        vehicleStatusQueryBO.setVin(vin);
        vehicleStatusQueryBO.setStartTime(startTime);
        vehicleStatusQueryBO.setEndTime(endTime);
        vehicleStatusQueryBO.setDataSource(dataSource);
        vehicleStatusQueryBO.setPageSize(6000);
        QueryVehicleTrackDTORes res = new QueryVehicleTrackDTORes();
        try {
            String resStr = restTemplate.postForObject(dubboRestProxyConfiguration.getQueryVehicleHistoryTrackUrl(),
                    vehicleStatusQueryBO, String.class);
            List<VehicleLocationDTO> vehicleLocationDTOS = JSONObject.parseArray(resStr, VehicleLocationDTO.class);
            List<VehicleTrackDTO> vehicleTrackDTOList = new ArrayList<>();
            for(VehicleLocationDTO dto:vehicleLocationDTOS){
                VehicleTrackDTO vehicleTrackDTO = new VehicleTrackDTO();
                vehicleTrackDTO.setVin(vin);
                vehicleTrackDTO.setLatitudeBD(String.valueOf(dto.getLatitudeBD()));
                vehicleTrackDTO.setLongitudeBD(String.valueOf(dto.getLongitudeBD()));
                vehicleTrackDTO.setGpsDateTime(dto.getLocationTime());
                vehicleTrackDTO.setSpeed(String.valueOf(dto.getSpeed()));
                vehicleTrackDTO.setDirection(String.valueOf(dto.getDirection()));
                vehicleTrackDTO.setReceiveTime(dto.getTimeStamp());
                vehicleTrackDTO.setRoadName(dto.getPositionName());
                vehicleTrackDTO.setTotalMileage(dto.getTotalMileage());
                vehicleTrackDTOList.add(vehicleTrackDTO);
            }
            res.setData(vehicleTrackDTOList);
            return res;
        } catch (Exception e) {
            log.error("queryOrderTravelListByOrderSeq " + e.getMessage(), e);
            return res;
        }
    }

    @Override
    public QueryOrderTravelListByOrderSeqRes queryOrderTravelListByOrderSeq(QueryOrderTravelListByOrderSeqReq queryOrderTravelListByOrderSeqReq) {
        String orderSeq = queryOrderTravelListByOrderSeqReq.getOrderSeq();
        if(StringUtils.isEmpty(orderSeq)){
            return QueryOrderTravelListByOrderSeqRes.failed(-2557003,"查询订单号不可为空！");
        }
        QueryOrderTravelListByOrderSeqDTORes res = new QueryOrderTravelListByOrderSeqDTORes();
        if(orderSeq.contains("MC")){
            SearchCarRentalTravelInfoRes searchCarRentalTravelInfoRes = mdOrderCenter.searchCarRentalTravelInfo(SearchCarRentalTravelInfoReq.newBuilder().setContractId(orderSeq).build());
            if(searchCarRentalTravelInfoRes.getRetCode() == 0 && !searchCarRentalTravelInfoRes.getInfoList().isEmpty()){
                List<CarRentalTravelInfo> infoList = searchCarRentalTravelInfoRes.getInfoList();
                List<OrderTravelDTO> orderTravelDTOS = new ArrayList<>();
                for(CarRentalTravelInfo info:infoList){
                    OrderTravelDTO orderTravelDTO = new OrderTravelDTO();
                    orderTravelDTO.setId(info.getId());
                    orderTravelDTO.setVin(info.getVin());
                    orderTravelDTO.setVehicleNo(info.getVehicleNo());
                    orderTravelDTO.setPickupTime(DateUtil.getDateFromTimeStr(info.getPickupTime(),DateUtil.DATE_TYPE1));
                    if(StringUtils.isNotEmpty(info.getReturnTime())){
                        orderTravelDTO.setReturnTime(DateUtil.getDateFromTimeStr(info.getReturnTime(),DateUtil.DATE_TYPE1));
                    }
                    orderTravelDTOS.add(orderTravelDTO);
                }
                res.setData(orderTravelDTOS);
                return PbConvertUtil.generateProtoBuffer(res, QueryOrderTravelListByOrderSeqRes.class);
            }
            return QueryOrderTravelListByOrderSeqRes.ok();
        }
        try {
            QueryOrderDto queryOrderDto = new QueryOrderDto();
            queryOrderDto.setOrderSeq(orderSeq);
            String resStr = restTemplate.postForObject(dubboRestProxyConfiguration.getOrderTravelListUrl(),queryOrderDto,String.class);
            List<OrderTravelDTO> orderTravelDTOS = JSONObject.parseArray(resStr, OrderTravelDTO.class);
            res.setData(orderTravelDTOS);
            return PbConvertUtil.generateProtoBuffer(res, QueryOrderTravelListByOrderSeqRes.class);
        } catch (Exception e) {
            log.error("queryOrderTravelListByOrderSeq " + e.getMessage(), e);
            return QueryOrderTravelListByOrderSeqRes.failed(-2557004,"查询异常");
        }
    }

    @Override
    public QueryCityConfigListRes queryCityConfigList(QueryCityConfigListReq queryCityConfigListReq) {
        com.saicmobility.evcard.md.mddataproxy.api.QueryCityConfigListRes queryCityConfigListRes = mdDataProxy.queryCityConfigList(com.saicmobility.evcard.md.mddataproxy.api.QueryCityConfigListReq.newBuilder().setCityName(queryCityConfigListReq.getCityName())
                .addAllCitySortList(queryCityConfigListReq.getCitySortListList()).build());
        if(queryCityConfigListRes.getRetCode() != 0){
            return QueryCityConfigListRes.failed(-2557001,"查询城市配置异常！");
        }
        List<CityDataList> cityDataListList = queryCityConfigListRes.getCityDataListList();
        return  QueryCityConfigListDTORes.toRes(cityDataListList);
    }

    @Override
    public ExportVehicleTrackRes exportVehicleTrack(QueryVehicleTrackReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();

        String fileName = ExcelTypeEnum.EXCEL_TYPE_7.getValue()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
        String templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_7.getTemplate()+".xlsx";

        // 开始导出
        String fileName1 = ExcelTypeEnum.EXCEL_TYPE_7.getValue();
        String exportFilePath = FileUtils.getFilePathSuffix(fileName1,1);
        String fullExportFilePath = mfsRootPath + exportFilePath;
        String downLoadFilePath = projectDownloadUrl + exportFilePath;

        Integer exportFileId = exportFileService.startExport(FileSourceEnum.FILE_SOURCE_25.getSource(), fileName, downLoadFilePath, currentUser.getNickName(),
                currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName());
        if (exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }
        String vin = req.getVin();
        if(StringUtils.isEmpty(req.getVin())){
            GetVehicleInfoByVehicleNoRes res = mdDataProxy.getVehicleInfoByVehicleNo(GetVehicleInfoByVehicleNoReq.newBuilder().setVehicleNo(req.getVehicleNo()).build());
            if(res.getRetCode() == 0 && StringUtils.isNotEmpty(res.getVin())){
                vin = res.getVin();
            }else {
                return ExportVehicleTrackRes.failed(-2557001,"车辆信息不存在！");
            }
        }
        // 异步导出
        String finalVin = vin;
        taskExecutor.execute(()->{
            List<VehicleTrackDTO> vehicleTrackList = queryVehicleHistoryTrack(finalVin,req.getDataSource(),req.getStartTime(),req.getEndTime()).getData();
            if (CollectionUtil.isEmpty(vehicleTrackList)){
                return;
            }
            //如果目录不存在，则创建目录
            File file = new File(fullExportFilePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            boolean noResult = ObjectUtil.isNull(vehicleTrackList);
            try (
                    InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(templateFile);
                    FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                    ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();
            ) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                //无数据处理
                if(noResult){
                    excelWriter.fill(new ArrayList<>(), writeSheet);
                }else {
                    excelWriter.fill(vehicleTrackList, writeSheet);
                }
                fileOutputStream.flush();
                excelWriter.finish();
                //导出成功
                exportFileService.exportSuccess(exportFileId, currentUser.getNickName());
            }catch (Exception e) {
                e.printStackTrace();
                log.error(e.getLocalizedMessage(), e);
                //导出失败
                exportFileService.exportFail(exportFileId, e.getLocalizedMessage(), currentUser.getNickName());
            }
        });
        return ExportVehicleTrackRes.ok();
    }

    /**
     * 实时监控
     * @param vin
     * @param dataSource
     * @param queryVehicleTrackDTORes
     */
    private void realTimeMonitor(String vin, String dataSource, QueryVehicleTrackDTORes queryVehicleTrackDTORes) {
        String enumDescByCode = TBoxEnum.getEnumDescByCode(dataSource);
        List<VehicleTrackDTO> data = new ArrayList<>();

        Map<Object, Object> realDataMap = null;
        if(StringUtils.isNotEmpty(enumDescByCode)){
            realDataMap = redisUtils.hmget(VEHICLE_REALDATA_KEY + enumDescByCode + "_" + vin);
        }
        if(MapUtil.isEmpty(realDataMap)){
            return;
        }
        //非万维
        realDataMap.put("DataSource","");
        data.add(getVehicleGPSData(realDataMap));
        queryVehicleTrackDTORes.setData(data);
    }

    /**
     * 组装数据
     * @param realDataMap
     * @return
     */
    private VehicleTrackDTO getVehicleGPSData(Map<Object, Object> realDataMap) {
        VehicleTrackDTO vehicleTrackDTO = new VehicleTrackDTO();
        if(null != realDataMap.get("UpdateTime")){
            vehicleTrackDTO.setReceiveTime(realDataMap.get("UpdateTime").toString());
            if(StringUtils.isNotEmpty(realDataMap.get("UpdateTime").toString()) && !realDataMap.get("UpdateTime").toString().contains("-")){
                vehicleTrackDTO.setReceiveTime(DateUtil.hdTimeTODateStr(Long.valueOf(realDataMap.get("UpdateTime").toString()), (short) 0, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        if(null != realDataMap.get("gpsDateTime")){
            vehicleTrackDTO.setGpsDateTime(realDataMap.get("gpsDateTime").toString());
            if(StringUtils.isNotEmpty(realDataMap.get("gpsDateTime").toString()) && !realDataMap.get("gpsDateTime").toString().contains("-")){
                vehicleTrackDTO.setGpsDateTime(DateUtil.hdTimeTODateStr(Long.valueOf(realDataMap.get("gpsDateTime").toString()), (short) 0, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        if(null != realDataMap.get("Direction")){
            vehicleTrackDTO.setDirectionAngle(realDataMap.get("Direction").toString());
            if (StringUtils.isNotEmpty(realDataMap.get("Direction").toString())) {
                vehicleTrackDTO.setDirection(gpsDirection((int) Double.parseDouble(realDataMap.get("Direction").toString())));
            }
        }
        vehicleTrackDTO.setLatitude(String.valueOf(0));
        if(null != realDataMap.get("Latitude") && StringUtils.isNotEmpty(realDataMap.get("Latitude").toString())){
            if(Double.parseDouble(realDataMap.get("Latitude").toString()) > 1000){
                try {
                    vehicleTrackDTO.setLatitude(String.valueOf(Double.parseDouble(realDataMap.get("Latitude").toString())/1000000));
                }catch (Exception e){
                }
            }else {
                vehicleTrackDTO.setLatitude(realDataMap.get("Latitude").toString());
            }
        }
        vehicleTrackDTO.setLongitude(String.valueOf(0));
        if(null != realDataMap.get("Longitude") && StringUtils.isNotEmpty(realDataMap.get("Longitude").toString())){
            if(Double.parseDouble(realDataMap.get("Longitude").toString()) > 1000){
                try {
                    vehicleTrackDTO.setLongitude(String.valueOf(Double.parseDouble(realDataMap.get("Longitude").toString())/1000000));
                }catch (Exception e){
                }
            }else {
                vehicleTrackDTO.setLongitude(realDataMap.get("Longitude").toString());
            }
        }
        if(StringUtils.isNotEmpty(vehicleTrackDTO.getLatitude()) && StringUtils.isNotEmpty(vehicleTrackDTO.getLongitude())){
            if(realDataMap.get("DataSource").equals(TBoxEnum.WAN_WEI.getType())){
                Address address = riskCommonService.reverseGeocoding(Double.parseDouble(vehicleTrackDTO.getLatitude()), Double.parseDouble(vehicleTrackDTO.getLongitude()), 0);
                vehicleTrackDTO.setRoadName(address.getFormattedAddress());
                vehicleTrackDTO.setLatitude(Double.toString(address.getLat()));
                vehicleTrackDTO.setLatitudeBD(Double.toString(address.getLat()));
                vehicleTrackDTO.setLongitude(Double.toString(address.getLng()));
                vehicleTrackDTO.setLongitudeBD(Double.toString(address.getLng()));
            }else {
                VehicleGPSData vehicleGPSData = new VehicleGPSData();
                vehicleGPSData.setLatitude(vehicleTrackDTO.getLatitude());
                vehicleGPSData.setLongitude(vehicleTrackDTO.getLongitude());
                riskCommonService.getCityInfoFromBaidu(vehicleGPSData);
                vehicleTrackDTO.setRoadName(vehicleGPSData.getLastAddress());
            }
        }
        vehicleTrackDTO.setSpeed("0");
        if(null != realDataMap.get("GPSSpeed") || null != realDataMap.get("Speed")){
            String speed = realDataMap.get("GPSSpeed") == null?realDataMap.get("Speed").toString():realDataMap.get("GPSSpeed").toString();
            if(StringUtils.isNotEmpty(speed)){
                vehicleTrackDTO.setSpeed(speed);
            }
        }

        return vehicleTrackDTO;
    }

    /**
     * 获取万维GPS
     * @param vin
     * @param queryVehicleTrackDTORes
     * @return
     */
    private void realTimeMonitorByWanWei(String vin,QueryVehicleTrackDTORes queryVehicleTrackDTORes) {
        List<VehicleTrackDTO> data = new ArrayList<>();
        Map<Object, Object> realDataMap = redisUtils.hmget(VEHICLE_THIRD_REALDATA_KEY + vin);
        if(MapUtil.isEmpty(realDataMap)){
            return;
        }
        realDataMap.put("DataSource",TBoxEnum.WAN_WEI.getType());
        data.add(getVehicleGPSData(realDataMap));
        queryVehicleTrackDTORes.setData(data);
    }


    /**
     * 根据GPS的方向值获取方向
     *
     * @param direction
     * @return
     */
    public static String gpsDirection(int direction) {
        if (direction == 0) {
            return "正北";
        } else if (direction == 90) {
            return "正东";
        } else if (direction == 180) {
            return "正南";
        } else if (direction == 270) {
            return "正西";
        } else if (direction > 0 && direction < 90) {
            return "东北";
        } else if (direction > 90 && direction < 180) {
            return "东南";
        } else if (direction > 180 && direction < 270) {
            return "西南";
        } else if (direction > 270 && direction < 360) {
            return "西北";
        }
        return "";
    }

}
