package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskPushRecord;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 车辆超出电子围栏预警处理器 - 优化版本
 * 
 * 主要功能：
 * 1. 监控车辆是否超出指定的电子围栏范围
 * 2. 根据超出时间和车辆类型创建不同级别的告警
 * 3. 处理告警的升级、恢复和暂停逻辑
 * 4. 与长租系统进行告警信息同步
 * 
 * 业务规则：
 * - 长租车辆：超出3天创建1级告警，超出7天创建2级告警
 * - 短租车辆：立即创建1级告警，超出10天创建2级告警
 * - 车管中心车辆：特殊处理逻辑
 * 
 * 优化特性：
 * - 批量数据处理，提高性能
 * - 完善的异常处理和重试机制
 * - 并行处理支持
 * - 详细的性能监控和日志
 * 
 * <AUTHOR>
 * @date 2024-02-20
 * @version 2.0 - 优化版本
 */
@Slf4j
@JobHandler(value = "vehicleHighRiskFenceWarningHandlerOptimized")
@Component
public class VehicleHighRiskFenceWarningHandlerOptimized extends IJobHandler {

    // ==================== 常量定义 ====================
    
    /**
     * 分页查询大小 - 优化后的批量处理大小
     */
    private static final int PAGE_SIZE = 2000;
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    /**
     * 重试延迟基础时间（毫秒）
     */
    private static final long RETRY_BASE_DELAY_MS = 1000;
    
    /**
     * 并行处理阈值 - 超过此数量的车辆将使用并行处理
     */
    private static final int PARALLEL_PROCESSING_THRESHOLD = 50;
    
    /**
     * 线程池最大大小
     */
    private static final int MAX_THREAD_POOL_SIZE = 10;
    
    /**
     * 批量查询大小 - 避免IN子句过长
     */
    private static final int BATCH_QUERY_SIZE = 100;
    
    /**
     * 任务失败率阈值 - 超过此比例认为任务失败
     */
    private static final double FAILURE_RATE_THRESHOLD = 0.5;

    // ==================== 业务常量 ====================
    
    /**
     * 产品线代码
     */
    private static final class ProductLineCode {
        /** 车管中心 */
        static final Integer VEHICLE_MANAGEMENT_CENTER = ParserForTypeEnum.VEHICLE_MANAGEMENT_CENTER.getCode();
        /** 长租 */
        static final Integer LONG_TERM = ParserForTypeEnum.LONG_TERM.getCode();
        /** 短租 */
        static final Integer SHORT_TERM = ParserForTypeEnum.SHORT_TERM.getCode();
    }
    
    /**
     * 告警时间阈值（天）
     */
    private static final class AlarmThresholdDays {
        /** 长租3天告警阈值 */
        static final int LONG_TERM_LEVEL_ONE = 3;
        /** 长租7天告警阈值 */
        static final int LONG_TERM_LEVEL_TWO = 7;
        /** 短租10天告警阈值 */
        static final int SHORT_TERM_LEVEL_TWO = 10;
        /** 告警升级时间阈值 */
        static final int ALARM_UPGRADE_THRESHOLD = 7;
        /** 短租30分钟告警阈值 */
        static final int SHORT_TERM_MINUTE_WARNING = 30;
    }
    
    /**
     * 错误代码定义
     */
    private static final class ErrorCode {
        static final String NETWORK_TIMEOUT = "NETWORK_TIMEOUT";
        static final String DATABASE_CONNECTION_ERROR = "DATABASE_CONNECTION_ERROR";
        static final String REDIS_CONNECTION_ERROR = "REDIS_CONNECTION_ERROR";
        static final String INVALID_VEHICLE_DATA = "INVALID_VEHICLE_DATA";
        static final String VEHICLE_NOT_FOUND = "VEHICLE_NOT_FOUND";
        static final String GPS_DATA_INVALID = "GPS_DATA_INVALID";
        static final String CRITICAL_SYSTEM_ERROR = "CRITICAL_SYSTEM_ERROR";
        static final String CONTEXT_BUILD_ERROR = "CONTEXT_BUILD_ERROR";
        static final String UNEXPECTED_ERROR = "UNEXPECTED_ERROR";
    }

    // ==================== 依赖注入 ====================
    
    @Resource
    private OutDescIdUtil outDescIdUtil;
    
    @Resource
    private MdDataProxy mdDataProxy;
    
    @Resource
    private RiskAlarmService riskAlarmService;
    
    @Resource
    private VlmsAssetsService vlmsAssetsService;
    
    @Resource
    private TableRiskAlarmService tableRiskAlarmService;
    
    @Resource
    private RiskCommonService riskCommonService;
    
    @Resource
    private SubAssetsService subAssetsService;
    
    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;
    
    @Resource
    private TableOperateLogService tableOperateLogService;
    
    @Resource
    private SubRiskAlaramService subRiskAlaramService;
    
    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;

    // ==================== 主要方法 ====================
    
    /**
     * 执行车辆电子围栏预警任务
     * 
     * @param queryVin 指定查询的车架号，为空时查询所有配置的车辆
     * @return 任务执行结果
     * @throws Exception 任务执行异常
     */
    @Override
    public ReturnT<String> execute(String queryVin) throws Exception {
        log.info("车辆电子围栏预警任务开始执行，查询条件VIN: {}", 
                StringUtils.isBlank(queryVin) ? "全部" : queryVin);
        
        long taskStartTime = System.currentTimeMillis();
        TaskExecutionStatistics statistics = new TaskExecutionStatistics();
        
        try {
            processVehiclesInBatches(queryVin, statistics);
            
            logTaskCompletion(statistics, taskStartTime);
            return determineTaskResult(statistics);
            
        } catch (Exception e) {
            log.error("车辆电子围栏预警任务执行异常", e);
            return ReturnT.FAIL;
        }
    }
    
    /**
     * 分批处理车辆
     * 
     * @param queryVin 查询条件VIN
     * @param statistics 执行统计信息
     */
    private void processVehiclesInBatches(String queryVin, TaskExecutionStatistics statistics) {
        int currentPageNumber = 1;
        List<RiskAlarmConfigData> currentBatchVehicles;
        
        do {
            currentBatchVehicles = queryVehicleConfigurationsBatch(queryVin, currentPageNumber);
            
            if (CollectionUtil.isEmpty(currentBatchVehicles)) {
                log.info("第{}页未查询到车辆配置数据，结束处理", currentPageNumber);
                break;
            }
            
            log.info("开始处理第{}页车辆，数量: {}", currentPageNumber, currentBatchVehicles.size());
            
            BatchProcessingResult batchResult = processVehicleBatch(currentBatchVehicles);
            statistics.addBatchResult(batchResult);
            
            currentPageNumber++;
            
        } while (currentBatchVehicles.size() == PAGE_SIZE);
    }
    
    /**
     * 查询车辆配置批次数据
     * 
     * @param queryVin 查询VIN条件
     * @param pageNumber 页码
     * @return 车辆配置列表
     */
    private List<RiskAlarmConfigData> queryVehicleConfigurationsBatch(String queryVin, int pageNumber) {
        try {
            return tableRiskAlarmConfigService.selectList(
                QueryRiskAlarmConfigReq.newBuilder()
                    .setVin(queryVin)
                    .setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())
                    .setPageNum(pageNumber)
                    .setPageSize(PAGE_SIZE)
                    .build()
            );
        } catch (Exception e) {
            log.error("查询第{}页车辆配置失败", pageNumber, e);
            throw new RuntimeException("查询车辆配置失败", e);
        }
    }
    
    /**
     * 处理车辆批次
     * 
     * @param vehicleConfigurations 车辆配置列表
     * @return 批次处理结果
     */
    private BatchProcessingResult processVehicleBatch(List<RiskAlarmConfigData> vehicleConfigurations) {
        if (CollectionUtil.isEmpty(vehicleConfigurations)) {
            return new BatchProcessingResult(0, 0, 0, 0);
        }
        
        long batchStartTime = System.currentTimeMillis();
        
        // 根据数据量选择处理策略
        BatchProcessingResult result;
        if (vehicleConfigurations.size() > PARALLEL_PROCESSING_THRESHOLD) {
            result = processVehiclesInParallel(vehicleConfigurations);
        } else {
            result = processVehiclesSequentially(vehicleConfigurations);
        }
        
        logBatchPerformance(vehicleConfigurations.size(), batchStartTime);
        return result;
    }

    /**
     * 顺序处理车辆列表
     *
     * @param vehicleConfigurations 车辆配置列表
     * @return 批次处理结果
     */
    private BatchProcessingResult processVehiclesSequentially(List<RiskAlarmConfigData> vehicleConfigurations) {
        int processedCount = 0;
        int successfulCount = 0;
        int failedCount = 0;
        int skippedCount = 0;

        for (RiskAlarmConfigData vehicleConfig : vehicleConfigurations) {
            processedCount++;
            VehicleProcessingResult result = processSingleVehicleWithRetry(vehicleConfig);

            if (result.isSuccessful()) {
                successfulCount++;
            } else if (result.isSkipped()) {
                skippedCount++;
            } else {
                failedCount++;
            }
        }

        return new BatchProcessingResult(processedCount, successfulCount, failedCount, skippedCount);
    }

    /**
     * 并行处理车辆列表
     *
     * @param vehicleConfigurations 车辆配置列表
     * @return 批次处理结果
     */
    private BatchProcessingResult processVehiclesInParallel(List<RiskAlarmConfigData> vehicleConfigurations) {
        // 使用线程池并行处理
        int threadPoolSize = Math.min(MAX_THREAD_POOL_SIZE, vehicleConfigurations.size());
        ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);

        try {
            List<CompletableFuture<VehicleProcessingResult>> futures = vehicleConfigurations.stream()
                .map(vehicle -> CompletableFuture.supplyAsync(() ->
                    processSingleVehicleWithRetry(vehicle), executor))
                .collect(Collectors.toList());

            // 等待所有任务完成并收集结果
            List<VehicleProcessingResult> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

            // 统计结果
            int processedCount = results.size();
            int successfulCount = (int) results.stream().filter(VehicleProcessingResult::isSuccessful).count();
            int skippedCount = (int) results.stream().filter(VehicleProcessingResult::isSkipped).count();
            int failedCount = processedCount - successfulCount - skippedCount;

            return new BatchProcessingResult(processedCount, successfulCount, failedCount, skippedCount);

        } finally {
            shutdownExecutor(executor);
        }
    }

    /**
     * 安全关闭线程池
     *
     * @param executor 线程池
     */
    private void shutdownExecutor(ExecutorService executor) {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("线程池未能正常关闭");
                }
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
            log.warn("线程池关闭被中断");
        }
    }

    /**
     * 处理单个车辆（带重试机制）
     *
     * @param vehicleConfiguration 车辆配置
     * @return 处理结果
     */
    private VehicleProcessingResult processSingleVehicleWithRetry(RiskAlarmConfigData vehicleConfiguration) {
        String vehicleVin = vehicleConfiguration.getVin();
        int retryAttemptCount = 0;

        while (retryAttemptCount <= MAX_RETRY_ATTEMPTS) {
            try {
                log.debug("处理车辆: {}, 尝试次数: {}", vehicleVin, retryAttemptCount + 1);

                VehicleProcessingResult result = processSingleVehicle(vehicleConfiguration);

                if (result.isSuccessful()) {
                    log.debug("车辆处理成功: {}", vehicleVin);
                    return result;
                }

                // 如果是业务逻辑跳过，不需要重试
                if (result.isSkipped()) {
                    return result;
                }

            } catch (VehicleProcessingException e) {
                ErrorHandlingStrategy strategy = determineErrorHandlingStrategy(e);

                if (handleVehicleProcessingException(e, strategy, retryAttemptCount)) {
                    break; // 不需要重试
                }

            } catch (Exception e) {
                log.error("车辆处理发生未预期异常: {}, 尝试次数: {}", vehicleVin, retryAttemptCount + 1, e);

                if (retryAttemptCount >= MAX_RETRY_ATTEMPTS) {
                    recordProcessingFailure(vehicleVin, ErrorCode.UNEXPECTED_ERROR, e.getMessage());
                    return VehicleProcessingResult.failed(vehicleVin, "未预期异常: " + e.getMessage());
                }
            }

            retryAttemptCount++;
            if (retryAttemptCount <= MAX_RETRY_ATTEMPTS) {
                waitForRetry(retryAttemptCount);
            }
        }

        return VehicleProcessingResult.failed(vehicleVin, "达到最大重试次数");
    }

    /**
     * 处理单个车辆的核心逻辑
     *
     * @param vehicleConfiguration 车辆配置
     * @return 处理结果
     * @throws VehicleProcessingException 车辆处理异常
     */
    private VehicleProcessingResult processSingleVehicle(RiskAlarmConfigData vehicleConfiguration)
            throws VehicleProcessingException {

        String vehicleVin = vehicleConfiguration.getVin();

        // 检查是否应该跳过处理
        if (shouldSkipVehicleProcessing(vehicleConfiguration)) {
            log.info("跳过车辆处理: {}, 原因: 在白名单中", vehicleVin);
            return VehicleProcessingResult.skipped(vehicleVin, "在白名单中");
        }

        // 构建车辆处理上下文
        VehicleProcessingContext processingContext = buildVehicleProcessingContext(vehicleConfiguration);

        if (processingContext == null) {
            log.warn("跳过车辆处理: {}, 原因: 无法构建处理上下文", vehicleVin);
            return VehicleProcessingResult.skipped(vehicleVin, "无法构建处理上下文");
        }

        // 验证车辆状态
        if (shouldIgnoreVehicleByStatus(processingContext)) {
            log.info("跳过车辆处理: {}, 原因: 车辆状态不符合告警条件", vehicleVin);
            return VehicleProcessingResult.skipped(vehicleVin, "车辆状态不符合告警条件");
        }

        // 执行告警处理逻辑
        executeVehicleAlarmProcessing(processingContext);

        return VehicleProcessingResult.successful(vehicleVin);
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查是否应该跳过车辆处理
     *
     * @param vehicleConfiguration 车辆配置
     * @return true表示应该跳过
     */
    private boolean shouldSkipVehicleProcessing(RiskAlarmConfigData vehicleConfiguration) {
        return riskCommonService.isWhiteList(
            vehicleConfiguration.getVin(),
            AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode()
        );
    }

    /**
     * 构建车辆处理上下文
     *
     * @param vehicleConfiguration 车辆配置
     * @return 处理上下文
     * @throws VehicleProcessingException 构建异常
     */
    private VehicleProcessingContext buildVehicleProcessingContext(RiskAlarmConfigData vehicleConfiguration)
            throws VehicleProcessingException {
        String vin = vehicleConfiguration.getVin();

        try {
            // 获取GPS数据
            VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(vin);
            if (vehicleGPSData == null || StringUtils.isEmpty(vehicleGPSData.getCityName())
                || vehicleGPSData.getGpsDateTime() == null) {
                throw new VehicleProcessingException(vin, ErrorCode.GPS_DATA_INVALID,
                    "GPS数据无效或缺失", null);
            }

            // 获取车辆文件信息
            SearchVehicleFileList vehicleFileInfo = getVehicleFileInfo(vin);
            if (vehicleFileInfo == null) {
                throw new VehicleProcessingException(vin, ErrorCode.VEHICLE_NOT_FOUND,
                    "未找到车辆文件信息", null);
            }

            // 获取电子围栏信息
            enrichWithElectronicFenceInfo(vehicleGPSData, vin);

            // 查询现有告警
            RiskAlarm riskAlarm = riskAlarmService.queryDataByVinAndAlarmType(
                vin, AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode());

            // 查询车辆风险状态
            Integer vehicleRiskStatus = riskCommonService.queryMaxVehicleRiskStatus(vin, null);

            return new VehicleProcessingContext(vehicleFileInfo, vehicleGPSData,
                                               riskAlarm, vehicleRiskStatus);

        } catch (VehicleProcessingException e) {
            throw e; // 重新抛出已知异常
        } catch (Exception e) {
            throw new VehicleProcessingException(vin, ErrorCode.CONTEXT_BUILD_ERROR,
                "构建车辆处理上下文失败", e);
        }
    }

    /**
     * 获取车辆文件信息
     *
     * @param vin 车架号
     * @return 车辆文件信息
     */
    private SearchVehicleFileList getVehicleFileInfo(String vin) {
        try {
            List<SearchVehicleFileList> vehicleList = subAssetsService.searchVehicleFileList(null, null, 0, vin);
            return CollectionUtil.isNotEmpty(vehicleList) ? vehicleList.get(0) : null;
        } catch (Exception e) {
            log.error("获取车辆文件信息失败: {}", vin, e);
            return null;
        }
    }

    /**
     * 补充电子围栏信息
     *
     * @param vehicleGPSData GPS数据
     * @param vin 车架号
     */
    private void enrichWithElectronicFenceInfo(VehicleGPSData vehicleGPSData, String vin) {
        try {
            // 查询电子围栏规则
            SearchElectronicFenceRegulationRes searchElectronicFenceRegulationRes =
                mdDataProxy.searchElectronicFenceRegulation(
                    SearchElectronicFenceRegulationReq.newBuilder()
                        .setVin(vin)
                        .build()
                );

            if (searchElectronicFenceRegulationRes != null &&
                searchElectronicFenceRegulationRes.getRetCode() == 0) {
                vehicleGPSData.setPenCityScope(searchElectronicFenceRegulationRes.getPenCityScope());
            }
        } catch (Exception e) {
            log.error("获取电子围栏信息失败: {}", vin, e);
            // 设置默认值，避免空指针
            vehicleGPSData.setPenCityScope("");
        }
    }

    /**
     * 根据车辆状态判断是否应该忽略告警
     *
     * @param processingContext 处理上下文
     * @return true表示应该忽略
     */
    private boolean shouldIgnoreVehicleByStatus(VehicleProcessingContext processingContext) {
        SearchVehicleFileList vehicleInfo = processingContext.getVehicleFileInfo();
        return RiskUtils.isIgnoreAlarm(vehicleInfo.getPropertyStatus(), vehicleInfo.getDeliveryStatus());
    }

    /**
     * 执行车辆告警处理逻辑
     *
     * @param processingContext 处理上下文
     */
    private void executeVehicleAlarmProcessing(VehicleProcessingContext processingContext) {
        // 计算超出围栏天数
        long fenceExceedDays = calculateFenceExceedDays(processingContext);

        // 根据是否存在现有告警选择处理策略
        if (processingContext.getExistingAlarm() == null) {
            handleNewAlarmCreation(processingContext, fenceExceedDays);
        } else {
            handleExistingAlarmUpdate(processingContext, fenceExceedDays);
        }
    }

    /**
     * 计算车辆超出电子围栏的天数
     *
     * 逻辑：先计算GPS时间差，然后根据是否超出围栏决定是否使用Redis计数器
     *
     * @param processingContext 处理上下文
     * @return 超出天数
     */
    private long calculateFenceExceedDays(VehicleProcessingContext processingContext) {
        VehicleGPSData gpsData = processingContext.getGpsData();
        String vehicleVin = processingContext.getVehicleFileInfo().getVin();

        // 先计算GPS时间差（与原始逻辑保持一致）
        long dayNum = DateUtil.daysBetween(new Date(), new Date(gpsData.getGpsDateTime()));
        log.info("{} dayNum: {}", vehicleVin, dayNum);

        // 检查是否超出围栏（与原始逻辑保持一致：不相等表示超出围栏）
        if (!gpsData.getCityName().equals(gpsData.getPenCityScope())) {
            // 超出围栏时使用Redis计数器
            String redisKey = RealFields.VEHICLE_ELECTRON_KEY + vehicleVin;
            dayNum = Global.instance.redisUtil.incr(redisKey);
            log.info("{} 车辆超电子围栏: {} 天", vehicleVin, dayNum);
        }

        return dayNum;
    }

    /**
     * 判断车辆是否在电子围栏内
     *
     * @param gpsData GPS数据
     * @return true表示在围栏内
     */
    private boolean isVehicleWithinFence(VehicleGPSData gpsData) {
        return gpsData.getCityName().equals(gpsData.getPenCityScope());
    }

    /**
     * 处理新告警创建
     *
     * @param processingContext 处理上下文
     * @param fenceExceedDays 超出围栏天数
     */
    private void handleNewAlarmCreation(VehicleProcessingContext processingContext, long fenceExceedDays) {
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();
        VehicleGPSData gpsData = processingContext.getGpsData();
        Integer vehicleRiskStatus = processingContext.getCurrentRiskStatus();

        // 更新车辆风险状态
        if (vehicleRiskStatus < VehicleRiskStatusEnum.RISK_VEHICLE.getCode()) {
            subAssetsService.updateVehicleRiskStatusByVin(vehicle.getVin(), VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
        }

        // 计算告警级别
        AlarmLevel alarmLevel = calculateAlarmLevel(vehicle, gpsData, fenceExceedDays);
        if (alarmLevel == AlarmLevel.NONE) {
            return;
        }

        // 创建告警
        int vehicleRiskStatusNew = alarmLevel == AlarmLevel.LEVEL_ONE ?
            VehicleRiskStatusEnum.RISK_VEHICLE.getCode() :
            VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();

        riskCommonService.addRiskAlarm(
            vehicle,
            gpsData,
            alarmLevel.getCode(),
            AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode(),
            vehicleRiskStatusNew
        );
    }

    /**
     * 处理现有告警更新
     *
     * @param processingContext 处理上下文
     * @param fenceExceedDays 超出围栏天数
     */
    private void handleExistingAlarmUpdate(VehicleProcessingContext processingContext, long fenceExceedDays) {
        RiskAlarm riskAlarm = processingContext.getExistingAlarm();

        // 检查暂停告警逻辑
        if (shouldResumeFromPause(processingContext)) {
            resumeAlarmFromPause(processingContext);
            return;
        }

        // 如果告警已恢复，不处理
        if (isAlarmRecovered(riskAlarm)) {
            return;
        }

        // 检查是否需要恢复告警
        if (shouldRecoverAlarm(processingContext)) {
            recoverAlarm(processingContext);
            return;
        }

        // 检查是否需要升级告警
        if (shouldUpgradeAlarm(processingContext, fenceExceedDays)) {
            upgradeAlarm(processingContext);
        }
    }

    /**
     * 计算告警级别
     *
     * 根据车辆产品线和超出时间计算告警级别
     *
     * @param vehicle 车辆信息
     * @param gpsData GPS数据
     * @param fenceExceedDays 超出围栏天数
     * @return 告警级别
     */
    private AlarmLevel calculateAlarmLevel(SearchVehicleFileList vehicle, VehicleGPSData gpsData, long fenceExceedDays) {
        if (vehicle.getProductLine() == ProductLineCode.LONG_TERM) {
            return calculateLongTermAlarmLevel(fenceExceedDays);
        } else if (vehicle.getProductLine() == ProductLineCode.SHORT_TERM) {
            return calculateShortTermAlarmLevel(fenceExceedDays, gpsData);
        }

        return AlarmLevel.NONE;
    }

    /**
     * 长租告警级别计算
     *
     * @param days 超出天数
     * @return 告警级别
     */
    private AlarmLevel calculateLongTermAlarmLevel(long days) {
        if (days >= AlarmThresholdDays.LONG_TERM_LEVEL_ONE && days < AlarmThresholdDays.LONG_TERM_LEVEL_TWO) {
            return AlarmLevel.LEVEL_ONE;
        } else if (days >= AlarmThresholdDays.LONG_TERM_LEVEL_TWO) {
            return AlarmLevel.LEVEL_TWO;
        }
        return AlarmLevel.NONE;
    }

    /**
     * 短租告警级别计算
     *
     * 短租逻辑（与原始代码保持一致）：
     * - 一级告警：0天到(3+7=10)天
     * - 二级告警：10天以上
     *
     * 注意：原始代码中短租没有30分钟的额外判断，直接基于天数
     *
     * @param days 超出天数
     * @param gpsData GPS数据（预留，原始逻辑中短租不使用分钟判断）
     * @return 告警级别
     */
    private AlarmLevel calculateShortTermAlarmLevel(long days, VehicleGPSData gpsData) {
        // 短租逻辑：0天到(3+7=10)天为一级告警，10天以上为二级告警
        // 与原始代码保持完全一致：dayNum >= 0 && dayNum < DAYS_WARNING_3 + DAYS_WARNING_7
        if (days >= 0 && days < (AlarmThresholdDays.LONG_TERM_LEVEL_ONE + AlarmThresholdDays.LONG_TERM_LEVEL_TWO)) {
            return AlarmLevel.LEVEL_ONE;
        } else if (days >= (AlarmThresholdDays.LONG_TERM_LEVEL_ONE + AlarmThresholdDays.LONG_TERM_LEVEL_TWO)) {
            return AlarmLevel.LEVEL_TWO;
        }
        return AlarmLevel.NONE;
    }

    /**
     * 检查是否应该从暂停状态恢复
     *
     * @param processingContext 处理上下文
     * @return true表示应该恢复
     */
    private boolean shouldResumeFromPause(VehicleProcessingContext processingContext) {
        RiskAlarm riskAlarm = processingContext.getExistingAlarm();
        VehicleGPSData gpsData = processingContext.getGpsData();

        return riskAlarm.getAlarmStatus() != AlarmStatusEnum.ALARM_STATUS.getCode() &&
               gpsData.getPauseDeadlineTime() != null &&
               gpsData.getPauseDeadlineTime().before(new Date());
    }

    /**
     * 检查告警是否已恢复
     *
     * @param riskAlarm 风险告警
     * @return true表示已恢复
     */
    private boolean isAlarmRecovered(RiskAlarm riskAlarm) {
        return riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode());
    }

    /**
     * 检查是否应该恢复告警
     *
     * 恢复条件（与原始代码保持一致）：
     * 1. 车辆回到电子围栏内（PenCityScope包含CityName）
     * 2. 或者车辆产品线是车管中心（产品线=1）
     *
     * @param processingContext 处理上下文
     * @return true表示应该恢复
     */
    private boolean shouldRecoverAlarm(VehicleProcessingContext processingContext) {
        VehicleGPSData gpsData = processingContext.getGpsData();
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();

        // 与原始代码保持一致：vehicleGPSData.getPenCityScope().contains(vehicleGPSData.getCityName()) || vehicle.getProductLine() == 1
        return gpsData.getPenCityScope().contains(gpsData.getCityName()) ||
               vehicle.getProductLine() == ProductLineCode.VEHICLE_MANAGEMENT_CENTER;
    }

    // ==================== 内部类和枚举定义 ====================

    /**
     * 车辆处理异常
     */
    private static class VehicleProcessingException extends Exception {
        private final String vin;
        private final String errorCode;

        public VehicleProcessingException(String vin, String errorCode, String message, Throwable cause) {
            super(message, cause);
            this.vin = vin;
            this.errorCode = errorCode;
        }

        public String getVin() {
            return vin;
        }

        public String getErrorCode() {
            return errorCode;
        }
    }

    /**
     * 告警级别枚举
     */
    private enum AlarmLevel {
        NONE(0),
        LEVEL_ONE(1),
        LEVEL_TWO(2);

        private final int code;

        AlarmLevel(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }
    }

    /**
     * 错误处理策略枚举
     */
    private enum ErrorHandlingStrategy {
        RETRY,      // 重试
        SKIP,       // 跳过
        FAIL_FAST   // 快速失败
    }

    /**
     * 任务执行统计信息
     */
    private static class TaskExecutionStatistics {
        private int totalProcessedCount = 0;
        private int totalSuccessfulCount = 0;
        private int totalFailedCount = 0;
        private int totalSkippedCount = 0;

        public void addBatchResult(BatchProcessingResult batchResult) {
            this.totalProcessedCount += batchResult.getProcessedCount();
            this.totalSuccessfulCount += batchResult.getSuccessfulCount();
            this.totalFailedCount += batchResult.getFailedCount();
            this.totalSkippedCount += batchResult.getSkippedCount();
        }

        public double getFailureRate() {
            return totalProcessedCount > 0 ? (double) totalFailedCount / totalProcessedCount : 0;
        }

        public int getTotalProcessedCount() {
            return totalProcessedCount;
        }

        public int getTotalSuccessfulCount() {
            return totalSuccessfulCount;
        }

        public int getTotalFailedCount() {
            return totalFailedCount;
        }

        public int getTotalSkippedCount() {
            return totalSkippedCount;
        }
    }

    /**
     * 批次处理结果
     */
    private static class BatchProcessingResult {
        private final int processedCount;
        private final int successfulCount;
        private final int failedCount;
        private final int skippedCount;

        public BatchProcessingResult(int processedCount, int successfulCount,
                                   int failedCount, int skippedCount) {
            this.processedCount = processedCount;
            this.successfulCount = successfulCount;
            this.failedCount = failedCount;
            this.skippedCount = skippedCount;
        }

        public int getProcessedCount() {
            return processedCount;
        }

        public int getSuccessfulCount() {
            return successfulCount;
        }

        public int getFailedCount() {
            return failedCount;
        }

        public int getSkippedCount() {
            return skippedCount;
        }
    }

    /**
     * 车辆处理结果
     */
    private static class VehicleProcessingResult {
        private final String vehicleVin;
        private final boolean successful;
        private final boolean skipped;
        private final String message;

        private VehicleProcessingResult(String vehicleVin, boolean successful,
                                      boolean skipped, String message) {
            this.vehicleVin = vehicleVin;
            this.successful = successful;
            this.skipped = skipped;
            this.message = message;
        }

        public static VehicleProcessingResult successful(String vehicleVin) {
            return new VehicleProcessingResult(vehicleVin, true, false, "处理成功");
        }

        public static VehicleProcessingResult skipped(String vehicleVin, String reason) {
            return new VehicleProcessingResult(vehicleVin, false, true, reason);
        }

        public static VehicleProcessingResult failed(String vehicleVin, String reason) {
            return new VehicleProcessingResult(vehicleVin, false, false, reason);
        }

        public String getVehicleVin() {
            return vehicleVin;
        }

        public boolean isSuccessful() {
            return successful;
        }

        public boolean isSkipped() {
            return skipped;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 车辆处理上下文
     */
    private static class VehicleProcessingContext {
        private final SearchVehicleFileList vehicleFileInfo;
        private final VehicleGPSData gpsData;
        private final RiskAlarm existingAlarm;
        private final Integer currentRiskStatus;

        public VehicleProcessingContext(SearchVehicleFileList vehicleFileInfo,
                                      VehicleGPSData gpsData,
                                      RiskAlarm existingAlarm,
                                      Integer currentRiskStatus) {
            this.vehicleFileInfo = vehicleFileInfo;
            this.gpsData = gpsData;
            this.existingAlarm = existingAlarm;
            this.currentRiskStatus = currentRiskStatus;
        }

        public SearchVehicleFileList getVehicleFileInfo() {
            return vehicleFileInfo;
        }

        public VehicleGPSData getGpsData() {
            return gpsData;
        }

        public RiskAlarm getExistingAlarm() {
            return existingAlarm;
        }

        public Integer getCurrentRiskStatus() {
            return currentRiskStatus;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 确定错误处理策略
     *
     * @param e 车辆处理异常
     * @return 错误处理策略
     */
    private ErrorHandlingStrategy determineErrorHandlingStrategy(VehicleProcessingException e) {
        String errorCode = e.getErrorCode();

        switch (errorCode) {
            case ErrorCode.NETWORK_TIMEOUT:
            case ErrorCode.DATABASE_CONNECTION_ERROR:
            case ErrorCode.REDIS_CONNECTION_ERROR:
                return ErrorHandlingStrategy.RETRY;

            case ErrorCode.INVALID_VEHICLE_DATA:
            case ErrorCode.VEHICLE_NOT_FOUND:
            case ErrorCode.GPS_DATA_INVALID:
                return ErrorHandlingStrategy.SKIP;

            case ErrorCode.CRITICAL_SYSTEM_ERROR:
                return ErrorHandlingStrategy.FAIL_FAST;

            default:
                return ErrorHandlingStrategy.RETRY;
        }
    }

    /**
     * 处理车辆处理异常
     *
     * @param e 异常
     * @param strategy 处理策略
     * @param retryCount 重试次数
     * @return true表示不需要重试
     */
    private boolean handleVehicleProcessingException(VehicleProcessingException e,
                                                   ErrorHandlingStrategy strategy,
                                                   int retryCount) {
        String vin = e.getVin();
        String errorCode = e.getErrorCode();

        switch (strategy) {
            case RETRY:
                if (retryCount >= MAX_RETRY_ATTEMPTS) {
                    log.error("车辆处理重试失败: {}, 错误代码: {}, 已达到最大重试次数",
                             vin, errorCode, e);
                    recordProcessingFailure(vin, errorCode, e.getMessage());
                    return true;
                } else {
                    log.warn("车辆处理失败，准备重试: {}, 错误代码: {}, 重试次数: {}",
                            vin, errorCode, retryCount + 1, e);
                    return false;
                }

            case SKIP:
                log.warn("跳过车辆处理: {}, 错误代码: {}, 原因: {}",
                        vin, errorCode, e.getMessage(), e);
                recordProcessingFailure(vin, errorCode, e.getMessage());
                return true;

            case FAIL_FAST:
                log.error("车辆处理遇到严重错误，停止处理: {}, 错误代码: {}",
                         vin, errorCode, e);
                throw new RuntimeException("Critical error processing vehicle: " + vin, e);

            default:
                return false;
        }
    }

    /**
     * 记录处理失败
     *
     * @param vin 车架号
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    private void recordProcessingFailure(String vin, String errorCode, String errorMessage) {
        try {
            log.info("记录车辆处理失败: {}, 错误代码: {}, 错误信息: {}", vin, errorCode, errorMessage);
            // 这里可以添加失败记录到数据库的逻辑
        } catch (Exception e) {
            log.error("记录处理失败时发生异常: {}", vin, e);
        }
    }

    /**
     * 等待重试
     *
     * @param retryAttemptCount 重试次数
     */
    private void waitForRetry(int retryAttemptCount) {
        try {
            long delayMs = RETRY_BASE_DELAY_MS * retryAttemptCount; // 线性退避
            Thread.sleep(delayMs);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("重试等待被中断");
        }
    }

    /**
     * 记录任务完成情况
     *
     * @param statistics 统计信息
     * @param taskStartTime 任务开始时间
     */
    private void logTaskCompletion(TaskExecutionStatistics statistics, long taskStartTime) {
        long taskDuration = System.currentTimeMillis() - taskStartTime;
        double throughput = statistics.getTotalProcessedCount() > 0 ?
            (double) statistics.getTotalProcessedCount() / taskDuration * 1000 : 0;

        log.info("车辆电子围栏预警任务执行完成 - " +
                "总处理: {}, 成功: {}, 失败: {}, 跳过: {}, " +
                "耗时: {}ms, 吞吐量: {:.2f} vehicles/sec",
                statistics.getTotalProcessedCount(),
                statistics.getTotalSuccessfulCount(),
                statistics.getTotalFailedCount(),
                statistics.getTotalSkippedCount(),
                taskDuration,
                throughput);
    }

    /**
     * 确定任务执行结果
     *
     * @param statistics 统计信息
     * @return 任务结果
     */
    private ReturnT<String> determineTaskResult(TaskExecutionStatistics statistics) {
        // 如果失败率超过阈值，认为任务失败
        if (statistics.getFailureRate() > FAILURE_RATE_THRESHOLD) {
            log.error("任务失败率过高: {:.2f}%, 总处理: {}, 失败: {}",
                     statistics.getFailureRate() * 100,
                     statistics.getTotalProcessedCount(),
                     statistics.getTotalFailedCount());
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 记录批次性能指标
     *
     * @param vehicleCount 车辆数量
     * @param batchStartTime 批次开始时间
     */
    private void logBatchPerformance(int vehicleCount, long batchStartTime) {
        long batchDuration = System.currentTimeMillis() - batchStartTime;
        double batchThroughput = vehicleCount > 0 ? (double) vehicleCount / batchDuration * 1000 : 0;

        log.info("批次处理完成 - 车辆数: {}, 耗时: {}ms, 吞吐量: {:.2f} vehicles/sec",
                vehicleCount, batchDuration, batchThroughput);
    }

    /**
     * 从暂停状态恢复告警
     *
     * 业务逻辑：
     * 1. 更新原告警的暂停状态
     * 2. 记录暂停恢复日志
     * 3. 根据当前GPS时间重新计算告警级别
     * 4. 创建新的告警记录
     * 5. 更新车辆风险状态
     * 6. 记录新告警创建日志
     *
     * @param processingContext 处理上下文
     */
    private void resumeAlarmFromPause(VehicleProcessingContext processingContext) {
        RiskAlarm originalAlarm = processingContext.getExistingAlarm();
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();
        VehicleGPSData gpsData = processingContext.getGpsData();
        Integer currentVehicleRiskStatus = processingContext.getCurrentRiskStatus();
        String vin = vehicle.getVin();

        log.info("开始处理暂停告警恢复逻辑: {}", vin);

        try {
            // 1. 更新原告警的暂停状态
            updateOriginalAlarmStopStatus(originalAlarm);

            // 2. 记录暂停恢复日志
            saveLog(originalAlarm.getAlarmNo(),
                   String.format("暂停后恢复风控报警【%s】",
                   AlarmTypeEnum.getValueByCode(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())));

            // 3. 重新计算告警级别和车辆风险状态
            AlarmLevelAndRiskStatus alarmResult = calculateAlarmLevelForResumed(vehicle, gpsData, currentVehicleRiskStatus);

            // 如果不满足告警条件，直接返回
            if (alarmResult.getAlarmLevel() == AlarmLevel.NONE) {
                log.info("暂停恢复后不满足告警条件，跳过创建新告警: {}", vin);
                return;
            }

            // 4. 创建新的告警记录
            createNewAlarmFromResumed(originalAlarm, vehicle, gpsData, alarmResult.getAlarmLevel());

            // 5. 更新车辆风险状态（如果需要）
            if (alarmResult.getNewVehicleRiskStatus() > currentVehicleRiskStatus) {
                subAssetsService.updateVehicleRiskStatusByVin(vin, alarmResult.getNewVehicleRiskStatus());
                log.info("更新车辆风险状态: {} -> {}", currentVehicleRiskStatus, alarmResult.getNewVehicleRiskStatus());
            }

            log.info("暂停告警恢复处理完成: {}", vin);

        } catch (Exception e) {
            log.error("处理暂停告警恢复失败: {}", vin, e);
            throw new RuntimeException("暂停告警恢复处理失败", e);
        }
    }

    /**
     * 更新原告警的暂停状态
     *
     * @param originalAlarm 原告警记录
     */
    private void updateOriginalAlarmStopStatus(RiskAlarm originalAlarm) {
        try {
            RiskAlarm updateAlarm = new RiskAlarm();
            updateAlarm.setId(originalAlarm.getId());
            updateAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
            tableRiskAlarmService.update(updateAlarm);

            log.debug("更新原告警暂停状态成功: {}", originalAlarm.getAlarmNo());
        } catch (Exception e) {
            log.error("更新原告警暂停状态失败: {}", originalAlarm.getAlarmNo(), e);
            throw new RuntimeException("更新原告警暂停状态失败", e);
        }
    }

    /**
     * 为恢复的告警重新计算告警级别和车辆风险状态
     *
     * @param vehicle 车辆信息
     * @param gpsData GPS数据
     * @param currentVehicleRiskStatus 当前车辆风险状态
     * @return 告警级别和风险状态结果
     */
    private AlarmLevelAndRiskStatus calculateAlarmLevelForResumed(SearchVehicleFileList vehicle,
                                                                VehicleGPSData gpsData,
                                                                Integer currentVehicleRiskStatus) {
        // 计算GPS时间差
        Date currentTime = new Date();
        Date gpsTime = new Date(gpsData.getGpsDateTime());

        long daysBetween = DateUtil.daysBetween(currentTime, gpsTime);
        long minutesBetween = DateUtil.minutesBetween(gpsTime, currentTime);

        log.debug("GPS时间差计算 - 天数: {}, 分钟数: {}", daysBetween, minutesBetween);

        AlarmLevel alarmLevel = AlarmLevel.NONE;
        Integer newVehicleRiskStatus = currentVehicleRiskStatus;

        // 根据产品线和时间差计算告警级别
        if (vehicle.getProductLine() == ProductLineCode.LONG_TERM) {
            // 长租逻辑：3天一级告警，7天二级告警
            if (daysBetween >= AlarmThresholdDays.LONG_TERM_LEVEL_ONE &&
                daysBetween < AlarmThresholdDays.LONG_TERM_LEVEL_TWO) {
                alarmLevel = AlarmLevel.LEVEL_ONE;
                if (currentVehicleRiskStatus < VehicleRiskStatusEnum.RISK_VEHICLE.getCode()) {
                    newVehicleRiskStatus = VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                }
            } else if (daysBetween >= AlarmThresholdDays.LONG_TERM_LEVEL_TWO) {
                alarmLevel = AlarmLevel.LEVEL_TWO;
                if (currentVehicleRiskStatus < VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()) {
                    newVehicleRiskStatus = VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
                }
            }
        } else if (vehicle.getProductLine() == ProductLineCode.SHORT_TERM) {
            // 短租逻辑：30分钟+天数条件一级告警，10天二级告警
            if (minutesBetween >= AlarmThresholdDays.SHORT_TERM_MINUTE_WARNING &&
                daysBetween < (AlarmThresholdDays.LONG_TERM_LEVEL_ONE + AlarmThresholdDays.LONG_TERM_LEVEL_TWO)) {
                alarmLevel = AlarmLevel.LEVEL_ONE;
                if (currentVehicleRiskStatus < VehicleRiskStatusEnum.RISK_VEHICLE.getCode()) {
                    newVehicleRiskStatus = VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                }
            } else if (daysBetween >= (AlarmThresholdDays.LONG_TERM_LEVEL_ONE + AlarmThresholdDays.LONG_TERM_LEVEL_TWO)) {
                alarmLevel = AlarmLevel.LEVEL_TWO;
                if (currentVehicleRiskStatus < VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()) {
                    newVehicleRiskStatus = VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
                }
            }
        }

        return new AlarmLevelAndRiskStatus(alarmLevel, newVehicleRiskStatus);
    }

    /**
     * 基于原告警创建新的告警记录
     *
     * @param originalAlarm 原告警记录
     * @param vehicle 车辆信息
     * @param gpsData GPS数据
     * @param alarmLevel 新的告警级别
     */
    private void createNewAlarmFromResumed(RiskAlarm originalAlarm,
                                         SearchVehicleFileList vehicle,
                                         VehicleGPSData gpsData,
                                         AlarmLevel alarmLevel) {
        try {
            Date currentTime = new Date();

            // 基于原告警创建新告警，重置关键字段
            RiskAlarm newAlarm = new RiskAlarm();

            // 复制原告警的基本信息
            newAlarm.setVin(originalAlarm.getVin());
            newAlarm.setAlarmType(originalAlarm.getAlarmType());

            // 设置新的告警信息
            newAlarm.setId(null); // 新记录，ID为空
            newAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ")); // 生成新的告警编号
            newAlarm.setAlarmLevel(alarmLevel.getCode());
            newAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
            newAlarm.setAlarmTime(currentTime);
            newAlarm.setCreateTime(currentTime);
            newAlarm.setCreateOperName("定时任务");
            newAlarm.setCreateOperAccount("");
            newAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
            newAlarm.setRecoverChannel(0);

            // 清空恢复相关字段
            newAlarm.setRecoveryDate(null);
            newAlarm.setRecoverMethod(null);
            newAlarm.setStopAlarmDay(null);
            newAlarm.setPauseDeadlineTime(null);

            // 设置GPS位置信息
            if (gpsData != null) {
                if (gpsData.getOldGpsDateTime() != null) {
                    newAlarm.setLastLocationTime(new Date(gpsData.getOldGpsDateTime()));
                }
                newAlarm.setLastLocation(gpsData.getLastAddress());
            }

            // 保存新告警记录
            tableRiskAlarmService.save(newAlarm);

            // 记录新告警创建日志
            saveLog(newAlarm.getAlarmNo(),
                   String.format("添加风控类型【%s】",
                   AlarmTypeEnum.getValueByCode(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())));

            log.info("创建新告警记录成功 - 原告警: {}, 新告警: {}, 级别: {}",
                    originalAlarm.getAlarmNo(), newAlarm.getAlarmNo(), alarmLevel.getCode());

        } catch (Exception e) {
            log.error("创建新告警记录失败 - 原告警: {}", originalAlarm.getAlarmNo(), e);
            throw new RuntimeException("创建新告警记录失败", e);
        }
    }

    /**
     * 告警级别和风险状态结果类
     */
    private static class AlarmLevelAndRiskStatus {
        private final AlarmLevel alarmLevel;
        private final Integer newVehicleRiskStatus;

        public AlarmLevelAndRiskStatus(AlarmLevel alarmLevel, Integer newVehicleRiskStatus) {
            this.alarmLevel = alarmLevel;
            this.newVehicleRiskStatus = newVehicleRiskStatus;
        }

        public AlarmLevel getAlarmLevel() {
            return alarmLevel;
        }

        public Integer getNewVehicleRiskStatus() {
            return newVehicleRiskStatus;
        }
    }

    /**
     * 恢复告警
     *
     * @param processingContext 处理上下文
     */
    private void recoverAlarm(VehicleProcessingContext processingContext) {
        RiskAlarm riskAlarm = processingContext.getExistingAlarm();
        VehicleGPSData gpsData = processingContext.getGpsData();
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();

        // 设置恢复状态
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());

        // 更新告警
        riskCommonService.updateRiskAlarm(false, riskAlarm, gpsData, vehicle.getProductLine());

        // 清理Redis数据
        cleanupRedisData(vehicle.getVin());

        // 通知长租
        notifyLongRentIfNeeded(vehicle, riskAlarm);

        log.info("告警已恢复: {}", vehicle.getVin());
    }

    /**
     * 检查是否需要升级告警
     *
     * @param processingContext 处理上下文
     * @param fenceExceedDays 超出围栏天数
     * @return true表示需要升级
     */
    private boolean shouldUpgradeAlarm(VehicleProcessingContext processingContext, long fenceExceedDays) {
        RiskAlarm riskAlarm = processingContext.getExistingAlarm();
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();

        long upgradeDayNum = DateUtil.daysBetween(new Date(), riskAlarm.getAlarmTime());

        return (vehicle.getProductLine() == ProductLineCode.LONG_TERM && upgradeDayNum >= AlarmThresholdDays.ALARM_UPGRADE_THRESHOLD) ||
               (vehicle.getProductLine() == ProductLineCode.SHORT_TERM && upgradeDayNum >= AlarmThresholdDays.ALARM_UPGRADE_THRESHOLD);
    }

    /**
     * 升级告警
     *
     * @param processingContext 处理上下文
     */
    private void upgradeAlarm(VehicleProcessingContext processingContext) {
        RiskAlarm riskAlarm = processingContext.getExistingAlarm();
        VehicleGPSData gpsData = processingContext.getGpsData();
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();

        // 升级告警
        riskCommonService.updateRiskAlarm(true, riskAlarm, gpsData, vehicle.getProductLine());

        // 处理长租推送
        handleLongRentPush(processingContext);

        log.info("告警已升级: {}", vehicle.getVin());
    }

    /**
     * 清理Redis数据
     *
     * @param vin 车架号
     */
    private void cleanupRedisData(String vin) {
        try {
            Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + vin);
            log.debug("清理Redis数据成功: {}", vin);
        } catch (Exception e) {
            log.error("清理Redis数据失败: {}", vin, e);
        }
    }

    /**
     * 通知长租（如果需要）
     *
     * @param vehicle 车辆信息
     * @param riskAlarm 风险告警
     */
    private void notifyLongRentIfNeeded(SearchVehicleFileList vehicle, RiskAlarm riskAlarm) {
        try {
            if (vehicle.getProductLine() == ProductLineCode.LONG_TERM ||
                vehicle.getProductLine() == ProductLineCode.VEHICLE_MANAGEMENT_CENTER) {
                subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
                log.info("已通知长租系统: {}", vehicle.getVin());
            }
        } catch (Exception e) {
            log.error("通知长租系统失败: {}", vehicle.getVin(), e);
        }
    }

    /**
     * 处理长租推送
     *
     * @param processingContext 处理上下文
     */
    private void handleLongRentPush(VehicleProcessingContext processingContext) {
        RiskAlarm riskAlarm = processingContext.getExistingAlarm();
        SearchVehicleFileList vehicle = processingContext.getVehicleFileInfo();
        Integer alarmLevel = riskAlarm.getAlarmLevel();

        try {
            // 查看有没有推过长租
            RiskPushRecord pushRecord = tableRiskPushRecordService.query(riskAlarm.getAlarmNo());
            if (pushRecord != null) {
                return;
            }

            if (vehicle.getProductLine() == ProductLineCode.LONG_TERM &&
                alarmLevel.equals(AlarmLevenEnum.TWO.getCode())) {

                // 查询长租订单合同信息
                QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes =
                    mdDataProxy.queryOperateContractInfoByVins(
                        QueryOperateContractInfoByVinsReq.newBuilder()
                            .addVinList(vehicle.getVin())
                            .build()
                    );

                if (queryOperateContractInfoByVinsRes != null &&
                    queryOperateContractInfoByVinsRes.getRetCode() == 0 &&
                    CollectionUtil.isNotEmpty(queryOperateContractInfoByVinsRes.getInfoList())) {

                    OperateContractInfo operateContractInfo = queryOperateContractInfoByVinsRes.getInfoList().get(0);
                    String orderNo = operateContractInfo.getOrderNo();

                    log.info("电子围栏推送长租 - 订单号：{}，车架号：{}, 报警等级：{}",
                            orderNo, vehicle.getVin(), alarmLevel);

                    RiskUtils.notifyRiskWorkOrderToLongRent(
                        orderNo, vehicle.getPlateNo(), alarmLevel,
                        riskAlarm.getAlarmNo(), riskAlarm.getAlarmType(), 1
                    );

                    // 记录推送标识
                    tableRiskPushRecordService.insert(riskAlarm.getAlarmNo());

                    // 更新车辆报警等级
                    Integer vehicleRiskStatus = processingContext.getCurrentRiskStatus();
                    subAssetsService.updateVehicleRiskStatusByVin(
                        vehicle.getVin(),
                        vehicleRiskStatus > alarmLevel + 1 ? vehicleRiskStatus : alarmLevel + 1
                    );
                }
            }
        } catch (Exception e) {
            log.error("处理长租推送失败: {}", vehicle.getVin(), e);
        }
    }

    /**
     * 记录操作日志
     *
     * @param relationKey 关联键
     * @param operateContent 操作内容
     */
    private void saveLog(String relationKey, String operateContent) {
        try {
            OperateLog operateLog = new OperateLog();
            operateLog.setForeignId(relationKey);
            operateLog.setOperateType(OperateTypeEnum.OPERATE_TYPE.getCode());
            operateLog.setOperateContent(operateContent);
            operateLog.setCreateOperName("定时任务");
            operateLog.setCreateOperAccount("System");
            tableOperateLogService.save(operateLog);
        } catch (Exception e) {
            log.error("保存操作日志失败: {}", relationKey, e);
        }
    }
}
