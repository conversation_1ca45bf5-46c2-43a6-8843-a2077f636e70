package com.saicmobility.evcard.vlms.risk.utils;

import cn.hutool.core.util.ObjectUtil;
import com.saicmobility.common.envconfig.EnvGlobal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class OutDescIdUtil {

    private static final Map<String, AtomicLong> atomicLongMap = new ConcurrentHashMap<>();

    @Autowired
    private RedisTemplate redisTemplate;

    public String nextId(String outDescType, boolean isRedis) {
        String outDescId = null;
        if (isRedis) {
            Long id = getNextId(outDescType);
            outDescId = new StringBuffer()
                    .append(outDescType)
                    .append(new SimpleDateFormat("yy", Locale.CHINESE).format(new Date()))
                    .append(new SimpleDateFormat("MMddHHmmss").format(new Date()))
                    .append(String.format("%03d", id % 1000)).toString();
        } else {
            AtomicLong atomicLong = atomicLongMap.get(outDescType);
            if (ObjectUtil.isNull(atomicLong)) {
                synchronized (this) {
                    atomicLong = atomicLongMap.get(outDescType);
                    if (ObjectUtil.isNull(atomicLong)) {
                        AtomicLong atomic = new AtomicLong(0);
                        atomicLongMap.put(outDescType, atomic);
                        outDescId = new StringBuffer()
                                .append(outDescType)
                                .append(new SimpleDateFormat("yy", Locale.CHINESE).format(new Date()))
                                .append(new SimpleDateFormat("MMddHHmmss").format(new Date()))
                                .append(String.format("%04d", atomic.incrementAndGet() % 10000)).toString();
                    }
                }
            } else {
                outDescId = new StringBuffer()
                        .append(outDescType)
                        .append(new SimpleDateFormat("yy", Locale.CHINESE).format(new Date()))
                        .append(new SimpleDateFormat("MMddHHmmss").format(new Date()))
                        .append(String.format("%04d", atomicLong.incrementAndGet() % 10000)).toString();
            }
        }
        return outDescId;
    }

    public String nextId(String outDescType) {
        return this.nextId(outDescType, true);
    }

    /**
     * 根据key获得唯一id
     *
     * @param key
     * @return
     */
    private Long getNextId(String key) {
        String fKey = prefix("outId_" + key);
        Long id = redisTemplate.opsForValue().increment(fKey);
        if (id <= 1L || id >= 1000) {
            if (!redisTemplate.expire(fKey, 1, TimeUnit.HOURS)) {
                return 0L;
            }
        }
        return id;
    }

    // 处理 redis key
    public static String prefix(String key) {
        if (key.isEmpty()) {
            return key;
        } else {
            return EnvGlobal.globalServiceId + "_" + key;
        }
    }
}
