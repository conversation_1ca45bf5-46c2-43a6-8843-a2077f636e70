package com.saicmobility.evcard.vlms.risk.mapper.extend.assets;

import com.saicmobility.evcard.vlms.risk.model.extend.VehicleLicenseInfo;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

import javax.annotation.Generated;

import java.util.List;
import java.util.Optional;

import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.VehicleLicenseInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface VehicleLicenseInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper , CommonInsertMapper<VehicleLicenseInfo> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, plateNo, usageProperty, registerDate, certificateDate, plateDate, inspectionExpireDate, drivingLicenseFrontPageUrl, drivingLicenseAttachedPageUrl, isDeleted, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName, systemUpdateTime, systemSyncCode);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleLicenseInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleLicenseInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="plate_no", property="plateNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_property", property="usageProperty", jdbcType=JdbcType.INTEGER),
        @Result(column="register_date", property="registerDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="certificate_date", property="certificateDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="plate_date", property="plateDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="inspection_expire_date", property="inspectionExpireDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="driving_license_front_page_url", property="drivingLicenseFrontPageUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="driving_license_attached_page_url", property="drivingLicenseAttachedPageUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="system_update_time", property="systemUpdateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="system_sync_code", property="systemSyncCode", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleLicenseInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleLicenseInfoResult")
    Optional<VehicleLicenseInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleLicenseInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleLicenseInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int insert(VehicleLicenseInfo row) {
        return MyBatis3Utils.insert(this::insert, row, vehicleLicenseInfo, c ->
            c.map(vin).toProperty("vin")
            .map(plateNo).toProperty("plateNo")
            .map(usageProperty).toProperty("usageProperty")
            .map(registerDate).toProperty("registerDate")
            .map(certificateDate).toProperty("certificateDate")
            .map(plateDate).toProperty("plateDate")
            .map(inspectionExpireDate).toProperty("inspectionExpireDate")
            .map(drivingLicenseFrontPageUrl).toProperty("drivingLicenseFrontPageUrl")
            .map(drivingLicenseAttachedPageUrl).toProperty("drivingLicenseAttachedPageUrl")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
            .map(systemUpdateTime).toProperty("systemUpdateTime")
            .map(systemSyncCode).toProperty("systemSyncCode")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int insertSelective(VehicleLicenseInfo row) {
        return MyBatis3Utils.insert(this::insert, row, vehicleLicenseInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(plateNo).toPropertyWhenPresent("plateNo", row::getPlateNo)
            .map(usageProperty).toPropertyWhenPresent("usageProperty", row::getUsageProperty)
            .map(registerDate).toPropertyWhenPresent("registerDate", row::getRegisterDate)
            .map(certificateDate).toPropertyWhenPresent("certificateDate", row::getCertificateDate)
            .map(plateDate).toPropertyWhenPresent("plateDate", row::getPlateDate)
            .map(inspectionExpireDate).toPropertyWhenPresent("inspectionExpireDate", row::getInspectionExpireDate)
            .map(drivingLicenseFrontPageUrl).toPropertyWhenPresent("drivingLicenseFrontPageUrl", row::getDrivingLicenseFrontPageUrl)
            .map(drivingLicenseAttachedPageUrl).toPropertyWhenPresent("drivingLicenseAttachedPageUrl", row::getDrivingLicenseAttachedPageUrl)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
            .map(systemUpdateTime).toPropertyWhenPresent("systemUpdateTime", row::getSystemUpdateTime)
            .map(systemSyncCode).toPropertyWhenPresent("systemSyncCode", row::getSystemSyncCode)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default Optional<VehicleLicenseInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleLicenseInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default List<VehicleLicenseInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleLicenseInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default List<VehicleLicenseInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleLicenseInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default Optional<VehicleLicenseInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleLicenseInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleLicenseInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(row::getVin)
                .set(plateNo).equalTo(row::getPlateNo)
                .set(usageProperty).equalTo(row::getUsageProperty)
                .set(registerDate).equalTo(row::getRegisterDate)
                .set(certificateDate).equalTo(row::getCertificateDate)
                .set(plateDate).equalTo(row::getPlateDate)
                .set(inspectionExpireDate).equalTo(row::getInspectionExpireDate)
                .set(drivingLicenseFrontPageUrl).equalTo(row::getDrivingLicenseFrontPageUrl)
                .set(drivingLicenseAttachedPageUrl).equalTo(row::getDrivingLicenseAttachedPageUrl)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName)
                .set(systemUpdateTime).equalTo(row::getSystemUpdateTime)
                .set(systemSyncCode).equalTo(row::getSystemSyncCode);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleLicenseInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(row::getVin)
                .set(plateNo).equalToWhenPresent(row::getPlateNo)
                .set(usageProperty).equalToWhenPresent(row::getUsageProperty)
                .set(registerDate).equalToWhenPresent(row::getRegisterDate)
                .set(certificateDate).equalToWhenPresent(row::getCertificateDate)
                .set(plateDate).equalToWhenPresent(row::getPlateDate)
                .set(inspectionExpireDate).equalToWhenPresent(row::getInspectionExpireDate)
                .set(drivingLicenseFrontPageUrl).equalToWhenPresent(row::getDrivingLicenseFrontPageUrl)
                .set(drivingLicenseAttachedPageUrl).equalToWhenPresent(row::getDrivingLicenseAttachedPageUrl)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
                .set(systemUpdateTime).equalToWhenPresent(row::getSystemUpdateTime)
                .set(systemSyncCode).equalToWhenPresent(row::getSystemSyncCode);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int updateByPrimaryKey(VehicleLicenseInfo row) {
        return update(c ->
            c.set(vin).equalTo(row::getVin)
            .set(plateNo).equalTo(row::getPlateNo)
            .set(usageProperty).equalTo(row::getUsageProperty)
            .set(registerDate).equalTo(row::getRegisterDate)
            .set(certificateDate).equalTo(row::getCertificateDate)
            .set(plateDate).equalTo(row::getPlateDate)
            .set(inspectionExpireDate).equalTo(row::getInspectionExpireDate)
            .set(drivingLicenseFrontPageUrl).equalTo(row::getDrivingLicenseFrontPageUrl)
            .set(drivingLicenseAttachedPageUrl).equalTo(row::getDrivingLicenseAttachedPageUrl)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .set(systemUpdateTime).equalTo(row::getSystemUpdateTime)
            .set(systemSyncCode).equalTo(row::getSystemSyncCode)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    default int updateByPrimaryKeySelective(VehicleLicenseInfo row) {
        return update(c ->
            c.set(vin).equalToWhenPresent(row::getVin)
            .set(plateNo).equalToWhenPresent(row::getPlateNo)
            .set(usageProperty).equalToWhenPresent(row::getUsageProperty)
            .set(registerDate).equalToWhenPresent(row::getRegisterDate)
            .set(certificateDate).equalToWhenPresent(row::getCertificateDate)
            .set(plateDate).equalToWhenPresent(row::getPlateDate)
            .set(inspectionExpireDate).equalToWhenPresent(row::getInspectionExpireDate)
            .set(drivingLicenseFrontPageUrl).equalToWhenPresent(row::getDrivingLicenseFrontPageUrl)
            .set(drivingLicenseAttachedPageUrl).equalToWhenPresent(row::getDrivingLicenseAttachedPageUrl)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .set(systemUpdateTime).equalToWhenPresent(row::getSystemUpdateTime)
            .set(systemSyncCode).equalToWhenPresent(row::getSystemSyncCode)
            .where(id, isEqualTo(row::getId))
        );
    }
}