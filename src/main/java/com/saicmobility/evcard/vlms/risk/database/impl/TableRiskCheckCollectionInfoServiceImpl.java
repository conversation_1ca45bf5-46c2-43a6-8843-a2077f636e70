package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckCollectionInfoService;
import com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCollectionInfoMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckCollectionInfo;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCollectionInfoDynamicSqlSupport.riskCheckCollectionInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
@Slf4j
public class TableRiskCheckCollectionInfoServiceImpl implements TableRiskCheckCollectionInfoService {

    @Resource
    private RiskCheckCollectionInfoMapper riskCheckCollectionInfoMapper;
    @Override
    public List<RiskCheckCollectionInfo> selectList(Long alarmCheckId) {
        if (alarmCheckId == 0){
            return null;
        }
        SelectStatementProvider render = select(riskCheckCollectionInfo.allColumns())
                .from(riskCheckCollectionInfo)
                .where()
                .and(riskCheckCollectionInfo.riskCheckId, isEqualTo(alarmCheckId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckCollectionInfoMapper.selectMany(render);
    }

    @Override
    public List<RiskCheckCollectionInfo> selectListByIds(List<Long> checkIds) {
        if (CollectionUtils.isEmpty(checkIds)){
            return null;
        }
        SelectStatementProvider render = select(riskCheckCollectionInfo.allColumns())
                .from(riskCheckCollectionInfo)
                .where()
                .and(riskCheckCollectionInfo.riskCheckId, isIn(checkIds))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckCollectionInfoMapper.selectMany(render);
    }

    @Override
    public Long save(Long riskCheckId, Integer collectCarType, String collectCarPeople, String miscDesc, CurrentUser currentUser) {
        RiskCheckCollectionInfo collectionInfo = new RiskCheckCollectionInfo();
        collectionInfo.setRiskCheckId(riskCheckId);
        collectionInfo.setCollectCarPeople(collectCarPeople);
        collectionInfo.setCollectCarType(collectCarType);
        collectionInfo.setMiscDesc(miscDesc);
        collectionInfo.setCreateOperAccount(currentUser.getUserAccount());
        collectionInfo.setCreateOperName(currentUser.getNickName());
        collectionInfo.setCreateTime(new Date());
        riskCheckCollectionInfoMapper.insertSelective(collectionInfo);
        return collectionInfo.getId();
    }
}
