package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum AttachmentTypeEnum {

    // 1-风控报警-解除 2-风控报警-升级 3-风控收车-其他附件 4-白名单配置
    //     *                 5-风控收车-验车单 6-风控收车-车辆清收音视频资料 7-风控收车-车内物品交接单 8-定位截图 9-水印照片 10 失控附件
    ALARM_RELEASE(1, "风控报警-解除"),
    ALARM_UPGRADE(2, "风控报警-升级"),
    VEHICLE_OTHER(3, "风控收车-其他附件"),
    VEHICLE_WHITE_LIST(4, "白名单配置"),
    VEHICLE_CHECK(5, "风控收车-验车单"),
    VEHICLE_CLEAR(6, "风控收车-车辆清收音视频资料"),
    VEHICLE_HANDOVER(7, "风控收车-车内物品交接单"),
    VEHICLE_LOCATION(8, "定位截图"),
    VEHICLE_WATERMARK(9, "水印照片"),
    VEHICLE_LOSS(10, "失控附件");

    private final Integer code;
    private final String value;


    public static String getValueByCode(Integer code){
        for (AttachmentTypeEnum item : AttachmentTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
