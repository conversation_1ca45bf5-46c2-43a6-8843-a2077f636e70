package com.saicmobility.evcard.vlms.risk.mapper.extend.assets;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import javax.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;

public final class VehicleLicenseInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    public static final VehicleLicenseInfo vehicleLicenseInfo = new VehicleLicenseInfo();

    /**
     * Database Column Remarks:
     *   自增主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.id")
    public static final SqlColumn<Long> id = vehicleLicenseInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.vin")
    public static final SqlColumn<String> vin = vehicleLicenseInfo.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_no")
    public static final SqlColumn<String> plateNo = vehicleLicenseInfo.plateNo;

    /**
     * Database Column Remarks:
     *   使用性质 0-租赁，1-非营运 2-预约出租客运 3-营转非 4-营运 5-预约出租转非
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.usage_property")
    public static final SqlColumn<Integer> usageProperty = vehicleLicenseInfo.usageProperty;

    /**
     * Database Column Remarks:
     *   注册日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.register_date")
    public static final SqlColumn<Date> registerDate = vehicleLicenseInfo.registerDate;

    /**
     * Database Column Remarks:
     *   发证日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.certificate_date")
    public static final SqlColumn<Date> certificateDate = vehicleLicenseInfo.certificateDate;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_date")
    public static final SqlColumn<Date> plateDate = vehicleLicenseInfo.plateDate;

    /**
     * Database Column Remarks:
     *   检测有效期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.inspection_expire_date")
    public static final SqlColumn<Date> inspectionExpireDate = vehicleLicenseInfo.inspectionExpireDate;

    /**
     * Database Column Remarks:
     *   行驶证正页地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_front_page_url")
    public static final SqlColumn<String> drivingLicenseFrontPageUrl = vehicleLicenseInfo.drivingLicenseFrontPageUrl;

    /**
     * Database Column Remarks:
     *   行驶证附页地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_attached_page_url")
    public static final SqlColumn<String> drivingLicenseAttachedPageUrl = vehicleLicenseInfo.drivingLicenseAttachedPageUrl;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleLicenseInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleLicenseInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_account")
    public static final SqlColumn<String> createOperAccount = vehicleLicenseInfo.createOperAccount;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleLicenseInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleLicenseInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = vehicleLicenseInfo.updateOperAccount;

    /**
     * Database Column Remarks:
     *   修改人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleLicenseInfo.updateOperName;

    /**
     * Database Column Remarks:
     *   系统同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_update_time")
    public static final SqlColumn<Date> systemUpdateTime = vehicleLicenseInfo.systemUpdateTime;

    /**
     * Database Column Remarks:
     *   系统同步编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_sync_code")
    public static final SqlColumn<String> systemSyncCode = vehicleLicenseInfo.systemSyncCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    public static final class VehicleLicenseInfo extends AliasableSqlTable<VehicleLicenseInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> plateNo = column("plate_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> usageProperty = column("usage_property", JDBCType.INTEGER);

        public final SqlColumn<Date> registerDate = column("register_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> certificateDate = column("certificate_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> plateDate = column("plate_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> inspectionExpireDate = column("inspection_expire_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> drivingLicenseFrontPageUrl = column("driving_license_front_page_url", JDBCType.VARCHAR);

        public final SqlColumn<String> drivingLicenseAttachedPageUrl = column("driving_license_attached_page_url", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> systemUpdateTime = column("system_update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> systemSyncCode = column("system_sync_code", JDBCType.VARCHAR);

        public VehicleLicenseInfo() {
            super("vlms_assets.t_vehicle_license_info", VehicleLicenseInfo::new);
        }
    }
}