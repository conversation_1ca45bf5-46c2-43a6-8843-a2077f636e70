package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface RiskAlarmConfigService {

    /**
     * 查询车辆风控报警配置
     * @param req
     * @return
     */
    public QueryRiskAlarmConfigRes queryRiskAlarmConfig(QueryRiskAlarmConfigReq req);

    /**
     * 导出
     * @param req
     * @return
     */
    public QueryRiskAlarmConfigRes exportRiskAlarmConfig(QueryRiskAlarmConfigReq req);

    /**
     * 编辑
     * @param req
     * @return
     */
    public UpdateRiskAlarmConfigRes updateRiskAlarmConfig(UpdateRiskAlarmConfigReq req);

    /**
     * 新增
     * @param riskAlarmConfig
     */
    public void insertRiskAlarmConfig(RiskAlarmConfig riskAlarmConfig);

    /**
     * 更细
     * @param vin
     * @param alarmType
     * @param alarmStartTime
     * @param pauseDay
     * @param pauseDeadlineTime
     * @param currentUser
     */
    Long updateRiskAlarmConfig(String vin, Integer alarmType, Date alarmStartTime, Integer pauseDay, Date pauseDeadlineTime, CurrentUser currentUser);

    /**
     * 新增（自测用）
     * @param addRiskAlarmConfiReq
     * @return
     */
    AddRiskAlarmConfiRes addRiskAlarmConfig(AddRiskAlarmConfiReq addRiskAlarmConfiReq);

    WangNengRiskAlarmRes wangNengRiskAlarm(WangNengRiskAlarmReq wangNengRiskAlarmReq);

    SyncRiskOrderRes syncRiskOrder(SyncRiskOrderReq syncRiskOrderReq);
}
