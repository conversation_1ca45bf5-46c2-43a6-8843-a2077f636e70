package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   风控报警表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_alarm
 */
public class RiskAlarm implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   风控报警编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_no")
    private String alarmNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   关联订单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_seq")
    private String orderSeq;

    /**
     * Database Column Remarks:
     *   订单类型（1、门店订单 2、渠道订单 3、长租订单 4、短租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_type")
    private Integer orderType;

    /**
     * Database Column Remarks:
     *   报警类型 1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失控
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_type")
    private Integer alarmType;

    /**
     * Database Column Remarks:
     *   报警等级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_level")
    private Integer alarmLevel;

    /**
     * Database Column Remarks:
     *   报警状态（1-报警中 2-已恢复）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_status")
    private Integer alarmStatus;

    /**
     * Database Column Remarks:
     *   恢复方式（1-人工 2-自动）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_method")
    private Integer recoverMethod;

    /**
     * Database Column Remarks:
     *   恢复方式（1-梧桐 2-长租）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_channel")
    private Integer recoverChannel;

    /**
     * Database Column Remarks:
     *   报警系统（1-梧桐 2-长租）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_system")
    private Integer alarmSystem;

    /**
     * Database Column Remarks:
     *   报警时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_time")
    private Date alarmTime;

    /**
     * Database Column Remarks:
     *   最后一次定位地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location")
    private String lastLocation;

    /**
     * Database Column Remarks:
     *   最后一次定位时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location_time")
    private Date lastLocationTime;

    /**
     * Database Column Remarks:
     *   是否暂停报警（1-是 0-否）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_stop_alarm")
    private Integer isStopAlarm;

    /**
     * Database Column Remarks:
     *   暂停报警升级时长（天）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.stop_alarm_day")
    private Integer stopAlarmDay;

    /**
     * Database Column Remarks:
     *   报警升级暂停截止时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.pause_deadline_time")
    private Date pauseDeadlineTime;

    /**
     * Database Column Remarks:
     *   恢复时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recovery_date")
    private Date recoveryDate;

    /**
     * Database Column Remarks:
     *   报警说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_desc")
    private String alarmDesc;

    /**
     * Database Column Remarks:
     *   升级说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.upgrade_desc")
    private String upgradeDesc;

    /**
     * Database Column Remarks:
     *   出围栏时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.exit_fence_date")
    private Date exitFenceDate;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_account")
    private String updateOperAccount;


    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_no")
    public String getAlarmNo() {
        return alarmNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_no")
    public void setAlarmNo(String alarmNo) {
        this.alarmNo = alarmNo == null ? null : alarmNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_seq")
    public String getOrderSeq() {
        return orderSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_seq")
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq == null ? null : orderSeq.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_type")
    public Integer getOrderType() {
        return orderType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_type")
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_type")
    public Integer getAlarmType() {
        return alarmType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_type")
    public void setAlarmType(Integer alarmType) {
        this.alarmType = alarmType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_level")
    public Integer getAlarmLevel() {
        return alarmLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_level")
    public void setAlarmLevel(Integer alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_status")
    public Integer getAlarmStatus() {
        return alarmStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_status")
    public void setAlarmStatus(Integer alarmStatus) {
        this.alarmStatus = alarmStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_method")
    public Integer getRecoverMethod() {
        return recoverMethod;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_method")
    public void setRecoverMethod(Integer recoverMethod) {
        this.recoverMethod = recoverMethod;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_channel")
    public Integer getRecoverChannel() {
        return recoverChannel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_channel")
    public void setRecoverChannel(Integer recoverChannel) {
        this.recoverChannel = recoverChannel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_system")
    public Integer getAlarmSystem() {
        return alarmSystem;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_system")
    public void setAlarmSystem(Integer alarmSystem) {
        this.alarmSystem = alarmSystem;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_time")
    public Date getAlarmTime() {
        return alarmTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_time")
    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location")
    public String getLastLocation() {
        return lastLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location")
    public void setLastLocation(String lastLocation) {
        this.lastLocation = lastLocation == null ? null : lastLocation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location_time")
    public Date getLastLocationTime() {
        return lastLocationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location_time")
    public void setLastLocationTime(Date lastLocationTime) {
        this.lastLocationTime = lastLocationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_stop_alarm")
    public Integer getIsStopAlarm() {
        return isStopAlarm;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_stop_alarm")
    public void setIsStopAlarm(Integer isStopAlarm) {
        this.isStopAlarm = isStopAlarm;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.stop_alarm_day")
    public Integer getStopAlarmDay() {
        return stopAlarmDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.stop_alarm_day")
    public void setStopAlarmDay(Integer stopAlarmDay) {
        this.stopAlarmDay = stopAlarmDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.pause_deadline_time")
    public Date getPauseDeadlineTime() {
        return pauseDeadlineTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.pause_deadline_time")
    public void setPauseDeadlineTime(Date pauseDeadlineTime) {
        this.pauseDeadlineTime = pauseDeadlineTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recovery_date")
    public Date getRecoveryDate() {
        return recoveryDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recovery_date")
    public void setRecoveryDate(Date recoveryDate) {
        this.recoveryDate = recoveryDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_desc")
    public String getAlarmDesc() {
        return alarmDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_desc")
    public void setAlarmDesc(String alarmDesc) {
        this.alarmDesc = alarmDesc == null ? null : alarmDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.upgrade_desc")
    public String getUpgradeDesc() {
        return upgradeDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.upgrade_desc")
    public void setUpgradeDesc(String upgradeDesc) {
        this.upgradeDesc = upgradeDesc == null ? null : upgradeDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.exit_fence_date")
    public Date getExitFenceDate() {
        return exitFenceDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.exit_fence_date")
    public void setExitFenceDate(Date exitFenceDate) {
        this.exitFenceDate = exitFenceDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}