package com.saicmobility.evcard.vlms.risk.model.extend;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.Date;

/**
 * Database Table Remarks:
 *   车辆资产信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table vlms_assets.t_assets_vehicle
 */
public class AssetsVehicle implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.engine_no")
    private String engineNo;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.color")
    private String color;

    /**
     * Database Column Remarks:
     *   资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_status")
    private Integer propertyStatus;

    /**
     * Database Column Remarks:
     *   资产公司编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_id")
    private String propertyOrgId;

    /**
     * Database Column Remarks:
     *   资产公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_name")
    private String propertyOrgName;

    /**
     * Database Column Remarks:
     *   运营公司编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_id")
    private String operationOrgId;

    /**
     * Database Column Remarks:
     *   运营公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_name")
    private String operationOrgName;

    /**
     * Database Column Remarks:
     *   交付状态 0 未交付 1 已交付
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.delivery_status")
    private Integer deliveryStatus;

    /**
     * Database Column Remarks:
     *   车辆型号id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   品牌型号id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.brand_model_id")
    private Long brandModelId;

    /**
     * Database Column Remarks:
     *   车辆渠道 0:普通 1 EVCARD 2 通用时行 4别克代步车 5松江大学城 6别克深度体验 7享道用车 8大众试乘试驾 9携程项目 10特殊车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_channel")
    private Integer vehicleChannel;

    /**
     * Database Column Remarks:
     *   车辆来源 0:本司采购  1外来租赁
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_source")
    private Integer vehicleSource;

    /**
     * Database Column Remarks:
     *   投运状态 1:未投运 2:投运成功 3:投运审批中
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.put_in_status")
    private Integer putInStatus;

    /**
     * Database Column Remarks:
     *   终端id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.sys_term_id")
    private Long sysTermId;

    /**
     * Database Column Remarks:
     *   商业险到期日
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.commercial_insurance_expire")
    private Date commercialInsuranceExpire;

    /**
     * Database Column Remarks:
     *   交强险到期日
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.compulsory_insurance_expire")
    private Date compulsoryInsuranceExpire;

    /**
     * Database Column Remarks:
     *   转固时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_transfer_time")
    private Date vehicleTransferTime;

    /**
     * Database Column Remarks:
     *   2:长租 3:短租
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.profit_center")
    private Integer profitCenter;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_time")
    private Date createTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_account")
    private String createOperAccount;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_time")
    private Date updateTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_account")
    private String updateOperAccount;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_name")
    private String updateOperName;

    /**
     * Database Column Remarks:
     *   系统同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_update_time")
    private Date systemUpdateTime;

    /**
     * Database Column Remarks:
     *   系统同步编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_sync_code")
    private String systemSyncCode;

    /**
     * Database Column Remarks:
     *   车辆风险状态（1-无风险 2-风险车辆 3-风控车辆 4-失控车辆）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_risk_status")
    private Integer vehicleRiskStatus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.engine_no")
    public String getEngineNo() {
        return engineNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.engine_no")
    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo == null ? null : engineNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.color")
    public String getColor() {
        return color;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.color")
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_status")
    public Integer getPropertyStatus() {
        return propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_status")
    public void setPropertyStatus(Integer propertyStatus) {
        this.propertyStatus = propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_id")
    public String getPropertyOrgId() {
        return propertyOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_id")
    public void setPropertyOrgId(String propertyOrgId) {
        this.propertyOrgId = propertyOrgId == null ? null : propertyOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_name")
    public String getPropertyOrgName() {
        return propertyOrgName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_name")
    public void setPropertyOrgName(String propertyOrgName) {
        this.propertyOrgName = propertyOrgName == null ? null : propertyOrgName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_id")
    public String getOperationOrgId() {
        return operationOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_id")
    public void setOperationOrgId(String operationOrgId) {
        this.operationOrgId = operationOrgId == null ? null : operationOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_name")
    public String getOperationOrgName() {
        return operationOrgName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_name")
    public void setOperationOrgName(String operationOrgName) {
        this.operationOrgName = operationOrgName == null ? null : operationOrgName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.delivery_status")
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.delivery_status")
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.brand_model_id")
    public Long getBrandModelId() {
        return brandModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.brand_model_id")
    public void setBrandModelId(Long brandModelId) {
        this.brandModelId = brandModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_channel")
    public Integer getVehicleChannel() {
        return vehicleChannel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_channel")
    public void setVehicleChannel(Integer vehicleChannel) {
        this.vehicleChannel = vehicleChannel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_source")
    public Integer getVehicleSource() {
        return vehicleSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_source")
    public void setVehicleSource(Integer vehicleSource) {
        this.vehicleSource = vehicleSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.put_in_status")
    public Integer getPutInStatus() {
        return putInStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.put_in_status")
    public void setPutInStatus(Integer putInStatus) {
        this.putInStatus = putInStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.sys_term_id")
    public Long getSysTermId() {
        return sysTermId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.sys_term_id")
    public void setSysTermId(Long sysTermId) {
        this.sysTermId = sysTermId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.commercial_insurance_expire")
    public Date getCommercialInsuranceExpire() {
        return commercialInsuranceExpire;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.commercial_insurance_expire")
    public void setCommercialInsuranceExpire(Date commercialInsuranceExpire) {
        this.commercialInsuranceExpire = commercialInsuranceExpire;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.compulsory_insurance_expire")
    public Date getCompulsoryInsuranceExpire() {
        return compulsoryInsuranceExpire;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.compulsory_insurance_expire")
    public void setCompulsoryInsuranceExpire(Date compulsoryInsuranceExpire) {
        this.compulsoryInsuranceExpire = compulsoryInsuranceExpire;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_transfer_time")
    public Date getVehicleTransferTime() {
        return vehicleTransferTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_transfer_time")
    public void setVehicleTransferTime(Date vehicleTransferTime) {
        this.vehicleTransferTime = vehicleTransferTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.profit_center")
    public Integer getProfitCenter() {
        return profitCenter;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.profit_center")
    public void setProfitCenter(Integer profitCenter) {
        this.profitCenter = profitCenter;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_update_time")
    public Date getSystemUpdateTime() {
        return systemUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_update_time")
    public void setSystemUpdateTime(Date systemUpdateTime) {
        this.systemUpdateTime = systemUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_sync_code")
    public String getSystemSyncCode() {
        return systemSyncCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_sync_code")
    public void setSystemSyncCode(String systemSyncCode) {
        this.systemSyncCode = systemSyncCode == null ? null : systemSyncCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_risk_status")
    public Integer getVehicleRiskStatus() {
        return vehicleRiskStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_risk_status")
    public void setVehicleRiskStatus(Integer vehicleRiskStatus) {
        this.vehicleRiskStatus = vehicleRiskStatus;
    }
}