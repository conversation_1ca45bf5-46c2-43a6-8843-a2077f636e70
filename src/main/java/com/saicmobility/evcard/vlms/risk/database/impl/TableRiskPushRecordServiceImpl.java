package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.mapper.RiskPushRecordMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskPushRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Date;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskPushRecordDynamicSqlSupport.riskPushRecord;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
@Slf4j
public class TableRiskPushRecordServiceImpl implements TableRiskPushRecordService {

    @Resource
    private RiskPushRecordMapper riskPushRecordMapper;
    @Override
    public void insert(String alarmNo) {
        RiskPushRecord pushRecord = new RiskPushRecord();
        pushRecord.setAlarmNo(alarmNo);
        pushRecord.setCreateTime(new Date());
        riskPushRecordMapper.insertSelective(pushRecord);
    }

    @Override
    public RiskPushRecord query(String alarmNo) {
        if (StringUtils.isBlank(alarmNo)){
            return null;
        }
        SelectStatementProvider render = select(riskPushRecord.allColumns())
                .from(riskPushRecord)
                .where()
                .and(riskPushRecord.alarmNo, isEqualTo(alarmNo))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskPushRecordMapper.selectOne(render).orElse(null);
    }
}
