package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   风控收车表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_check
 */
public class RiskCheck implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   风控收车编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.check_no")
    private String checkNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   处理状态（1-执行中 2-已执行 3-审批中）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.deal_status")
    private Integer dealStatus;

    /**
     * Database Column Remarks:
     *   报警系统（1-梧桐 2-长租）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.alarm_system")
    private Integer alarmSystem;

    /**
     * Database Column Remarks:
     *   最后一次定位地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location")
    private String lastLocation;

    /**
     * Database Column Remarks:
     *   最后一次定位时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location_time")
    private Date lastLocationTime;

    /**
     * Database Column Remarks:
     *   失控类型（1-失联车辆、2-灭失车辆、3-纠纷被扣、4-行政被扣）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_type")
    private Integer outControlType;

    /**
     * Database Column Remarks:
     *   升级失控时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_date")
    private Date outControlDate;

    /**
     * Database Column Remarks:
     *   失控附件关联ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_annex_id")
    private Long outControlAnnexId;

    /**
     * Database Column Remarks:
     *   失控说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_desc")
    private String outControlDesc;

    /**
     * Database Column Remarks:
     *   车辆照片/附件关联ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_pic_id")
    private Long vehiclePicId;

    /**
     * Database Column Remarks:
     *   车辆收回说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_check_desc")
    private String vehicleCheckDesc;

    /**
     * Database Column Remarks:
     *   恢复时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.recovery_time")
    private Date recoveryTime;

    /**
     * Database Column Remarks:
     *   完成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.task_end_time")
    private Date taskEndTime;

    /**
     * Database Column Remarks:
     *   任务完成处理人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_name")
    private String endOperName;

    /**
     * Database Column Remarks:
     *   任务完成处理人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_account")
    private String endOperAccount;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_account")
    private String updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_name")
    private String updateOperName;

    /**
     * Database Column Remarks:
     *   是否因加入白名单而恢复 0 否，1：是 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_white_list_recover")
    private Integer isWhiteListRecover;

    /**
     * Database Column Remarks:
     *   自动完成长租收车 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_auto_complete")
    private Integer isAutoComplete;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.check_no")
    public String getCheckNo() {
        return checkNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.check_no")
    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo == null ? null : checkNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.deal_status")
    public Integer getDealStatus() {
        return dealStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.deal_status")
    public void setDealStatus(Integer dealStatus) {
        this.dealStatus = dealStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.alarm_system")
    public Integer getAlarmSystem() {
        return alarmSystem;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.alarm_system")
    public void setAlarmSystem(Integer alarmSystem) {
        this.alarmSystem = alarmSystem;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location")
    public String getLastLocation() {
        return lastLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location")
    public void setLastLocation(String lastLocation) {
        this.lastLocation = lastLocation == null ? null : lastLocation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location_time")
    public Date getLastLocationTime() {
        return lastLocationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location_time")
    public void setLastLocationTime(Date lastLocationTime) {
        this.lastLocationTime = lastLocationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_type")
    public Integer getOutControlType() {
        return outControlType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_type")
    public void setOutControlType(Integer outControlType) {
        this.outControlType = outControlType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_date")
    public Date getOutControlDate() {
        return outControlDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_date")
    public void setOutControlDate(Date outControlDate) {
        this.outControlDate = outControlDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_annex_id")
    public Long getOutControlAnnexId() {
        return outControlAnnexId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_annex_id")
    public void setOutControlAnnexId(Long outControlAnnexId) {
        this.outControlAnnexId = outControlAnnexId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_desc")
    public String getOutControlDesc() {
        return outControlDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_desc")
    public void setOutControlDesc(String outControlDesc) {
        this.outControlDesc = outControlDesc == null ? null : outControlDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_pic_id")
    public Long getVehiclePicId() {
        return vehiclePicId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_pic_id")
    public void setVehiclePicId(Long vehiclePicId) {
        this.vehiclePicId = vehiclePicId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_check_desc")
    public String getVehicleCheckDesc() {
        return vehicleCheckDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_check_desc")
    public void setVehicleCheckDesc(String vehicleCheckDesc) {
        this.vehicleCheckDesc = vehicleCheckDesc == null ? null : vehicleCheckDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.recovery_time")
    public Date getRecoveryTime() {
        return recoveryTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.recovery_time")
    public void setRecoveryTime(Date recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.task_end_time")
    public Date getTaskEndTime() {
        return taskEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.task_end_time")
    public void setTaskEndTime(Date taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_name")
    public String getEndOperName() {
        return endOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_name")
    public void setEndOperName(String endOperName) {
        this.endOperName = endOperName == null ? null : endOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_account")
    public String getEndOperAccount() {
        return endOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_account")
    public void setEndOperAccount(String endOperAccount) {
        this.endOperAccount = endOperAccount == null ? null : endOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_white_list_recover")
    public Integer getIsWhiteListRecover() {
        return isWhiteListRecover;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_white_list_recover")
    public void setIsWhiteListRecover(Integer isWhiteListRecover) {
        this.isWhiteListRecover = isWhiteListRecover;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_auto_complete")
    public Integer getIsAutoComplete() {
        return isAutoComplete;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_auto_complete")
    public void setIsAutoComplete(Integer isAutoComplete) {
        this.isAutoComplete = isAutoComplete;
    }
}