package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.service.OperateLogService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryOperationLogReq;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryOperationLogRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class OperateLogServiceImpl implements OperateLogService {

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Override
    public QueryOperationLogRes queryOperationLog(QueryOperationLogReq req) {
        QueryOperationLogRes.Builder builder = QueryOperationLogRes.newBuilder();
        List<OperateLog> operateLogs = tableOperateLogService.selectList(req);
        if (CollectionUtil.isEmpty(operateLogs)){
            return builder.build();
        }
        List<com.saicmobility.evcard.vlms.vlmsriskservice.api.OperateLog> operateLogList= new ArrayList<>();
        for (OperateLog operateLog : operateLogs) {
            operateLogList.add(PbConvertUtil.generateProtoBuffer(operateLog, com.saicmobility.evcard.vlms.vlmsriskservice.api.OperateLog.class));
        }
        builder.addAllList(operateLogList);
        builder.setTotal(tableOperateLogService.selectTotal(req));
        return builder.build();
    }
}
