package com.saicmobility.evcard.vlms.risk;

import com.saicmobility.evcard.vlms.risk.service.*;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
@Slf4j
public class VlmsRiskServiceImpl implements VlmsRiskService {

    @Resource
    private OperateLogService operateLogService;

    @Resource
    private PauseAlarmConfigService pauseAlarmConfigService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private RiskCheckService riskCheckService;

    @Resource
    private RiskWhitelistConfigService riskWhitelistConfigService;

    @Resource
    private VehicleElectronicFenceRegulationService vehicleElectronicFenceRegulationService;

    @Resource
    private VehicleTerminalService vehicleTerminalService;

    @Resource
    private RiskCheckCollectionInfoService riskCheckCollectionInfoService;

    @Override
    public QueryRiskAlarmListRes queryRiskAlarmList(QueryRiskAlarmListReq queryRiskAlarmListReq) {
        return riskAlarmService.queryRiskAlarmList(queryRiskAlarmListReq);
    }

    @Override
    public AddRiskAlarmRes addRiskAlarm(AddRiskAlarmReq addRiskAlarmReq) {
        return riskAlarmService.addRiskAlarm(addRiskAlarmReq);
    }

    @Override
    public QueryRiskAlarmListRes exportRiskAlarm(QueryRiskAlarmListReq queryRiskAlarmListReq) {
        return riskAlarmService.exportRiskAlarm(queryRiskAlarmListReq);
    }

    @Override
    public QueryRiskAlarmDetailRes queryRiskAlarmDetail(QueryRiskAlarmDetailReq queryRiskAlarmDetailReq) {
        return riskAlarmService.queryRiskAlarmDetail(queryRiskAlarmDetailReq);
    }

    @Override
    public PauseRiskAlarmRes pauseRiskAlarm(PauseRiskAlarmReq pauseRiskAlarmReq) {
        return riskAlarmService.pauseRiskAlarm(pauseRiskAlarmReq);
    }

    @Override
    public UpgradeRiskAlarmDescRes upgradeRiskAlarmDesc(UpgradeRiskAlarmDescReq upgradeRiskAlarmDescReq) {
        return riskAlarmService.upgradeRiskAlarmDesc(upgradeRiskAlarmDescReq);
    }

    @Override
    public RemoveRiskAlarmRes removeRiskAlarm(RemoveRiskAlarmReq removeRiskAlarmReq) {
        return riskAlarmService.removeRiskAlarm(removeRiskAlarmReq);
    }

    @Override
    public QueryVehicleRiskAlarmTypeRes queryVehicleRiskAlarmType(QueryVehicleRiskAlarmTypeReq queryVehicleRiskAlarmTypeReq) {
        return riskAlarmService.queryVehicleRiskAlarmType(queryVehicleRiskAlarmTypeReq);
    }

    @Override
    public BatchPauseRiskAlarmRes batchPauseRiskAlarm(BatchPauseRiskAlarmReq batchPauseRiskAlarmReq) {
        return riskAlarmService.batchPauseRiskAlarm(batchPauseRiskAlarmReq);
    }

    @Override
    public BatchRemoveRiskAlarmRes batchRemoveRiskAlarm(BatchRemoveRiskAlarmReq batchRemoveRiskAlarmReq) {
        return riskAlarmService.batchRemoveRiskAlarm(batchRemoveRiskAlarmReq);
    }

    @Override
    public QueryRiskCheckRes queryRiskCheck(QueryRiskCheckReq queryRiskCheckReq) {
        return riskCheckService.queryRiskCheck(queryRiskCheckReq);
    }

    @Override
    public QueryRiskCheckRes exportRiskCheck(QueryRiskCheckReq queryRiskCheckReq) {
        return riskCheckService.exportRiskCheck(queryRiskCheckReq);
    }

    @Override
    public QueryRiskCheckDetailRes queryRiskCheckDetail(QueryRiskCheckDetailReq queryRiskCheckDetailReq) {
        return riskCheckService.queryRiskCheckDetail(queryRiskCheckDetailReq);
    }

    @Override
    public UpgradeRiskCheckRes upgradeRiskCheck(UpgradeRiskCheckReq upgradeRiskCheckReq) {
        return riskCheckService.upgradeRiskCheck(upgradeRiskCheckReq);
    }

    @Override
    public CompleteRiskCheckRes completeRiskCheck(CompleteRiskCheckReq completeRiskCheckReq) {
//        return riskCheckService.completeRiskCheck(completeRiskCheckReq);
        return riskCheckService.submitRiskCheckToBaiYang(completeRiskCheckReq);
    }

    @Override
    public CancelRiskCheckRes cancelRiskCheck(CancelRiskCheckReq cancelRiskCheckReq) {
        return riskCheckService.cancelRiskCheck(cancelRiskCheckReq);
    }

    @Override
    public UpgradeRiskAlarmDescRes batchUpgradeRiskAlarmDesc(BatchUpgradeRiskAlarmDescReq batchUpgradeRiskAlarmDescReq) {
        return riskAlarmService.batchUpgradeRiskAlarmDesc(batchUpgradeRiskAlarmDescReq);
    }

    @Override
    public RiskCheckTmpDetailRes riskCheckTmpDetail(RiskCheckTmpDetailReq riskCheckTmpDetailReq) {
        return null;
    }

    @Override
    public QueryRiskAlarmConfigRes queryRiskAlarmConfig(QueryRiskAlarmConfigReq queryRiskAlarmConfigReq) {
        return riskAlarmConfigService.queryRiskAlarmConfig(queryRiskAlarmConfigReq);
    }

    @Override
    public QueryRiskAlarmConfigRes exportRiskAlarmConfig(QueryRiskAlarmConfigReq queryRiskAlarmConfigReq) {
        return riskAlarmConfigService.exportRiskAlarmConfig(queryRiskAlarmConfigReq);
    }

    @Override
    public UpdateRiskAlarmConfigRes updateRiskAlarmConfig(UpdateRiskAlarmConfigReq updateRiskAlarmConfigReq) {
        return riskAlarmConfigService.updateRiskAlarmConfig(updateRiskAlarmConfigReq);
    }

    @Override
    public AddRiskAlarmConfiRes addRiskAlarmConfig(AddRiskAlarmConfiReq addRiskAlarmConfiReq) {
        return riskAlarmConfigService.addRiskAlarmConfig(addRiskAlarmConfiReq);
    }

    @Override
    public WangNengRiskAlarmRes wangNengRiskAlarm(WangNengRiskAlarmReq wangNengRiskAlarmReq) {
        return riskAlarmConfigService.wangNengRiskAlarm(wangNengRiskAlarmReq);
    }

    @Override
    public SyncRiskOrderRes syncRiskOrder(SyncRiskOrderReq syncRiskOrderReq) {
        return riskAlarmConfigService.syncRiskOrder(syncRiskOrderReq);
    }

    @Override
    public QueryPauseAlarmDurationConfigRes queryPauseAlarmDurationConfig(QueryPauseAlarmDurationConfigReq queryPauseAlarmDurationConfigReq) {
        return pauseAlarmConfigService.queryPauseAlarmDurationConfig(queryPauseAlarmDurationConfigReq);
    }

    @Override
    public UpdatePauseAlarmDurationConfigRes updatePauseAlarmDurationConfig(UpdatePauseAlarmDurationConfigReq updatePauseAlarmDurationConfigReq) {
        return pauseAlarmConfigService.updatePauseAlarmDurationConfig(updatePauseAlarmDurationConfigReq);
    }

    @Override
    public QueryWhiteListRes queryWhiteList(QueryWhiteListReq queryWhiteListReq) {
        return riskWhitelistConfigService.queryWhiteList(queryWhiteListReq);
    }

    @Override
    public ImportVehicleRes importVehicles(ImportVehicleReq importVehicleReq) {
        return riskWhitelistConfigService.importVehicles(importVehicleReq);
    }

    @Override
    public QueryWhiteListRes exportWhiteList(QueryWhiteListReq queryWhiteListReq) {
        return riskWhitelistConfigService.exportWhiteList(queryWhiteListReq);
    }

    @Override
    public UpdateWhiteListConfigRes updateWhiteListConfig(UpdateWhiteListConfigReq updateWhiteListConfigReq) {
        return riskWhitelistConfigService.updateWhiteListConfig(updateWhiteListConfigReq);
    }

    @Override
    public CloseWhiteListConfigRes closeWhiteListConfig(CloseWhiteListConfigReq closeWhiteListConfigReq) {
        return riskWhitelistConfigService.closeWhiteListConfig(closeWhiteListConfigReq);
    }

    @Override
    public QueryOperationLogRes queryOperationLog(QueryOperationLogReq queryOperationLogReq) {
        return operateLogService.queryOperationLog(queryOperationLogReq);
    }

    @Override
    public InitElectronicFenceConfigRes initElectronicFenceConfig(InitElectronicFenceConfigReq initElectronicFenceConfigReq) {
        return vehicleElectronicFenceRegulationService.initElectronicFenceConfig(initElectronicFenceConfigReq);
    }

    @Override
    public QueryElectronicFenceConfigRes queryElectronicFenceConfig(QueryElectronicFenceConfigReq queryElectronicFenceConfigReq) {
        return vehicleElectronicFenceRegulationService.queryElectronicFenceConfig(queryElectronicFenceConfigReq);
    }

    @Override
    public QueryElectronicFenceConfigRes exportElectronicFenceConfig(QueryElectronicFenceConfigReq queryElectronicFenceConfigReq) {
        return vehicleElectronicFenceRegulationService.exportElectronicFenceConfig(queryElectronicFenceConfigReq);
    }

    @Override
    public AddElectronicFenceConfigListRes addElectronicFenceConfigList(AddElectronicFenceConfigListReq addElectronicFenceConfigListReq) {
        return vehicleElectronicFenceRegulationService.addElectronicFenceConfigList(addElectronicFenceConfigListReq);
    }

    @Override
    public UpdateElectronicFenceConfigRes updateElectronicFenceConfig(UpdateElectronicFenceConfigReq updateElectronicFenceConfigReq) {
        return vehicleElectronicFenceRegulationService.updateElectronicFenceConfig(updateElectronicFenceConfigReq);
    }

    @Override
    public DeleteElectronicFenceConfigRes deleteElectronicFenceConfig(DeleteElectronicFenceConfigReq deleteElectronicFenceConfigReq) {
        return vehicleElectronicFenceRegulationService.deleteElectronicFenceConfig(deleteElectronicFenceConfigReq);
    }

    @Override
    public QueryElectronicFenceDefaultRegulationLogRes queryElectronicFenceDefaultRegulationLog(QueryElectronicFenceDefaultRegulationLogReq queryElectronicFenceDefaultRegulationLogReq) {
        return vehicleElectronicFenceRegulationService.queryElectronicFenceDefaultRegulationLog(queryElectronicFenceDefaultRegulationLogReq);
    }

    @Override
    public InsertOrUpdateBicycleRegulationRes insertOrUpdateBicycleRegulation(InsertOrUpdateBicycleRegulationReq insertOrUpdateBicycleRegulationReq) {
        return vehicleElectronicFenceRegulationService.insertOrUpdateBicycleRegulation(insertOrUpdateBicycleRegulationReq);
    }

    @Override
    public QueryAllProvinceRes queryAllProvince(QueryAllProvinceReq queryAllProvinceReq) {
        return vehicleElectronicFenceRegulationService.queryAllProvince(queryAllProvinceReq);
    }

    @Override
    public QueryAllCityByProvinceRes queryAllCityByProvince(QueryAllCityByProvinceReq queryAllCityByProvinceReq) {
        return vehicleElectronicFenceRegulationService.queryAllCityByProvince(queryAllCityByProvinceReq);
    }

    @Override
    public QueryTerminalInfoByVinRes queryTerminalInfoByVin(QueryTerminalInfoByVinReq queryTerminalInfoByVinReq) {
        return vehicleTerminalService.queryTerminalInfoByVin(queryTerminalInfoByVinReq);
    }

    @Override
    public QueryVehicleTrackRes queryVehicleTrack(QueryVehicleTrackReq queryVehicleTrackReq) {
        return vehicleTerminalService.queryVehicleTrack(queryVehicleTrackReq);
    }

    @Override
    public QueryOrderTravelListByOrderSeqRes queryOrderTravelListByOrderSeq(QueryOrderTravelListByOrderSeqReq queryOrderTravelListByOrderSeqReq) {
        return vehicleTerminalService.queryOrderTravelListByOrderSeq(queryOrderTravelListByOrderSeqReq);
    }

    @Override
    public QueryCityConfigListRes queryCityConfigList(QueryCityConfigListReq queryCityConfigListReq) {
        return vehicleTerminalService.queryCityConfigList(queryCityConfigListReq);
    }

    @Override
    public ExportVehicleTrackRes exportVehicleTrack(QueryVehicleTrackReq queryVehicleTrackReq) {
        return vehicleTerminalService.exportVehicleTrack(queryVehicleTrackReq);
    }

    @Override
    public QueryRiskCheckCollectionInfoRes queryRiskCheckCollectionInfo(QueryRiskCheckCollectionInfoReq queryRiskCheckCollectionInfoReq) {
        return riskCheckCollectionInfoService.queryRiskCheckCollectionInfo(queryRiskCheckCollectionInfoReq);
    }

    @Override
    public ExportRiskCheckCollectionInfoRes exportRiskCheckCollectionInfo(ExportRiskCheckCollectionInfoReq queryRiskCheckCollectionInfoReq) {
        return riskCheckCollectionInfoService.exportRiskCheckCollectionInfo(queryRiskCheckCollectionInfoReq);
    }

    @Override
    public AddRiskCheckCollectionInfoRes addRiskCheckCollectionInfo(AddRiskCheckCollectionInfoReq addRiskCheckCollectionInfoReq) {
        return riskCheckCollectionInfoService.addRiskCheckCollectionInfo(addRiskCheckCollectionInfoReq);
    }

    @Override
    public FinishWorkOrderRes finishWorkOrder(FinishWorkOrderReq finishWorkOrderReq) {
        return riskAlarmService.finishWorkOrder(finishWorkOrderReq);
    }

    @Override
    public AddRiskCheckForReturnCarRes addRiskCheckForReturnCar(AddRiskCheckForReturnCarReq addRiskCheckForReturnCarReq) {
        return riskAlarmService.addRiskCheckForReturnCar(addRiskCheckForReturnCarReq);
    }

    @Override
    public CancelWorkOrderRes cancelWorkOrder(CancelWorkOrderReq cancelWorkOrderReq) {
        return riskAlarmService.cancelWorkOrder(cancelWorkOrderReq);
    }

    @Override
    public SubmitRiskCheckApprovalResultRes submitRiskCheckApprovalResult(SubmitRiskCheckApprovalResultReq submitRiskCheckApprovalResultReq) {
        return riskCheckService.submitRiskCheckApprovalResult(submitRiskCheckApprovalResultReq);
    }

    @Override
    public HandleCancelRiskAlarmRes handleCancelRiskAlarm(HandleCancelRiskAlarmReq handleCancelRiskAlarmReq) {
        return riskAlarmService.handleCancelRiskAlarm(handleCancelRiskAlarmReq);
    }

    @Override
    public batchAddRiskCheckCollectionInfoRes batchAddRiskCheckCollectionInfo(batchAddRiskCheckCollectionInfoReq batchAddRiskCheckCollectionInfoReq) {
        return riskCheckCollectionInfoService.batchAddRiskCheckCollectionInfo(batchAddRiskCheckCollectionInfoReq);
    }
}
