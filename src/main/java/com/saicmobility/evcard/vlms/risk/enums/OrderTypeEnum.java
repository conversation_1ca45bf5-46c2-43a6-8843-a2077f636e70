package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单类型
 * <AUTHOR>
 * @date 2024-01-16
 */

@AllArgsConstructor
@Getter
public enum OrderTypeEnum {
    MD_ORDER(1, "门店订单"),
    PARTNER_ORDER(2, "渠道订单"),
    LONG_RENT_ORDER(3, "长租订单"),
    SAIC_ORDER(4, "分时订单"),
   ;
    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (OrderTypeEnum item : OrderTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCodeByValue(String value){
        for (OrderTypeEnum item : OrderTypeEnum.values()) {
            if (item.getValue().equals(value.trim())){
                return item.getCode();
            }
        }
        return null;
    }

    public static OrderTypeEnum getEnumByType(Integer code) {
        for (OrderTypeEnum item : OrderTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
