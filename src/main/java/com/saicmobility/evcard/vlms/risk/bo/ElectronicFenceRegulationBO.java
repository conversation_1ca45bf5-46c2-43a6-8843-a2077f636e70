package com.saicmobility.evcard.vlms.risk.bo;

import lombok.Data;

@Data
public class ElectronicFenceRegulationBO {

    private Long id;

    /**
     * 规则类型 1、默认规则 2、单车规则
     */
    private Integer regulationType;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆业务状态（0-分时租赁、1-长租、2-专车、3-短租、4-公务用车、5-市场用车、6-其他、7-课题测试）
     */
    private Integer rentType;

    /**
     * 所属公司id
     */
    private String orgId;

    /**
     * 所属公司id
     */
    private String orgName;

    /**
     * 运营公司id
     */
    private String operationOrgId;


    /**
     * 运营公司id
     */
    private String operationOrgName;


    /**
     * 围栏城市id范围
     */
    private String penCityIdScope;

    /**
     * 围栏城市范围
     */
    private String penCityScope;

    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 是否查询所有
     */
    private Integer isAll = 0;

    /**
     * 产品线 1:车管中心 2:长租 3:短租 4:公务用车
     */
    private Integer productLine;

    /**
     * 子产品线 1:携程短租-短租 2:门店-短租 3.分时-短租 4.普通-长租 5.时行-长租 6.平台业务-长租 7.政企业务-长租 8.网约车业务-长租 9.公务用车-公务车
     */
    private Integer businessLine;

    /**
     * 产品线 1:车管中心 2:长租 3:短租 4:公务用车
     */
    private String productLineStr;
    /**
     * 产品线 1:车管中心 2:长租 3:短租 4:公务用车
     */
    private String businessLineStr;
}
