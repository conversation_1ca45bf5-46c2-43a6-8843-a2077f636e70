package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   风控收车完成任务白杨审批表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_check_complete_approve
 */
public class RiskCheckCompleteApprove implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   白杨审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.application_id")
    private String applicationId;

    /**
     * Database Column Remarks:
     *   审批状态 1-审批中 10-审批完成 20-审批退回 30-审批拒绝 40-撤回
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.approve_status")
    private Integer approveStatus;

    /**
     * Database Column Remarks:
     *   请求no（提交审核时）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.request_no")
    private String requestNo;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.status")
    private Short status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   用户名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.user_name")
    private String userName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *   创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_account")
    private String updateOperAccount;

    /**
     * Database Column Remarks:
     *   修改人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.application_id")
    public String getApplicationId() {
        return applicationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.application_id")
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId == null ? null : applicationId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.approve_status")
    public Integer getApproveStatus() {
        return approveStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.approve_status")
    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.request_no")
    public String getRequestNo() {
        return requestNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.request_no")
    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo == null ? null : requestNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.status")
    public Short getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.status")
    public void setStatus(Short status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.user_name")
    public String getUserName() {
        return userName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.user_name")
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}