package com.saicmobility.evcard.vlms.risk.dto;

public class BaseMasterRecord {
    /**
     * 梧桐单据号码
     */
    private String documentNum;

    /**
     * 梧桐单据链接
     */
    private String documentLink;

    /**
     * 单据标题
     */
    private String applicationTitle;

    /**
     * 提交人  传UID，传提交人UID
     */
    private String submitBy;

    /**
     * 需求人/申请人 传UID，传申请人UID
     */
    private String applyBy;

    /**
     * 部门 传入申请人部门code
     */
    private String deptCode;

    /**
     * 运营公司 传入单据归属的公司code，需要转换为白杨C开头的code
     */
    private String compCode;

    /**
     * 运营公司sap code
     */
    private String compSapCode;

    /**
     * 情况说明
     */
    private String situationDescription;

    /**
     * 单据所属公司
     */
    private String branchId;

    /**
     * 单据所属公司名称
     */
    private String branchName;

    public String getSituationDescription() {
        return situationDescription;
    }

    public void setSituationDescription(String situationDescription) {
        this.situationDescription = situationDescription;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getDocumentNum() {
        return documentNum;
    }

    public void setDocumentNum(String documentNum) {
        this.documentNum = documentNum;
    }

    public String getDocumentLink() {
        return documentLink;
    }

    public void setDocumentLink(String documentLink) {
        this.documentLink = documentLink;
    }

    public String getApplicationTitle() {
        return applicationTitle;
    }

    public void setApplicationTitle(String applicationTitle) {
        this.applicationTitle = applicationTitle;
    }

    public String getSubmitBy() {
        return submitBy;
    }

    public void setSubmitBy(String submitBy) {
        this.submitBy = submitBy;
    }

    public String getApplyBy() {
        return applyBy;
    }

    public void setApplyBy(String applyBy) {
        this.applyBy = applyBy;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getCompCode() {
        return compCode;
    }

    public void setCompCode(String compCode) {
        this.compCode = compCode;
    }

    public String getCompSapCode() {
        return compSapCode;
    }

    public void setCompSapCode(String compSapCode) {
        this.compSapCode = compSapCode;
    }

    private String confirmReason;

    public String getConfirmReason() {
        return confirmReason;
    }

    public void setConfirmReason(String confirmReason) {
        this.confirmReason = confirmReason;
    }
}
