package com.saicmobility.evcard.vlms.risk.mapper.extend;

import com.saicmobility.evcard.vlms.risk.mapper.AttachmentMapper;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;
import java.util.Collection;

import static com.saicmobility.evcard.vlms.risk.mapper.AttachmentDynamicSqlSupport.*;


@Mapper
public interface AttachmentExtendMapper extends AttachmentMapper {

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int insertMultiple(Collection<Attachment> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, attachment, c ->
                c.map(fileType).toProperty("fileType")
                        .map(relationId).toProperty("relationId")
                        .map(fileName).toProperty("fileName")
                        .map(filePath).toProperty("filePath")
                        .map(contentType).toProperty("contentType")
                        .map(createOperAccount).toProperty("createOperAccount")
                        .map(createOperName).toProperty("createOperName")
        );
    }
}
