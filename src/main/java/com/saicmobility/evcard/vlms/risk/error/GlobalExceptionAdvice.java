package com.saicmobility.evcard.vlms.risk.error;

import com.google.protobuf.GeneratedMessageV3;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
public class GlobalExceptionAdvice {

   @Pointcut("execution(* com.saicmobility.evcard.vlms.risk.VlmsRiskServiceImpl.*(..))")
   public void exceptionPointCut() {
   }


   @Around("exceptionPointCut()")
   public Object exceptionAround(ProceedingJoinPoint joinPoint) throws Throwable {
      try{
         return joinPoint.proceed();
      }catch (ServiceException e) {
         Signature signature = joinPoint.getSignature();
         if(signature instanceof MethodSignature){
            //被切方法
            MethodSignature methodSignature = (MethodSignature) signature;
            //返回类型
            Class<?> methodReturnType = methodSignature.getReturnType();

            if (GeneratedMessageV3.class.isAssignableFrom(methodReturnType)) {
               Object builder = methodReturnType.getDeclaredMethod("newBuilder").invoke(null);

               //组装retCode
               Method setRetCodeMethod = builder.getClass().getMethod("setRetCode", int.class);
               setRetCodeMethod.invoke(builder, e.getResultCode().getCode());
               //组装retMsg
               Method setRetMsgMethod = builder.getClass().getMethod("setRetMsg", String.class);
               setRetMsgMethod.invoke(builder, e.getMessage());

               return builder.getClass().getDeclaredMethod("build").invoke(builder);
            }
            throw e;
         }
      }catch (Throwable e){
         log.error(e.getMessage());
         throw e;
      }
      return null;
   }


}
