package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CollectCarTypeEnum {

    // 1:内部 2:委外
    INTERNAL(1,"内部"),
    OUTSIDE(2,"委外");

    private Integer code;
    private String value;
    public static String getValueByCode(Integer code)
    {
        for (CollectCarTypeEnum dealStatusEnum : CollectCarTypeEnum.values()) {
            if (dealStatusEnum.getCode().equals(code)) {
                return dealStatusEnum.getValue();
            }
        }
        return null;
    }

    public static Integer getCodeByValue(String value)
    {
        for (CollectCarTypeEnum dealStatusEnum : CollectCarTypeEnum.values()) {
            if (dealStatusEnum.getValue().equals(value)) {
                return dealStatusEnum.getCode();
            }
        }
        return null;
    }
}
