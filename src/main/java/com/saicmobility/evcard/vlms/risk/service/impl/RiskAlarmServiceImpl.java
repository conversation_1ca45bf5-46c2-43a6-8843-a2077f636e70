package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.database.TablePauseAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.VehicleOperateLog;
import com.saicmobility.evcard.vlms.risk.dto.riskalarm.QueryRiskAlarmListResponse;
import com.saicmobility.evcard.vlms.risk.dto.riskalarm.QueryVehicleRiskAlarmTypeResponse;
import com.saicmobility.evcard.vlms.risk.dto.riskalarm.RiskAlarmData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.excel.*;
import com.saicmobility.evcard.vlms.risk.job.AutoRecoveryRiskHandler;
import com.saicmobility.evcard.vlms.risk.model.*;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.service.*;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.BaseUtils;
import com.saicmobility.evcard.vlms.risk.utils.ExcelUtil;
import com.saicmobility.evcard.vlms.risk.utils.HttpClientUtils;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;

import static com.saicmobility.evcard.vlms.risk.enums.RiskOperateEnum.*;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class RiskAlarmServiceImpl extends BaseService implements RiskAlarmService {

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private TablePauseAlarmConfigService tablePauseAlarmConfigService;

    @Resource
    private RiskCheckService riskCheckService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private ExportFileService exportFileService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private SubRiskAlaramService subRiskAlaramService;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;

    @Resource
    private TableRiskCheckService tableRiskCheckService;


    public final List<Integer> ADD_ALARM_TYPE_LIST =
            Arrays.asList(AlarmTypeEnum.VEHICLE_IS_BROKEN.getCode(), AlarmTypeEnum.BUSINESS_RISK.getCode());

    // 报警升级允许的类型
    public final List<Integer> UPGRADE_ALARM_TYPE_LIST =
            Arrays.asList(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode(), AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(),
                    AlarmTypeEnum.VEHICLE_MOVEMENT.getCode(), AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());

    @Override
    public QueryRiskAlarmListRes queryRiskAlarmList(QueryRiskAlarmListReq req) {
        List<RiskAlarmData> riskAlarmList = tableRiskAlarmService.selectList(req);
        if (CollectionUtil.isNotEmpty(riskAlarmList)){
            for (RiskAlarmData riskAlarm : riskAlarmList) {
                // 构建返回参数
                buildRiskAlarmData(riskAlarm);
            }
        }
        QueryRiskAlarmListResponse response = new QueryRiskAlarmListResponse();
        response.setTotal(tableRiskAlarmService.selectTotal(req));
        response.setList(riskAlarmList);
        return PbConvertUtil.generateProtoBuffer(response, QueryRiskAlarmListRes.class);
    }

    private void buildRiskAlarmData(RiskAlarmData riskAlarmData) {

        riskAlarmData.setAlarmLevel(AlarmLevenEnum.getValueByCode(riskAlarmData.getAlarmLevelNum()));

        // 查询车辆信息
        AssetsVehicle vehicleInfo = getVehicleInfoByVin(riskAlarmData.getVin());
        if (vehicleInfo != null){
            riskAlarmData.setOrgName(vehicleInfo.getPropertyOrgName());
            riskAlarmData.setOperateOrgName(vehicleInfo.getOperationOrgName());
            riskAlarmData.setProductLine(vehicleInfo.getProductLine());
            riskAlarmData.setSubProductLine(vehicleInfo.getSubProductLine());
            riskAlarmData.setStoreName(getStoreName(vehicleInfo.getStoreId()));
            //riskAlarmData.setVehicleRiskStatus(vehicleInfo.getVehicleRiskStatus());
            riskAlarmData.setPropertyStatus(vehicleInfo.getPropertyStatus());
        }
        // 报警中且是暂停状态
       /* if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_STATUS.getCode()) && riskAlarm.getIsStopAlarm().equals(IsStopAlarmEnum.YES.getCode())){
            riskAlarmData.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        }*/
        // 查询风控报警条数
        if (riskAlarmData.getAlarmStatus() == AlarmStatusEnum.ALARM_STATUS.getCode()){
            List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskAlarmData.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
            riskAlarmData.setAlarmNum(riskAlarmList.size());
        }
    }

    @Transactional
    @Override
    public AddRiskAlarmRes addRiskAlarm(AddRiskAlarmReq req) {
        int alarmType = req.getAlarmType();
        if (!ADD_ALARM_TYPE_LIST.contains(alarmType)){
            throw new ServiceException("手动添加风控仅支持【车辆被扣】和【经营风险】");
        }
        // 解析excel
        List<ImportVehicleDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, ImportVehicleDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 5000){
            throw new ServiceException("单次上传限制5000条");
        }
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();
        List<RiskAlarm> riskAlarmList = new ArrayList<>();
        Map<String, Boolean> checkMap = new LinkedHashMap<>();
        int row = 1;
        for (ImportVehicleDemo importVehicleDemo : vehicleDemoList) {
            String vin = importVehicleDemo.getVin();
            if (ObjectUtil.isNotEmpty(checkMap.get(vin))) {
                errorInfoList.add(createErrorInfo(vin, "该车辆存在多条数据，请检查上传清单"));
            }

            if (StringUtils.isBlank(vin)){
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
            }else {
                // 判断车辆是否存在
                AssetsVehicle assetsVehicle = getVehicleInfoByVin(vin);
                if (assetsVehicle == null){
                    errorInfoList.add(createErrorInfo(vin, "车辆不存在"));
                    continue;
                }
                int productLine = assetsVehicle.getProductLine();
                // 【产品线】=长租，不可以添加风控（即，不可以添加：经营风险）
                if (productLine == 2 && alarmType == AlarmTypeEnum.BUSINESS_RISK.getCode()){
                    errorInfoList.add(createErrorInfo(vin, "该车辆产品线为【长租】，不可以添加【经营风险】报警"));
                    continue;
                }
                // 该车报警类型是否存在
                if (isExistAlarmType(vin,alarmType)){
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("车辆：【{}】报警类型已存在", AlarmTypeEnum.getValueByCode(alarmType))));
                }
                riskAlarmList.add(buildRiskAlarm(req, assetsVehicle));
            }
            row ++;
            checkMap.put(vin, true);
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("添加风险报警上传错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,114,errorInfoList, req.getCurrentUser());
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }
        // 新增
        for (RiskAlarm riskAlarm : riskAlarmList) {
            handelRiskAlarm(riskAlarm, OPERATE_ADD.getCode(), req.getCurrentUser());
            String logContent = StrUtil.format("添加风控类型【{}】", AlarmTypeEnum.getValueByCode(alarmType));
            // 操作日志
            saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
            // 车辆日志
            saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());
        }
        return AddRiskAlarmRes.ok();
    }

    private RiskAlarm buildRiskAlarm(AddRiskAlarmReq req, AssetsVehicle assetsVehicle) {
        CurrentUser currentUser = req.getCurrentUser();
        Date now = new Date();
        RiskAlarm riskAlarm = new RiskAlarm();
        riskAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ"));
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
        riskAlarm.setAlarmSystem(AlarmSystemEnum.WTXT.getCode());
        riskAlarm.setAlarmType(req.getAlarmType());
        riskAlarm.setAlarmTime(now);
        riskAlarm.setAlarmDesc(req.getAlarmDesc());
        riskAlarm.setVin(assetsVehicle.getVin());
        riskAlarm.setCreateOperName(currentUser.getNickName());
        riskAlarm.setCreateOperAccount(currentUser.getUserAccount());
        riskAlarm.setCreateTime(now);
        riskAlarm.setOrderSeq("");
        // 设置GPS信息
        setGPSInfo(riskAlarm);
        return riskAlarm;
    }

    /**
     * 判断车辆是否有该类型的报警
     * @param vin
     * @param alarmType
     * @return
     */
    private Boolean isExistAlarmType(String vin, Integer alarmType){
        RiskAlarm riskAlarmRes = tableRiskAlarmService.queryRiskAlarm(vin,alarmType, AlarmStatusEnum.ALARM_STATUS.getCode());
        if (riskAlarmRes == null){
            return false;
        }
        return true;
    }

    @Override
    public QueryRiskAlarmListRes exportRiskAlarm(QueryRiskAlarmListReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        long total = tableRiskAlarmService.selectTotal(req);
        if (total == 0){
            throw new ServiceException(ResultCode.Fail, "没有符合条件的风控报警不能导出");
        }

        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();

        String fileName = ExcelTypeEnum.EXCEL_TYPE_1.getValue()+"_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";
        String templateFile = "template/"+ExcelTypeEnum.EXCEL_TYPE_1.getTemplate()+".xlsx";
        // 开始导出
        String fileName1 = ExcelTypeEnum.EXCEL_TYPE_1.getValue();
        String exportFilePath = FileUtils.getFilePathSuffix(fileName1,1);
        String fullExportFilePath = mfsRootPath + exportFilePath;
        String downLoadFilePath = projectDownloadUrl + exportFilePath;

        Integer exportFileId = exportFileService.startExport(FileSourceEnum.FILE_SOURCE_15.getSource(), fileName, downLoadFilePath, currentUser.getNickName(),
                currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName());
        if (exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }
        // 异步导出
        taskExecutor.execute(() ->{
            // 每页条数
            int pageSize = 5000;
            int totalPageNum = ((int)total + pageSize - 1) / pageSize;
            int pageNum = 1;
            List<RiskAlarmData> riskAlarmDataList = new ArrayList<>();
            do {
                QueryRiskAlarmListReq build = req.toBuilder().setPageNum(pageNum).setPageSize(pageSize).build();
                List<RiskAlarmData> riskAlarmList = tableRiskAlarmService.selectList(build);
                if (CollectionUtil.isEmpty(riskAlarmList)){
                    break;
                }
                for (RiskAlarmData riskAlarm : riskAlarmList) {

                    riskAlarm.setAlarmTypeDesc(AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
                    riskAlarm.setAlarmLevel(AlarmLevenEnum.getValueByCode(riskAlarm.getAlarmLevelNum()));
                    riskAlarm.setAlarmStatusDesc(AlarmStatusEnum.getValueByCode(riskAlarm.getAlarmStatus()));
                    riskAlarm.setRecoverMethodDesc(RecoverMethodEnum.getValueByCode(riskAlarm.getRecoverMethod()));
                    riskAlarm.setAlarmTimeStr(com.saicmobility.evcard.vlms.risk.utils.DateUtil.dateToString(riskAlarm.getAlarmTime(), com.saicmobility.evcard.vlms.risk.utils.DateUtil.DATE_TYPE1));
                    riskAlarm.setLastLocationTimeStr(com.saicmobility.evcard.vlms.risk.utils.DateUtil.dateToString(riskAlarm.getLastLocationTime(), com.saicmobility.evcard.vlms.risk.utils.DateUtil.DATE_TYPE1));
                    riskAlarm.setIsStopAlarmDesc(IsStopAlarmEnum.getValueByCode(riskAlarm.getIsStopAlarm()));
                    riskAlarm.setPauseDeadlineTimeStr(com.saicmobility.evcard.vlms.risk.utils.DateUtil.dateToString(riskAlarm.getPauseDeadlineTime(), com.saicmobility.evcard.vlms.risk.utils.DateUtil.DATE_TYPE1));
                    riskAlarm.setRecoveryDateStr(com.saicmobility.evcard.vlms.risk.utils.DateUtil.dateToString(riskAlarm.getRecoveryDate(), com.saicmobility.evcard.vlms.risk.utils.DateUtil.DATE_TYPE1));
                    riskAlarm.setVehicleRiskStatusDesc(VehicleRiskStatusEnum.getValueByCode(riskAlarm.getVehicleRiskStatus()));
                    riskAlarm.setOperateOrgName(Global.instance.configLoader.getOrgName(riskAlarm.getOperateOrgId()));
                    riskAlarm.setOrgName(Global.instance.configLoader.getOrgName(riskAlarm.getOrgId()));
                    riskAlarm.setAlarmSystemDesc(AlarmSystemEnum.getValueByCode(riskAlarm.getAlarmSystem()));
                    riskAlarm.setRecoverChannelDesc(AlarmSystemEnum.getValueByCode(riskAlarm.getRecoverChannel()));

                    // 资产字段
                    AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskAlarm.getVin());
                    if (vehicleInfoByVin != null){
                        riskAlarm.setStoreName(getStoreName(vehicleInfoByVin.getStoreId()));
                        riskAlarm.setProductLineDesc(ProductLineEnum.getValueByCode(vehicleInfoByVin.getProductLine()));
                        riskAlarm.setSubProductLineDesc(SubProductLineEnum.getValueByCode(vehicleInfoByVin.getSubProductLine()));
                        riskAlarm.setPropertyStatusDesc(PropertyStatusEnums.getValueByCode(vehicleInfoByVin.getPropertyStatus()));
                        riskAlarm.setPropertyStatusDesc(PropertyStatusEnums.getValueByCode(vehicleInfoByVin.getPropertyStatus()));
                    }
                    // 查询风控报警条数
                    riskAlarm.setAlarmNum(0);
                    if (riskAlarm.getAlarmStatus() == AlarmStatusEnum.ALARM_STATUS.getCode()){
                        List<RiskAlarm> riskAlarmIngList = tableRiskAlarmService.selectAlarmVin(riskAlarm.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
                        riskAlarm.setAlarmNum(riskAlarmIngList.size());
                    }
                }
                riskAlarmDataList.addAll(riskAlarmList);
                pageNum ++;
            }while (totalPageNum >= pageNum);

            //如果目录不存在，则创建目录
            File file = new File(fullExportFilePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            boolean noResult = ObjectUtil.isNull(riskAlarmDataList);
            try (
                    InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream(templateFile);
                    FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                    ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();

            ) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                //无数据处理
                if(noResult){
                    excelWriter.fill(new ArrayList<>(), writeSheet);
                }else {
                    excelWriter.fill(riskAlarmDataList, writeSheet);
                }
                fileOutputStream.flush();
                excelWriter.finish();
                //导出成功
                exportFileService.exportSuccess(exportFileId, currentUser.getNickName());
            }catch (Exception e) {
                e.printStackTrace();
                log.error(e.getLocalizedMessage(), e);
                //导出失败
                exportFileService.exportFail(exportFileId, e.getLocalizedMessage(), currentUser.getNickName());
            }
        });
        return QueryRiskAlarmListRes.ok();
    }

    @Override
    public QueryRiskAlarmDetailRes queryRiskAlarmDetail(QueryRiskAlarmDetailReq req) {
        QueryRiskAlarmDetailRes.Builder builder = QueryRiskAlarmDetailRes.newBuilder();
        RiskAlarm riskAlarm = tableRiskAlarmService.selectById(req.getId());
        if (riskAlarm == null){
            throw new ServiceException("风控报警不存在");
        }
        RiskAlarmData riskAlarmData = new RiskAlarmData();
        BeanUtils.copyProperties(riskAlarm, riskAlarmData);
        riskAlarmData.setAlarmLevel(AlarmLevenEnum.getValueByCode(riskAlarm.getAlarmLevel()));

        // 资产字段
        AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskAlarm.getVin());
        if (vehicleInfoByVin != null){
            riskAlarmData.setStoreName(getStoreName(vehicleInfoByVin.getStoreId()));
            riskAlarmData.setProductLine(vehicleInfoByVin.getProductLine());
            riskAlarmData.setSubProductLine(vehicleInfoByVin.getSubProductLine());
            riskAlarmData.setPropertyStatus(vehicleInfoByVin.getPropertyStatus());
            riskAlarmData.setVehicleRiskStatus(vehicleInfoByVin.getVehicleRiskStatus());
            riskAlarmData.setPlateNo(vehicleInfoByVin.getPlateNo());
            riskAlarmData.setOrgName(Global.instance.configLoader.getOrgName(vehicleInfoByVin.getPropertyOrgId()));
            riskAlarmData.setOperateOrgName(Global.instance.configLoader.getOrgName(vehicleInfoByVin.getOperationOrgId()));
        }

        // 查询风控报警条数
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskAlarm.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
        riskAlarmData.setAlarmNum(riskAlarmList.size());
        builder.setRiskAlarm(PbConvertUtil.generateProtoBuffer(riskAlarmData, RiskAlarmInfo.class));

        // 查询报警升级附件列表
        List<Attachment> attachmentList = queryAttachment(2, String.valueOf(riskAlarm.getId()));
        if (CollectionUtil.isNotEmpty(attachmentList)){
            List<AttachmentInfo> upgradeAttachmentPath = new ArrayList<>();
            for (Attachment attachment : attachmentList) {
                AttachmentInfo attachmentInfo = AttachmentInfo.newBuilder().setFilePath(attachment.getFilePath()).setFileName(attachment.getFileName()).build();
                upgradeAttachmentPath.add(attachmentInfo);
            }
            builder.addAllUpgradeAttachmentPath(upgradeAttachmentPath);
        }
        // 查询解除报警附件列表
        attachmentList = queryAttachment(1, String.valueOf(riskAlarm.getId()));
        if (CollectionUtil.isNotEmpty(attachmentList)){
            List<AttachmentInfo> removeAttachmentPath = new ArrayList<>();
            for (Attachment attachment : attachmentList) {
                AttachmentInfo attachmentInfo = AttachmentInfo.newBuilder().setFilePath(attachment.getFilePath()).setFileName(attachment.getFileName()).build();
                removeAttachmentPath.add(attachmentInfo);
            }
            builder.addAllRemoveAttachmentPath(removeAttachmentPath);
        }
        return builder.build();
    }

    @Transactional
    @Override
    public PauseRiskAlarmRes pauseRiskAlarm(PauseRiskAlarmReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        RiskAlarm riskAlarm = tableRiskAlarmService.selectById(req.getId());
        if (riskAlarm == null){
            throw new ServiceException("风控报警不存在");
        }
        if (riskAlarm.getAlarmSystem() == 2 && riskAlarm.getAlarmType().equals(AlarmTypeEnum.BUSINESS_RISK.getCode())){
            throw new ServiceException("长租系统创建的经营风险报警不可以暂停");
        }
        int stopAlarmDay = req.getStopAlarmDay();
        if (stopAlarmDay < 1){
            throw new ServiceException("暂停时间必须是正整数");
        }
        if (riskAlarm.getAlarmLevel() != AlarmLevenEnum.ONE.getCode()){
            throw new ServiceException("2级及以上报警不可以暂停");
        }

        //判断暂停天数是否大于最大限制天数
        judgeMaxAlarmDay(riskAlarm.getAlarmType(), req.getStopAlarmDay());
        // 给当前时间加上暂停天数
        DateTime pauseDeadlineTime = DateUtil.offsetDay(new Date(), stopAlarmDay);

        // 修改风控报警
        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.YES.getCode());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        riskAlarm.setStopAlarmDay(stopAlarmDay);
        riskAlarm.setPauseDeadlineTime(pauseDeadlineTime);
        riskAlarm.setRecoveryDate(new Date());
        riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
        riskAlarm.setMiscDesc(req.getMiscDesc());
        riskAlarm.setUpdateOperName(req.getCurrentUser().getNickName());
        riskAlarm.setUpdateOperAccount(req.getCurrentUser().getUserAccount());
        tableRiskAlarmService.update(riskAlarm);

        //  自动完成车辆巡查任务
        riskCommonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);

        // 删除redis中的数据
        if (riskAlarm.getAlarmType().equals(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
            Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + riskAlarm.getVin());
        }

        // 修改风控报警配置
        Long config = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), null, stopAlarmDay, pauseDeadlineTime, currentUser);
        saveOperateLog("修改报警升级暂停截止时间", config+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

        // 日志
        String logContent = StrUtil.format("暂停报警,报警类型:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
        if (StringUtils.isNotBlank(req.getMiscDesc())){
            logContent += StrUtil.format(", 备注:{}", req.getMiscDesc());
        }
        saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
        saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());

        // 通知长租
        subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
        return PauseRiskAlarmRes.ok();
    }

    /**
     * 判断暂停天数是否大于最大限制天数
     * @param alarmType
     * @param stopAlarmDay
     */
    private void judgeMaxAlarmDay(Integer alarmType, int stopAlarmDay) {
        PauseAlarmConfig pauseAlarmConfig = tablePauseAlarmConfigService.selectByAlarmType(alarmType);
        if (stopAlarmDay > pauseAlarmConfig.getMaxPauseDay()){
            throw new ServiceException("暂停天数不能大于最长暂停时长");
        }
    }

    @Transactional
    @Override
    public UpgradeRiskAlarmDescRes upgradeRiskAlarmDesc(UpgradeRiskAlarmDescReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        RiskAlarm riskAlarm = tableRiskAlarmService.selectById(req.getId());
        if (riskAlarm == null){
            throw new ServiceException("风控报警不存在");
        }
        if (!UPGRADE_ALARM_TYPE_LIST.contains(riskAlarm.getAlarmType())){
            throw new ServiceException("该报警类型不支持升级");
        }

        List<AttachmentInfo> attachmentInfoList = req.getAttachmentPathsList();
        // 更新
        riskAlarm.setUpgradeDesc(req.getUpdateDesc());
        riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
        riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
        riskAlarm.setUpdateOperName(currentUser.getNickName());
        // 获取GPS车辆信息
        setGPSInfo(riskAlarm);
        // 更新逻辑
        handelRiskAlarm(riskAlarm, OPERATE_UPGRADE.getCode(), currentUser);

        // 日志
        String logContent = "报警升级";
        if (StringUtils.isNotBlank(req.getUpdateDesc())){
            logContent += StrUtil.format(", 升级说明:{}", req.getUpdateDesc());
        }
        saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
        saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());
        // 附件存表
        String relationId = String.valueOf(riskAlarm.getId());
        batchUploadAttachment(attachmentInfoList,2,relationId,req.getCurrentUser());
        return UpgradeRiskAlarmDescRes.ok();
    }


    /**
     * 设置GPS信息
     * @param riskAlarm
     */
    public void setGPSInfo(RiskAlarm riskAlarm){
        VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(riskAlarm.getVin());
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
    }

    @Transactional
    @Override
    public RemoveRiskAlarmRes removeRiskAlarm(RemoveRiskAlarmReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        RiskAlarm riskAlarm = tableRiskAlarmService.selectById(req.getId());
        if (riskAlarm == null){
            throw new ServiceException("风控报警不存在");
        }
        // 非自动恢复
        if (req.getAutoFlag() != 1 && riskAlarm.getAlarmSystem() == 2 && riskAlarm.getAlarmType().equals(AlarmTypeEnum.BUSINESS_RISK.getCode())){
            throw new ServiceException("长租系统创建的经营风险报警不可以解除");
        }
        List<AttachmentInfo> attachmentInfoList = req.getAttachmentPathsList();
        if (CollectionUtil.isNotEmpty(attachmentInfoList)){
            String relationId = String.valueOf(riskAlarm.getId());
            batchUploadAttachment(attachmentInfoList,1,relationId,req.getCurrentUser());
        }

        // 更新
        riskAlarm.setMiscDesc(req.getMiscDesc());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
        riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
        riskAlarm.setRecoveryDate(new Date());
        riskAlarm.setUpdateOperName(currentUser.getNickName());
        riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
        //tableRiskAlarmService.update(riskAlarm);

        //  自动完成车辆巡查任务
        riskCommonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);

        // 修改风控报警配置
        handelRiskAlarm(riskAlarm, OPERATE_REMOVE.getCode(), currentUser);
        Long aLong = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), new Date(), null, null, currentUser);
        saveOperateLog("解除报警， 更新报警开始时间", aLong+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

        // 同步更新车辆风控状态
        subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo()));

        // 日志
        String logContent = StrUtil.format("解除报警,报警类型:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
        if (StringUtils.isNotBlank(req.getMiscDesc())){
            logContent += StrUtil.format(", 备注:{}", req.getMiscDesc());
        }
        saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
        saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());

        // 通知长租
        subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
        return RemoveRiskAlarmRes.ok();
    }

    @Override
    public QueryVehicleRiskAlarmTypeRes queryVehicleRiskAlarmType(QueryVehicleRiskAlarmTypeReq req) {
        QueryVehicleRiskAlarmTypeResponse response = new QueryVehicleRiskAlarmTypeResponse();
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(req.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
        if (CollectionUtil.isNotEmpty(riskAlarmList)){
            List<RiskAlarmData> riskAlarmDataList = new ArrayList<>();
            for (RiskAlarm riskAlarm : riskAlarmList) {
                if (riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                    continue;
                }
                RiskAlarmData riskAlarmData = new RiskAlarmData();
                BeanUtils.copyProperties(riskAlarm, riskAlarmData);
                riskAlarmData.setAlarmLevel(AlarmLevenEnum.getValueByCode(riskAlarm.getAlarmLevel()));
                riskAlarmDataList.add(riskAlarmData);
            }
            String vin = riskAlarmList.get(0).getVin();
            response.setVin(vin);
            AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(vin);
            response.setPlateNo(vehicleInfoByVin.getPlateNo());
            response.setList(riskAlarmDataList);
        }
        return PbConvertUtil.generateProtoBuffer(response, QueryVehicleRiskAlarmTypeRes.class);
    }

    @Override
    public RiskAlarm queryDataByVinAndAlarmType(String vin, Integer alarmType) {
        return tableRiskAlarmService.queryDataByVinAndType(vin,alarmType);
    }

    @Override
    public void handelRiskAlarm(RiskAlarm riskAlarm, Integer type, CurrentUser currentUser) {
        RiskOperateEnum riskOperateEnum = getEnumByCode(type);
        switch (riskOperateEnum){
            case OPERATE_ADD:
                operateAdd(riskAlarm, currentUser);
                break;
            case OPERATE_UPGRADE:
                operateUpgrade(riskAlarm, currentUser);
                break;
            case OPERATE_REMOVE:
                operateRemove(riskAlarm, currentUser);
                break;
            default:
                break;
        }
    }

    @Override
    public BatchPauseRiskAlarmRes batchPauseRiskAlarm(BatchPauseRiskAlarmReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        // 解析excel
        List<ImportPauseAlarmDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, ImportPauseAlarmDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 500){
            throw new ServiceException("单次上传限制500条");
        }
        // 报警
        List<RiskAlarm> riskAlarmList = new ArrayList<>();
        // 报警配置
        List<RiskAlarmConfig> riskAlarmConfigList = new ArrayList<>();
        // 错误信息
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();

        int row = 0;
        for (ImportPauseAlarmDemo importVehicleDemo : vehicleDemoList) {
            row ++;
            // 车架号
            String vin = importVehicleDemo.getVin();
            if (StringUtils.isBlank(vin)){
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }
            Boolean doFlag = true;
            // 报警类型判断
            String alarmType = importVehicleDemo.getAlarmType();
            if (StringUtils.isBlank(alarmType)){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报警类型为空", row)));
            }else {
                Integer alarmCode = AlarmTypeEnum.getCodeByValue(alarmType);
                if (alarmCode == null){
                    doFlag = false;
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("报警类型【{}】不存在", alarmType)));
                }
            }
            String stopAlarmDay = importVehicleDemo.getStopAlarmDay();
            if (StringUtils.isBlank(stopAlarmDay)){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报警暂停天数为空", row)));
            }else {
                if (!BaseUtils.isNumeric(stopAlarmDay)){
                    doFlag = false;
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报暂停天数不是正整数", row)));
                }
            }
            String miscDesc = importVehicleDemo.getMiscDesc();
            if (StringUtils.isNotBlank(miscDesc) && miscDesc.length() > 500){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "备注长度不能超过500"));
            }
            // 如果有字段为空或不符合规则
            if (!doFlag){
                continue;
            }
            Integer stopAlarmDayNum = Integer.valueOf(stopAlarmDay);
            Integer alarmCode = AlarmTypeEnum.getCodeByValue(alarmType);
            // 进行中的报警判断
            RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, alarmCode);
            if (riskAlarm == null){
                errorInfoList.add(createErrorInfo(vin,  StrUtil.format("没有进行中的【{}】报警", alarmType)));
                continue;
            }else{
                // 非自动恢复
                if (riskAlarm.getAlarmSystem() == 2 && riskAlarm.getAlarmType().equals(AlarmTypeEnum.BUSINESS_RISK.getCode())){
                    errorInfoList.add(createErrorInfo(vin, "车辆存在长租系统创建的【经营风险】报警，不可暂停"));
                }
                if (riskAlarm.getAlarmLevel() != AlarmLevenEnum.ONE.getCode()){
                    errorInfoList.add(createErrorInfo(vin,"2级及以上报警不可以暂停"));
                }
                if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
                    errorInfoList.add(createErrorInfo(vin,StrUtil.format("车辆【{}】报警已恢复，不可暂停", alarmType)));
                }
                if (riskAlarm.getIsStopAlarm().equals(IsStopAlarmEnum.YES.getCode())){
                    errorInfoList.add(createErrorInfo(vin,StrUtil.format("车辆【{}】报警已暂停，不可暂停", alarmType)));
                }
                PauseAlarmConfig pauseAlarmConfig = tablePauseAlarmConfigService.selectByAlarmType(alarmCode);
                if (stopAlarmDayNum > pauseAlarmConfig.getMaxPauseDay()){
                    errorInfoList.add(createErrorInfo(vin,"暂停天数不能大于最长暂停时长"));
                    continue;
                }
            }
            //判断暂停天数是否大于最大限制天数
            judgeMaxAlarmDay(riskAlarm.getAlarmType(), Integer.valueOf(importVehicleDemo.getStopAlarmDay()));
            // 给当前时间加上暂停天数
            DateTime pauseDeadlineTime = DateUtil.offsetDay(new Date(), stopAlarmDayNum);
            // 组装报警数据
            riskAlarm.setIsStopAlarm(IsStopAlarmEnum.YES.getCode());
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setStopAlarmDay(stopAlarmDayNum);
            riskAlarm.setPauseDeadlineTime(pauseDeadlineTime);
            riskAlarm.setRecoveryDate(new Date());
            riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
            riskAlarm.setMiscDesc(miscDesc);
            riskAlarm.setUpdateOperName(currentUser.getNickName());
            riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
            riskAlarmList.add(riskAlarm);

            // 组装风控报警配置
            RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
            riskAlarmConfig.setVin(vin);
            riskAlarmConfig.setAlarmType(alarmCode);
            riskAlarmConfig.setPauseDay(stopAlarmDayNum);
            riskAlarmConfig.setPauseDeadlineTime(pauseDeadlineTime);
            riskAlarmConfig.setCreateOperName(currentUser.getNickName());
            riskAlarmConfig.setCreateOperAccount(currentUser.getUserAccount());
            riskAlarmConfigList.add(riskAlarmConfig);
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("批量暂停风险报警上传错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,114, errorInfoList, currentUser);
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }
        List<VehicleOperateLog> vehicleOperateLogList = new ArrayList<>();
        // 更新报警
        for (RiskAlarm riskAlarm : riskAlarmList) {
            tableRiskAlarmService.update(riskAlarm);

            //  自动完成车辆巡查任务
            riskCommonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);

            // 删除redis中的数据
            if (riskAlarm.getAlarmType().equals(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
                Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + riskAlarm.getVin());
            }
            // 日志
            String logContent = StrUtil.format("暂停报警,报警类型:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
            if (StringUtils.isNotBlank(riskAlarm.getMiscDesc())){
                logContent += StrUtil.format(", 备注:{}", riskAlarm.getMiscDesc());
            }
            // 日志记录
            saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);

            // 车辆日志
            VehicleOperateLog vehicleOperateLog = new VehicleOperateLog();
            vehicleOperateLog.setVin(riskAlarm.getVin());
            vehicleOperateLog.setLogContent(logContent);
            vehicleOperateLogList.add(vehicleOperateLog);

            // 通知长租
            subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
        }
        // 记录车辆操作日至
        addVehicleOperateLog(vehicleOperateLogList, currentUser);
        // 更新报警配置
        for (RiskAlarmConfig alarmConfig : riskAlarmConfigList) {
            Long config = riskAlarmConfigService.updateRiskAlarmConfig(alarmConfig.getVin(), alarmConfig.getAlarmType(), null, alarmConfig.getPauseDay(), alarmConfig.getPauseDeadlineTime(), currentUser);
            saveOperateLog("修改报警升级暂停截止时间", config+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);
        }
        return BatchPauseRiskAlarmRes.ok();
    }

    @Override
    public BatchRemoveRiskAlarmRes batchRemoveRiskAlarm(BatchRemoveRiskAlarmReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        // 解析excel
        List<ImportRemoveAlarmDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, ImportRemoveAlarmDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 5000){
            throw new ServiceException("单次上传限制5000条");
        }
        List<AttachmentInfo> attachmentInfoList = req.getAttachmentPathsList();
        if (CollectionUtil.isEmpty(attachmentInfoList)){
            throw new ServiceException("请上传附件");
        }
        // 报警
        List<RiskAlarm> riskAlarmList = new ArrayList<>();
        // 报警配置
        List<RiskAlarmConfig> riskAlarmConfigList = new ArrayList<>();
        // 错误信息
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();

        int row = 1;
        for (ImportRemoveAlarmDemo importVehicleDemo : vehicleDemoList) {
            // 车架号
            String vin = importVehicleDemo.getVin();
            if (StringUtils.isBlank(vin)){
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }
            Boolean doFlag = true;
            // 报警类型判断
            String alarmType = importVehicleDemo.getAlarmType();
            if (StringUtils.isBlank(alarmType)){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报警类型为空", row)));
            }else {
                Integer alarmCode = AlarmTypeEnum.getCodeByValue(alarmType);
                if (alarmCode == null){
                    doFlag = false;
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("报警类型【{}】不存在", alarmType)));
                }
            }
            String miscDesc = importVehicleDemo.getMiscDesc();
            if (StringUtils.isNotBlank(miscDesc) && miscDesc.length() > 500){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "备注长度不能超过500"));
            }
            // 如果有字段为空或不符合规则
            if (!doFlag){
                continue;
            }

            Integer alarmCode = AlarmTypeEnum.getCodeByValue(alarmType);
            // 进行中的报警判断
            RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, alarmCode);
            if (riskAlarm == null){
                errorInfoList.add(createErrorInfo(vin,  StrUtil.format("没有进行中的【{}】报警", alarmType)));
                continue;
            }else {
                // 非自动恢复
                if (riskAlarm.getAlarmSystem() == 2 && riskAlarm.getAlarmType().equals(AlarmTypeEnum.BUSINESS_RISK.getCode())){
                    errorInfoList.add(createErrorInfo(vin, "车辆存在长租系统创建的【经营风险】报警，不可解除"));
                }
                if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
                    errorInfoList.add(createErrorInfo(vin,StrUtil.format("车辆【{}】报警已恢复，不可解除", alarmType)));
                }
                if (riskAlarm.getIsStopAlarm().equals(IsStopAlarmEnum.YES.getCode())){
                    errorInfoList.add(createErrorInfo(vin,StrUtil.format("车辆【{}】报警已暂停，不可解除", alarmType)));
                }
            }

            // 更新
            riskAlarm.setMiscDesc(miscDesc);
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
            riskAlarm.setRecoveryDate(new Date());
            riskAlarm.setUpdateOperName(currentUser.getNickName());
            riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
            riskAlarmList.add(riskAlarm);

            // 组装风控报警配置
            RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
            riskAlarmConfig.setVin(vin);
            riskAlarmConfig.setAlarmType(alarmCode);
            riskAlarmConfig.setCreateOperName(currentUser.getNickName());
            riskAlarmConfig.setCreateOperAccount(currentUser.getUserAccount());
            riskAlarmConfigList.add(riskAlarmConfig);

            row ++;
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("批量解除风险报警上传错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,114, errorInfoList, currentUser);
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }
        for (RiskAlarm riskAlarm : riskAlarmList) {
            //  自动完成车辆巡查任务
            riskCommonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);
            // 修改风控报警配置
            handelRiskAlarm(riskAlarm, OPERATE_REMOVE.getCode(), currentUser);
            Long aLong = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), new Date(), null, null, currentUser);
            saveOperateLog("解除报警， 更新报警开始时间", aLong+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

            // 同步更新车辆风控状态
            subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo()));

            // 日志
            String logContent = StrUtil.format("解除报警,报警类型:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
            if (StringUtils.isNotBlank(riskAlarm.getMiscDesc())){
                logContent += StrUtil.format(", 备注:{}", riskAlarm.getMiscDesc());
            }
            saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
            saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());

            //保存附件
            batchUploadAttachment(attachmentInfoList,1, String.valueOf(riskAlarm.getId()),req.getCurrentUser());

            // 通知长租
            subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
        }
        return BatchRemoveRiskAlarmRes.ok();
    }


    @Override
    public UpgradeRiskAlarmDescRes batchUpgradeRiskAlarmDesc(BatchUpgradeRiskAlarmDescReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        // 解析excel
        List<ImportRemoveAlarmDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, ImportRemoveAlarmDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 5000){
            throw new ServiceException("单次上传限制5000条");
        }
        List<AttachmentInfo> attachmentInfoList = req.getAttachmentPathsList();
//        if (CollectionUtil.isEmpty(attachmentInfoList)){
//            throw new ServiceException("请上传附件");
//        }
        // 报警配置
        List<RiskAlarm> riskAlarmList = new ArrayList<>();
        // 错误信息
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();

        int row = 1;
        for (ImportRemoveAlarmDemo importVehicleDemo : vehicleDemoList) {
            // 车架号
            String vin = importVehicleDemo.getVin();
            if (StringUtils.isBlank(vin)){
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }
            Boolean doFlag = true;
            // 报警类型判断
            String alarmType = importVehicleDemo.getAlarmType();
            if (StringUtils.isBlank(alarmType)){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报警类型为空", row)));
            }else {
                Integer alarmCode = AlarmTypeEnum.getCodeByValue(alarmType);
                if (alarmCode == null){
                    doFlag = false;
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报警类型【{}】不存在", row,alarmType)));
                } else {
                    if (!UPGRADE_ALARM_TYPE_LIST.contains(alarmCode)){
                        errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行报警类型【{}】不支持升级", row,alarmType)));
                    }
                }
            }
            String miscDesc = importVehicleDemo.getMiscDesc();
            if (StringUtils.isNotBlank(miscDesc) && miscDesc.length() > 500){
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "备注长度不能超过500"));
            }
            // 如果有字段为空或不符合规则
            if (!doFlag){
                continue;
            }
            Integer alarmCode = AlarmTypeEnum.getCodeByValue(alarmType);
            // 进行中的报警判断
            RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, alarmCode);
            if (riskAlarm == null){
                errorInfoList.add(createErrorInfo(vin,  StrUtil.format("没有进行中的【{}】报警", alarmType)));
                continue;
            }else {
                if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
                    errorInfoList.add(createErrorInfo(vin,StrUtil.format("车辆【{}】报警已恢复，不可升级", alarmType)));
                }
                if (riskAlarm.getIsStopAlarm().equals(IsStopAlarmEnum.YES.getCode())){
                    errorInfoList.add(createErrorInfo(vin,StrUtil.format("车辆【{}】报警已暂停，不可升级", alarmType)));
                }
                int alarmLevel = riskAlarm.getAlarmLevel();
                if (alarmLevel == 1){
                    riskAlarm.setUpgradeDesc(importVehicleDemo.getMiscDesc());
                    riskAlarmList.add(riskAlarm);
                }
            }
            row ++;
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("批量升级风险报警上传错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,125, errorInfoList, currentUser);
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }

        if (CollectionUtil.isNotEmpty(riskAlarmList)){
            for (RiskAlarm riskAlarm : riskAlarmList) {
                riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
                riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
                riskAlarm.setUpdateOperName(currentUser.getNickName());
                // 获取GPS车辆信息
                setGPSInfo(riskAlarm);
                // 更新逻辑
                handelRiskAlarm(riskAlarm, OPERATE_UPGRADE.getCode(), currentUser);
                // 日志
                String logContent = "报警升级";
                if (StringUtils.isNotBlank(riskAlarm.getUpgradeDesc())){
                    logContent += StrUtil.format(", 升级说明:{}", riskAlarm.getUpgradeDesc());
                }
                saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), req.getCurrentUser());
                saveVehicleLog(riskAlarm.getVin(), logContent, req.getCurrentUser());
                // 附件存表
                String relationId = String.valueOf(riskAlarm.getId());
                batchUploadAttachment(attachmentInfoList,2,relationId,req.getCurrentUser());
            }
        }
        return UpgradeRiskAlarmDescRes.ok();
    }

    @Override
    @Transactional
    public FinishWorkOrderRes finishWorkOrder(FinishWorkOrderReq req) {
        CurrentUser currentUser = CurrentUser.newBuilder().setNickName(req.getUserName()).build();
        String workOrderNo = req.getWorkOrderNo();
        log.info("finishWorkOrder param：tid:{}, 工单号:{}", workOrderNo, Trace.currentTraceId());
        if (StringUtils.isBlank(workOrderNo)){
            throw new ServiceException("工单号不能为空");
        }
        RiskAlarm riskAlarm = tableRiskAlarmService.queryRiskAlarmByAlarmNo(workOrderNo);
        if (riskAlarm == null){
            throw new ServiceException("工单不存在");
        }
        if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
            throw new ServiceException("该工单已恢复，无需处理");
        }
        String vin = riskAlarm.getVin();
        if (req.getRiskStatus() == 1){
            // “风控车，需要收车
            // 该车报警类型是否存在
            RiskAlarm riskAlarmRes = tableRiskAlarmService.queryRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), AlarmStatusEnum.ALARM_STATUS.getCode());
            // 如果不存在则新增添加经营风险风控
            if (riskAlarmRes == null){
                // 构建 经营风险报警对象
                longrentCreateRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), req.getRemark(), currentUser);
            }else if (!riskAlarmRes.getAlarmSystem().equals(AlarmSystemEnum.CZXT.getCode())){
                // 解除老的报警
                removeAlarm(riskAlarmRes, req.getRemark(), currentUser);
                // 构建 经营风险报警对象
                longrentCreateRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), req.getRemark(), currentUser);
            }
        }else {
            // 非风险车，解除预警
            int stopAlarmDay = req.getAlarmCycle();
            if (stopAlarmDay < 1){
                throw new ServiceException("暂停时间必须是正整数");
            }
            //判断暂停天数是否大于最大限制天数
            judgeMaxAlarmDay(riskAlarm.getAlarmType(), stopAlarmDay);
            // 给当前时间加上暂停天数
            DateTime pauseDeadlineTime = DateUtil.offsetDay(new Date(), stopAlarmDay);

            // 修改风控报警
            riskAlarm.setIsStopAlarm(IsStopAlarmEnum.YES.getCode());
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setStopAlarmDay(stopAlarmDay);
            riskAlarm.setPauseDeadlineTime(pauseDeadlineTime);
            riskAlarm.setRecoveryDate(new Date());
            riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
            riskAlarm.setRecoverChannel(AlarmSystemEnum.CZXT.getCode());
            riskAlarm.setMiscDesc(req.getRemark());
            riskAlarm.setUpdateOperName("长租");
            riskAlarm.setUpdateOperAccount("长租系统");
            tableRiskAlarmService.update(riskAlarm);

            //  自动完成车辆巡查任务
            riskCommonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);

            // 修改风控报警配置
            Long config = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), null, stopAlarmDay, pauseDeadlineTime, currentUser);
            saveOperateLog("长租发起【非风险车，解除预警】，修改报警升级暂停截止时间", config+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

            // 日志内容
            String logContent = StrUtil.format("长租发起【非风险车，解除预警】，暂停报警,报警类型:【{}】,暂停时间:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()), stopAlarmDay+"天");
            if (StringUtils.isNotBlank(req.getRemark())){
                logContent += StrUtil.format(", 备注:{}", req.getRemark());
            }
            // 通知长租
            //subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
            // 操作日志
            saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);
            // 车辆日志
            saveVehicleLog(riskAlarm.getVin(), logContent, currentUser);
        }
        // 删除redis中的数据
        if (riskAlarm.getAlarmType().equals(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
            Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + riskAlarm.getVin());
        }

        return FinishWorkOrderRes.ok();
    }


    /**
     * 长租新增风险报警
     * @param vin 车架号
     * @param alarmType 报警类型
     * @param remark    备注
     * @param currentUser 当前用户
     */
    public void longrentCreateRiskAlarm(String vin, Integer alarmType, String remark, CurrentUser currentUser){
        // 构建 经营风险报警对象
        RiskAlarm newRiskAlarm = buildRiskAlarm(vin, alarmType, remark, AlarmSystemEnum.CZXT.getCode(), currentUser);
        // 处理逻辑
        handelRiskAlarm(newRiskAlarm, OPERATE_ADD.getCode(), currentUser);
        String logContent = "长租系统操作，风控车，需要收车，新增经营风险报警";
        if (StringUtils.isNotBlank(remark)){
            logContent += StrUtil.format(", 备注:{}", remark);
        }
        // 操作日志
        saveOperateLog(logContent, newRiskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);
        // 车辆日志
        saveVehicleLog(newRiskAlarm.getVin(), logContent, currentUser);
    }

    @Override
    public AddRiskCheckForReturnCarRes addRiskCheckForReturnCar(AddRiskCheckForReturnCarReq req) {
        CurrentUser currentUser = CurrentUser.newBuilder().setNickName("长租").setUserAccount("长租系统").build();
        String vin = req.getVin();
        RiskAlarm riskAlarmRes = tableRiskAlarmService.queryRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), AlarmStatusEnum.ALARM_STATUS.getCode());
        if (riskAlarmRes == null){
            longrentCreateRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), req.getRemark(), currentUser);
        }else if (!riskAlarmRes.getAlarmSystem().equals(AlarmSystemEnum.CZXT.getCode())){
            // 非长租创建的，解除风险
            removeAlarm(riskAlarmRes, req.getRemark(), currentUser);
            // 新增新的经营风险
            longrentCreateRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), req.getRemark(), currentUser);
        }
        return AddRiskCheckForReturnCarRes.ok();
    }

    public void removeAlarm(RiskAlarm riskAlarm, String remark, CurrentUser currentUser){
        // 解除风险
        handelRiskAlarm(riskAlarm, OPERATE_REMOVE.getCode(), currentUser);
        // 更新
        riskAlarm.setMiscDesc(remark);
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
        riskAlarm.setRecoveryDate(new Date());
        riskAlarm.setUpdateOperName(currentUser.getNickName());
        riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());

        //  自动完成车辆巡查任务
        riskCommonService.autoCompleteSingleVehiclePatrolTask(riskAlarm);

        // 修改风控报警配置
        handelRiskAlarm(riskAlarm, OPERATE_REMOVE.getCode(), currentUser);
        Long aLong = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), new Date(), null, null, currentUser);
        saveOperateLog("解除报警， 更新报警开始时间", aLong+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

        // 同步更新车辆风控状态
        subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo()));

        // 日志
        String logContent = StrUtil.format("解除报警,报警类型:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
        if (StringUtils.isNotBlank(remark)){
            logContent += StrUtil.format(", 备注:{}", remark);
        }
        saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);
        saveVehicleLog(riskAlarm.getVin(), logContent, currentUser);
    }

    @Override
    @Transactional
    public CancelWorkOrderRes cancelWorkOrder(CancelWorkOrderReq req) {
        CancelWorkOrderRes.Builder builder = CancelWorkOrderRes.newBuilder();
        CurrentUser currentUser = CurrentUser.newBuilder().setNickName(req.getApplyUser()).setUserAccount("长租系统").build();
        String plateNo = req.getPlateNo();
        if (StringUtils.isBlank(plateNo)){
            throw new ServiceException("车牌号不能为空");
        }
        GetVehicleInfoByVehicleNoRes vehicleInfoByVehicleNo = mdDataProxy.getVehicleInfoByVehicleNo(GetVehicleInfoByVehicleNoReq.newBuilder().setVehicleNo(plateNo).build());
        if (vehicleInfoByVehicleNo == null){
            throw new ServiceException("未查询到该车辆信息");
        }
        // 通知长租取消的风险
        List<String> notifyCanceAlarmNo = Lists.newArrayList();
        String vin = vehicleInfoByVehicleNo.getVin();
        // 解除该车辆【经营风险-风控预警】
        //RiskAlarm riskAlarm = tableRiskAlarmService.queryRiskAlarm(vin, AlarmTypeEnum.BUSINESS_RISK.getCode(), AlarmStatusEnum.ALARM_STATUS.getCode());
        List<RiskAlarm> riskAlarms = tableRiskAlarmService.selectAlarmVin(vin, AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
        if (CollectionUtil.isNotEmpty(riskAlarms)){
            for (RiskAlarm riskAlarm : riskAlarms) {
                // 附件处理
                List<AttachmentInfo> applyAttachmentsList = req.getApplyAttachmentsList();
                if (CollectionUtil.isNotEmpty(applyAttachmentsList)){
                    String relationId = String.valueOf(riskAlarm.getId());
                    batchUploadAttachment(applyAttachmentsList,1,relationId, currentUser);
                }
                // 更新
                riskAlarm.setMiscDesc(req.getCancelReason());
                riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
                riskAlarm.setRecoverMethod(RecoverMethodEnum.RECOVER_METHOD.getCode());
                riskAlarm.setRecoverChannel(AlarmSystemEnum.CZXT.getCode());
                riskAlarm.setRecoveryDate(new Date());
                riskAlarm.setUpdateOperName(currentUser.getNickName());
                riskAlarm.setUpdateOperAccount(currentUser.getUserAccount());
                //tableRiskAlarmService.update(riskAlarm);

                // 修改风控报警配置
                handelRiskAlarm(riskAlarm, OPERATE_REMOVE.getCode(), currentUser);
                Long aLong = riskAlarmConfigService.updateRiskAlarmConfig(riskAlarm.getVin(), riskAlarm.getAlarmType(), new Date(), null, null, currentUser);
                saveOperateLog("长租系统撤回风控收车任务，解除报警， 更新报警开始时间", aLong+"", OperateTypeEnum.OPERATE_CONFIG.getCode(), currentUser);

                // 日志
                String logContent = StrUtil.format("长租系统撤回风控收车任务， 解除报警,报警类型:【{}】", AlarmTypeEnum.getValueByCode(riskAlarm.getAlarmType()));
                if (StringUtils.isNotBlank(req.getCancelReason())){
                    logContent += StrUtil.format(", 备注:{}", req.getCancelReason());
                }
                // 操作日志
                saveOperateLog(logContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode(), currentUser);
                // 车辆日志
                saveVehicleLog(riskAlarm.getVin(), logContent, currentUser);

                RiskPushRecord pushRecord = tableRiskPushRecordService.query(riskAlarm.getAlarmNo());
                if (pushRecord != null){
                    notifyCanceAlarmNo.add(riskAlarm.getAlarmNo());
                }
            }
            // 同步更新车辆风控状态
            subAssetsService.updateVehicleRiskStatusByVin(vin, VehicleRiskStatusEnum.NO_RISK.getCode());
        }
        // 调用车管
        JSONObject result = RiskUtils.cancelLongRentTask(req.getThirdPartSeq());
        if (result == null){
            throw new ServiceException("调用车管服务异常");
        }
        if (result.getIntValue("code") != 0){
            throw new ServiceException(result.getString("message"));
        }
        JSONObject data = result.getJSONObject("data");
        if(StringUtils.isNotBlank(data.getString("jobNo"))){
            builder.setJobNo (data.getString("jobNo"));
        }

        // 通知长租取消风控工单
        if (CollectionUtil.isNotEmpty(notifyCanceAlarmNo)){
            for (String alarmNo : notifyCanceAlarmNo) {
                RiskUtils.notifyCancelsRiskWarningToLongRent(alarmNo);
            }
        }
        return builder.build();
    }

    @Autowired
    private AutoRecoveryRiskHandler autoRecoveryRiskHandler;
    @Override
    public HandleCancelRiskAlarmRes handleCancelRiskAlarm(HandleCancelRiskAlarmReq req) {
        try {
            autoRecoveryRiskHandler.execute("");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return HandleCancelRiskAlarmRes.ok();
    }

    @Override
    public batchAddRiskCheckCollectionInfoRes batchAddRiskCheckCollectionInfo(batchAddRiskCheckCollectionInfoReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        // 解析excel
        List<BatchAddRiskCheckCollectionDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, BatchAddRiskCheckCollectionDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 500){
            throw new ServiceException("单次上传限制500条");
        }
        // 错误信息
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();

        int row = 0;
        for (BatchAddRiskCheckCollectionDemo importVehicleDemo : vehicleDemoList) {
            row ++;
            // 车架号
            String vin = importVehicleDemo.getVin();
            if (StringUtils.isBlank(vin)) {
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }
            Boolean doFlag = true;
            // 收车方式判断
            String collectCarType = importVehicleDemo.getCollectCarType();
            if (StringUtils.isBlank(collectCarType)) {
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行收车方式为空", row)));
            } else {
                if (CollectCarTypeEnum.getCodeByValue(collectCarType) == null) {
                    doFlag = false;
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("收车方式必须是【内部/委外】，当前收车方式【{}】", collectCarType)));
                }
            }

            // 收车人判断
            String collectCarPeople = importVehicleDemo.getCollectCarPeople();
            if (StringUtils.isBlank(collectCarPeople)) {
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行收车人为空", row)));
            }else if (collectCarPeople.length() > 50) {
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "收车人为长度不能超过50"));
            }
            String miscDesc = importVehicleDemo.getMiscDesc();
            if (StringUtils.isNotBlank(miscDesc) && miscDesc.length() > 500) {
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "备注长度不能超过500"));
            }
            // 如果有字段为空或不符合规则
           /* if (!doFlag) {
                continue;
            }*/

            // 查询车辆是否有进行中的风控收车任务
            List<RiskCheck> riskChecks = tableRiskCheckService.selectByVin(vin, DealStatusEnum.DEAL_STATUS.getCode());
            if (CollectionUtil.isEmpty(riskChecks)){
                errorInfoList.add(createErrorInfo(vin, "车辆不存在进行中的风控收车任务"));
            }
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("批量添加收车日志上传错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,125, errorInfoList, currentUser);
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }
        return null;
    }


    private RiskAlarm buildRiskAlarm(String vin, Integer alarmType, String remark, Integer alarmSystem, CurrentUser currentUser) {
        Date now = new Date();
        RiskAlarm riskAlarm = new RiskAlarm();
        riskAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ"));
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
        riskAlarm.setAlarmType(alarmType);
        riskAlarm.setAlarmTime(now);
        riskAlarm.setAlarmSystem(alarmSystem);
        riskAlarm.setAlarmDesc(remark);
        riskAlarm.setVin(vin);
        riskAlarm.setCreateOperName(currentUser.getNickName());
        riskAlarm.setCreateOperAccount(currentUser.getUserAccount());
        riskAlarm.setCreateTime(now);
        riskAlarm.setOrderSeq("");
        // 设置GPS信息
        setGPSInfo(riskAlarm);
        return riskAlarm;
    }

    /**
     * 解除风控
     * @param riskAlarm
     * @param currentUser
     */
    private void operateRemove(RiskAlarm riskAlarm, CurrentUser currentUser) {
        // 获取当前最大的VehicleRiskStatus
        int maxVehicleStatus = riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo());
        subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), maxVehicleStatus);

        // 更新风险报警, 非CZXT渠道的更新为WTXT渠道
        if (riskAlarm.getRecoverChannel() != null && !riskAlarm.getRecoverChannel().equals(AlarmSystemEnum.CZXT.getCode())){
            riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
        }
        tableRiskAlarmService.update(riskAlarm);

        // 删除redis中的数据
        if (riskAlarm.getAlarmType().equals(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
            Global.instance.redisUtil.del(RealFields.VEHICLE_ELECTRON_KEY + riskAlarm.getVin());
        }
    }

    /**
     * 升级风控
     * @param riskAlarm
     * @param currentUser
     */
    private void operateUpgrade(RiskAlarm riskAlarm, CurrentUser currentUser) {
        // 获取当前最大的VehicleRiskStatus
        int maxVehicleStatus = riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), null);
        // 更新风险报警
        tableRiskAlarmService.update(riskAlarm);
        // 推送判断
        SearchVehicleFileList assetsVehicle = subAssetsService.searchAssetsVehicle(riskAlarm.getVin());
        int productLine = assetsVehicle.getProductLine();
        if (RiskUtils.isNeedCreateCheck(productLine, riskAlarm.getAlarmType())){
            // 长租异动不需要创建收车任务
            if (productLine == 2 && riskAlarm.getAlarmType().equals(AlarmTypeEnum.VEHICLE_MOVEMENT.getCode())){
                return;
            }
            riskCheckService.autoCreateRiskCheck(riskAlarm, currentUser);
        }else if (assetsVehicle.getProductLine() == 2 && riskAlarm.getAlarmLevel() == 2){
            // 车辆风险等级
            Integer vehicleStatusNum = maxVehicleStatus > VehicleRiskStatusEnum.RISK_VEHICLE.getCode() ? maxVehicleStatus : VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
            // 如果产品线是长租，且有进行中的长租订单 则推送长租
            QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(assetsVehicle.getVin()).build());
            if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                log.error("查询长租订单合同信息失败! vin:{}", riskAlarm.getVin());
                return;
            }
            List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
            if (CollectionUtil.isNotEmpty(operateContractInfoList)){
                // 获取订单号
                OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                String orderNo = operateContractInfo.getOrderNo();
                // 请求长租
                RiskUtils.notifyRiskWorkOrderToLongRent(orderNo, assetsVehicle.getPlateNo(), 2, riskAlarm.getAlarmNo(), riskAlarm.getAlarmType(), 0);
                // 记录推送标识，恢复报警需要判断是否已经推送过
                tableRiskPushRecordService.insert(riskAlarm.getAlarmNo());
            }
        }
        // 更新车辆风控状态
        if (riskAlarm.getAlarmLevel() + 1 > maxVehicleStatus){
            subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), riskAlarm.getAlarmLevel() + 1);
        }

    }

    /**
     * 新增风控
     * @param riskAlarm
     * @param currentUser
     */
    void operateAdd(RiskAlarm riskAlarm, CurrentUser currentUser){
        boolean isBoolean = false;
        if (riskAlarm.getAlarmLevel() == AlarmLevenEnum.TWO.getCode()){
            isBoolean = true;
        }
        // 获取当前最大的VehicleRiskStatus
        int maxVehicleStatus = riskCommonService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), null);
        // 新增风险报警
        tableRiskAlarmService.save(riskAlarm);
        // 产品线
        List<SearchVehicleFileList> searchVehicleFileLists = subAssetsService.searchVehicleFileList(null, null, 0, riskAlarm.getVin());
        if (CollectionUtil.isEmpty(searchVehicleFileLists)){
            throw new ServiceException("查询车辆信息失败");
        }
        SearchVehicleFileList assetsVehicle = searchVehicleFileLists.get(0);
        // 是否长短组
        boolean isLongShortGroup = RiskUtils.isLongShortGroup(assetsVehicle.getProductLine());
        // 新增风控收车
        if ((isBoolean && (RiskUtils.isNeedCreateCheck(assetsVehicle.getProductLine(), riskAlarm.getAlarmType())) || riskAlarm.getAlarmSystem().equals(AlarmSystemEnum.CZXT.getCode()))){
            riskCheckService.autoCreateRiskCheck(riskAlarm, currentUser);
        }
        if (riskAlarm.getAlarmLevel() + 1 > maxVehicleStatus){
            // 传入的VehicleRiskStatus最大则更新当前车辆的所有VehicleRiskStatus
            subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), riskAlarm.getAlarmLevel() + 1);
        }
    }

}
