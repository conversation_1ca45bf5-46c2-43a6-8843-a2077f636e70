package com.saicmobility.evcard.vlms.risk.dto.check;

import com.saicmobility.evcard.pb.annotation.PbFormat;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RiskCheckCollectionInfoData {
    private Integer collectCarType; // 收车方式 1:内部 2:委外
    private String collectCarTypeDesc;
    private String collectCarPeople; // 收车人

    private List<Attachment> locationPicInfos; // 定位截图
    private String hasLocationPic;

    private List<Attachment> watermarkPicInfos; // 水印照片
    private String hasWatermarkPic;

    private List<Attachment> attachmentInfos; // 失控附件
    private String hasAttachmentInfo;

    private String miscDesc; // 备注
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; // 操作时间

    private String createTimeStr;

    private String createOperName; // 操作人
}
