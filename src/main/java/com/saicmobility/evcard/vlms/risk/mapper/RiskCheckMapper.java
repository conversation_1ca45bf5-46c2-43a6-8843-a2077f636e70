package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskCheckMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    BasicColumn[] selectList = BasicColumn.columnList(id, checkNo, vin, dealStatus, alarmSystem, lastLocation, lastLocationTime, outControlType, outControlDate, outControlAnnexId, outControlDesc, vehiclePicId, vehicleCheckDesc, recoveryTime, taskEndTime, endOperName, endOperAccount, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName, isWhiteListRecover, isAutoComplete);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskCheck> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskCheckResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="check_no", property="checkNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="deal_status", property="dealStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_system", property="alarmSystem", jdbcType=JdbcType.INTEGER),
        @Result(column="last_location", property="lastLocation", jdbcType=JdbcType.VARCHAR),
        @Result(column="last_location_time", property="lastLocationTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="out_control_type", property="outControlType", jdbcType=JdbcType.INTEGER),
        @Result(column="out_control_date", property="outControlDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="out_control_annex_id", property="outControlAnnexId", jdbcType=JdbcType.BIGINT),
        @Result(column="out_control_desc", property="outControlDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_pic_id", property="vehiclePicId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_check_desc", property="vehicleCheckDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="recovery_time", property="recoveryTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="task_end_time", property="taskEndTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_oper_name", property="endOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="end_oper_account", property="endOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_white_list_recover", property="isWhiteListRecover", jdbcType=JdbcType.INTEGER),
        @Result(column="is_auto_complete", property="isAutoComplete", jdbcType=JdbcType.INTEGER)
    })
    List<RiskCheck> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskCheckResult")
    Optional<RiskCheck> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskCheck, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskCheck, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int insert(RiskCheck row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheck, c ->
            c.map(checkNo).toProperty("checkNo")
            .map(vin).toProperty("vin")
            .map(dealStatus).toProperty("dealStatus")
            .map(alarmSystem).toProperty("alarmSystem")
            .map(lastLocation).toProperty("lastLocation")
            .map(lastLocationTime).toProperty("lastLocationTime")
            .map(outControlType).toProperty("outControlType")
            .map(outControlDate).toProperty("outControlDate")
            .map(outControlAnnexId).toProperty("outControlAnnexId")
            .map(outControlDesc).toProperty("outControlDesc")
            .map(vehiclePicId).toProperty("vehiclePicId")
            .map(vehicleCheckDesc).toProperty("vehicleCheckDesc")
            .map(recoveryTime).toProperty("recoveryTime")
            .map(taskEndTime).toProperty("taskEndTime")
            .map(endOperName).toProperty("endOperName")
            .map(endOperAccount).toProperty("endOperAccount")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
            .map(isWhiteListRecover).toProperty("isWhiteListRecover")
            .map(isAutoComplete).toProperty("isAutoComplete")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int insertSelective(RiskCheck row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheck, c ->
            c.map(checkNo).toPropertyWhenPresent("checkNo", row::getCheckNo)
            .map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(dealStatus).toPropertyWhenPresent("dealStatus", row::getDealStatus)
            .map(alarmSystem).toPropertyWhenPresent("alarmSystem", row::getAlarmSystem)
            .map(lastLocation).toPropertyWhenPresent("lastLocation", row::getLastLocation)
            .map(lastLocationTime).toPropertyWhenPresent("lastLocationTime", row::getLastLocationTime)
            .map(outControlType).toPropertyWhenPresent("outControlType", row::getOutControlType)
            .map(outControlDate).toPropertyWhenPresent("outControlDate", row::getOutControlDate)
            .map(outControlAnnexId).toPropertyWhenPresent("outControlAnnexId", row::getOutControlAnnexId)
            .map(outControlDesc).toPropertyWhenPresent("outControlDesc", row::getOutControlDesc)
            .map(vehiclePicId).toPropertyWhenPresent("vehiclePicId", row::getVehiclePicId)
            .map(vehicleCheckDesc).toPropertyWhenPresent("vehicleCheckDesc", row::getVehicleCheckDesc)
            .map(recoveryTime).toPropertyWhenPresent("recoveryTime", row::getRecoveryTime)
            .map(taskEndTime).toPropertyWhenPresent("taskEndTime", row::getTaskEndTime)
            .map(endOperName).toPropertyWhenPresent("endOperName", row::getEndOperName)
            .map(endOperAccount).toPropertyWhenPresent("endOperAccount", row::getEndOperAccount)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
            .map(isWhiteListRecover).toPropertyWhenPresent("isWhiteListRecover", row::getIsWhiteListRecover)
            .map(isAutoComplete).toPropertyWhenPresent("isAutoComplete", row::getIsAutoComplete)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default Optional<RiskCheck> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskCheck, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default List<RiskCheck> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskCheck, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default List<RiskCheck> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskCheck, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default Optional<RiskCheck> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskCheck, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskCheck row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(checkNo).equalTo(row::getCheckNo)
                .set(vin).equalTo(row::getVin)
                .set(dealStatus).equalTo(row::getDealStatus)
                .set(alarmSystem).equalTo(row::getAlarmSystem)
                .set(lastLocation).equalTo(row::getLastLocation)
                .set(lastLocationTime).equalTo(row::getLastLocationTime)
                .set(outControlType).equalTo(row::getOutControlType)
                .set(outControlDate).equalTo(row::getOutControlDate)
                .set(outControlAnnexId).equalTo(row::getOutControlAnnexId)
                .set(outControlDesc).equalTo(row::getOutControlDesc)
                .set(vehiclePicId).equalTo(row::getVehiclePicId)
                .set(vehicleCheckDesc).equalTo(row::getVehicleCheckDesc)
                .set(recoveryTime).equalTo(row::getRecoveryTime)
                .set(taskEndTime).equalTo(row::getTaskEndTime)
                .set(endOperName).equalTo(row::getEndOperName)
                .set(endOperAccount).equalTo(row::getEndOperAccount)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName)
                .set(isWhiteListRecover).equalTo(row::getIsWhiteListRecover)
                .set(isAutoComplete).equalTo(row::getIsAutoComplete);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskCheck row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(checkNo).equalToWhenPresent(row::getCheckNo)
                .set(vin).equalToWhenPresent(row::getVin)
                .set(dealStatus).equalToWhenPresent(row::getDealStatus)
                .set(alarmSystem).equalToWhenPresent(row::getAlarmSystem)
                .set(lastLocation).equalToWhenPresent(row::getLastLocation)
                .set(lastLocationTime).equalToWhenPresent(row::getLastLocationTime)
                .set(outControlType).equalToWhenPresent(row::getOutControlType)
                .set(outControlDate).equalToWhenPresent(row::getOutControlDate)
                .set(outControlAnnexId).equalToWhenPresent(row::getOutControlAnnexId)
                .set(outControlDesc).equalToWhenPresent(row::getOutControlDesc)
                .set(vehiclePicId).equalToWhenPresent(row::getVehiclePicId)
                .set(vehicleCheckDesc).equalToWhenPresent(row::getVehicleCheckDesc)
                .set(recoveryTime).equalToWhenPresent(row::getRecoveryTime)
                .set(taskEndTime).equalToWhenPresent(row::getTaskEndTime)
                .set(endOperName).equalToWhenPresent(row::getEndOperName)
                .set(endOperAccount).equalToWhenPresent(row::getEndOperAccount)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
                .set(isWhiteListRecover).equalToWhenPresent(row::getIsWhiteListRecover)
                .set(isAutoComplete).equalToWhenPresent(row::getIsAutoComplete);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int updateByPrimaryKey(RiskCheck row) {
        return update(c ->
            c.set(checkNo).equalTo(row::getCheckNo)
            .set(vin).equalTo(row::getVin)
            .set(dealStatus).equalTo(row::getDealStatus)
            .set(alarmSystem).equalTo(row::getAlarmSystem)
            .set(lastLocation).equalTo(row::getLastLocation)
            .set(lastLocationTime).equalTo(row::getLastLocationTime)
            .set(outControlType).equalTo(row::getOutControlType)
            .set(outControlDate).equalTo(row::getOutControlDate)
            .set(outControlAnnexId).equalTo(row::getOutControlAnnexId)
            .set(outControlDesc).equalTo(row::getOutControlDesc)
            .set(vehiclePicId).equalTo(row::getVehiclePicId)
            .set(vehicleCheckDesc).equalTo(row::getVehicleCheckDesc)
            .set(recoveryTime).equalTo(row::getRecoveryTime)
            .set(taskEndTime).equalTo(row::getTaskEndTime)
            .set(endOperName).equalTo(row::getEndOperName)
            .set(endOperAccount).equalTo(row::getEndOperAccount)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .set(isWhiteListRecover).equalTo(row::getIsWhiteListRecover)
            .set(isAutoComplete).equalTo(row::getIsAutoComplete)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    default int updateByPrimaryKeySelective(RiskCheck row) {
        return update(c ->
            c.set(checkNo).equalToWhenPresent(row::getCheckNo)
            .set(vin).equalToWhenPresent(row::getVin)
            .set(dealStatus).equalToWhenPresent(row::getDealStatus)
            .set(alarmSystem).equalToWhenPresent(row::getAlarmSystem)
            .set(lastLocation).equalToWhenPresent(row::getLastLocation)
            .set(lastLocationTime).equalToWhenPresent(row::getLastLocationTime)
            .set(outControlType).equalToWhenPresent(row::getOutControlType)
            .set(outControlDate).equalToWhenPresent(row::getOutControlDate)
            .set(outControlAnnexId).equalToWhenPresent(row::getOutControlAnnexId)
            .set(outControlDesc).equalToWhenPresent(row::getOutControlDesc)
            .set(vehiclePicId).equalToWhenPresent(row::getVehiclePicId)
            .set(vehicleCheckDesc).equalToWhenPresent(row::getVehicleCheckDesc)
            .set(recoveryTime).equalToWhenPresent(row::getRecoveryTime)
            .set(taskEndTime).equalToWhenPresent(row::getTaskEndTime)
            .set(endOperName).equalToWhenPresent(row::getEndOperName)
            .set(endOperAccount).equalToWhenPresent(row::getEndOperAccount)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .set(isWhiteListRecover).equalToWhenPresent(row::getIsWhiteListRecover)
            .set(isAutoComplete).equalToWhenPresent(row::getIsAutoComplete)
            .where(id, isEqualTo(row::getId))
        );
    }
}