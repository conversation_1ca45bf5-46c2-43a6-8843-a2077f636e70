package com.saicmobility.evcard.vlms.risk.service.sub;

import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;

public interface SubRiskAlaramService {

    /**
     * 告警恢复通知长租
     * @param alarmNo
     */
    void alarmRecoryNotify(String alarmNo);

    /**
     * 查询长租订单
     * @param vin
     * @return
     */
    String quertLongRentOrderByVin(String vin);


    /**
     * 是否有进行中的收车任务
     * @param vin
     * @return
     */
    boolean hasRiskCheckTask(String vin);


    /**
     * 获取车辆风险状态
     * @param vin
     * @param alarmLevel
     */
    Integer getVehicleRiskStatus(String vin, Integer alarmLevel, Integer productLine, boolean hasRiskCheck);

    /**
     * 获取最大风险等级
     * @param vin
     * @param alarmNo
     */
    public Integer queryMaxRiskLevel(String vin, String alarmNo);
}
