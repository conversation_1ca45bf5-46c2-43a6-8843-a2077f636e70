package com.saicmobility.evcard.vlms.risk;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.saicmobility.common.bpe.FlowMap;
import com.saicmobility.common.envconfig.EnvGlobal;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.md.mdstoreservice.api.GetCacheStoreReq;
import com.saicmobility.evcard.md.mdstoreservice.api.GetCacheStoreRes;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import com.saicmobility.team.hqoaauthservice.api.HqOaAuthService;
import com.saicmobility.team.hqoaauthservice.api.QueryUserInfoReq;
import com.saicmobility.team.hqoaauthservice.api.QueryUserInfoRes;
import com.saicmobility.team.hqoaauthservice.api.UserDetailInfo;
import krpc.rpc.bootstrap.RpcAppInitBean;
import krpc.rpc.core.ClientContext;
import krpc.rpc.util.MessageToMap;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@Component
public class ConfigLoader {

    static final Logger log = LoggerFactory.getLogger(ConfigLoader.class);

    Map<String, FlowMap> orgInfoMap = new HashMap<>();

    String orgInfoMapMd5 = ""; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile

    Map<String, FlowMap> userInfoMap = new HashMap<>();

    String userInfoMapMd5 = ""; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile

    Map<String, String> orgNameMap = new HashMap<>();
    Map<Long, FlowMap> vehicleModelMap = new HashMap<>();

    String vehicleModelMd5 = ""; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile

    Map<Long, FlowMap> storeMap = new HashMap<>();

    String storeMapMd5 = ""; // org.quartz.threadPool.threadCount == 1 单线程访问， 无需使用 volatile


    @Resource
    RpcAppInitBean rpcAppInitBean;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private HqOaAuthService hqOaAuthService;

    @Resource
    private MdStoreService mdStoreService;

    Scheduler scheduler;


    @PostConstruct
    void init() throws Exception {

        loadData(true); // load in main thread

        Properties p = new Properties();
        p.put("org.quartz.threadPool.threadCount", "1");
        p.put("org.quartz.scheduler.instanceName", "config_loader_timer");
        StdSchedulerFactory factory = new StdSchedulerFactory();
        factory.initialize(p);
        scheduler = factory.getScheduler();

        JobDataMap map = new JobDataMap();
        map.put("service", this);

        JobDetail job1 = JobBuilder.newJob(LoadDataJob.class).withIdentity("job4").setJobData(map).build();
        CronTrigger cronTrigger1 = TriggerBuilder.newTrigger().withIdentity("trigger4").withSchedule(CronScheduleBuilder.cronSchedule("55 0/1 * * * ? ")).build();
        scheduler.scheduleJob(job1, cronTrigger1);

        scheduler.start();
    }

    static public class LoadDataJob implements Job {

        @Override
        public void execute(JobExecutionContext ctx) {
            ConfigLoader service = (ConfigLoader) ctx.getJobDetail().getJobDataMap().get("service");
            try {
                service.loadData(false);
            } catch (Exception e) {
                log.error("loadData exception", e);
            }
        }

    }

    @PreDestroy
    void close() {
        try {
            scheduler.shutdown();
        } catch (Exception e) {
        }
    }

    void loadData(boolean init) {
        loadOrgInfo(init);
        loadUserInfo(init);
        loadVehicleModel(init);
        loadStore(init);
    }

    boolean needReload(String lastCfgMd5, String currentCfgMd5) {
        if (lastCfgMd5.isEmpty() || currentCfgMd5.isEmpty()) return true;
        return !lastCfgMd5.equals(currentCfgMd5);
    }

    // 查询机构
    void loadOrgInfo(boolean init) {
        int retCode = loadOrgInfo();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            log.error("cannot load orgInfo, retCode={}", retCode);
            retCode = loadOrgInfo();
            if (retCode == 0) break;
        }
    }

    int loadOrgInfo() {
        ClientContext.setTimeout(15000);
        GetAllOrgInfoRes getAllOrgInfoRes = mdDataProxy
                .getAllOrgInfo(GetAllOrgInfoReq.newBuilder().setCfgMd5(orgInfoMapMd5).build());
        if (getAllOrgInfoRes.getRetCode() == 0 && needReload(orgInfoMapMd5, getAllOrgInfoRes.getCfgMd5())) {
            Map<String, FlowMap> tmpMap = new HashMap<>();
            Map<String, String> tmpOrgNameMap = new HashMap<>();
            List<OrgInfo> list = getAllOrgInfoRes.getInfoList();
            for (OrgInfo info : list) {
                FlowMap map = new FlowMap();
                tmpMap.put(info.getOrgCode(), map.append("*", MessageToMap.toMap(info)));
                tmpOrgNameMap.put(info.getOrgName(), info.getOrgCode());
            }
            this.orgInfoMap = tmpMap;
            this.orgNameMap = tmpOrgNameMap;
            this.orgInfoMapMd5 = getAllOrgInfoRes.getCfgMd5();
        }
        return getAllOrgInfoRes.getRetCode();
    }

    void loadUserInfo(boolean init) {
        int retCode = loadUserInfo();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            log.error("cannot load userInfo, retCode={}", retCode);
            retCode = loadUserInfo();
            if (retCode == 0) break;
        }
    }

    int loadUserInfo() {
        ClientContext.setTimeout(15000);
        QueryUserInfoRes queryUserInfoRes = hqOaAuthService.queryUserInfo(QueryUserInfoReq.newBuilder()
                .setPageNum(1).setPageSize(Integer.MAX_VALUE)
                .setStatus(-1).setMd5(userInfoMapMd5).build());
        if (queryUserInfoRes.getRetCode() == 0 && needReload(userInfoMapMd5, queryUserInfoRes.getMd5())) {
            Map<String, FlowMap> tmpMap = new HashMap<>();
            List<UserDetailInfo> list = queryUserInfoRes.getUserInfoList();
            for (UserDetailInfo info : list) {
                FlowMap map = new FlowMap();
                tmpMap.put(info.getAdAccount(), map.append("*", MessageToMap.toMap(info)));
            }
            this.userInfoMap = tmpMap;
            this.userInfoMapMd5 = queryUserInfoRes.getMd5();
        }
        return queryUserInfoRes.getRetCode();
    }

    void loadVehicleModel(boolean init) {
        int retCode = loadVehicleModel();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            log.error("cannot load vehicleModel, retCode={}", retCode);
            retCode = loadVehicleModel();
            if (retCode == 0) break;
        }
    }

    int loadVehicleModel() {
        ClientContext.setTimeout(15000);
        GetAllVehicleModelInfoRes allVehicleModelInfo = mdDataProxy.getAllVehicleModelInfo(GetAllVehicleModelInfoReq.newBuilder().setCfgMd5(vehicleModelMd5).build());
        if (allVehicleModelInfo.getRetCode() == 0 && needReload(vehicleModelMd5, allVehicleModelInfo.getCfgMd5())) {
            Map<Long, FlowMap> tmpMap = new HashMap<>();
            List<VehicleModelInfo> infoList = allVehicleModelInfo.getInfoList();
            for (VehicleModelInfo info : infoList) {
                FlowMap map = new FlowMap();
                tmpMap.put(Convert.toLong(info.getVehicleModelSeq()), map.append("*", MessageToMap.toMap(info)));
            }
            this.vehicleModelMap = tmpMap;
            this.vehicleModelMd5 = allVehicleModelInfo.getCfgMd5();
        }
        return allVehicleModelInfo.getRetCode();
    }

    void loadStore(boolean init) {
        int retCode = loadStore();
        if (retCode == 0) return;
        if (!init) return;
        if (!EnvGlobal.globalProfile.equals("prd")) return;

        // 生产环境启动时若没有加载到配置数据则一直重试直到成功为止
        for (; ; ) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            log.error("cannot load store, retCode={}", retCode);
            retCode = loadStore();
            if (retCode == 0) break;
        }
    }

    int loadStore() {
        ClientContext.setTimeout(15000);
        GetCacheStoreRes getCacheStoreRes = mdStoreService.getCacheStore(GetCacheStoreReq.newBuilder().setCfgMd5(storeMapMd5).build());
        if (getCacheStoreRes.getRetCode() == 0 && needReload(storeMapMd5, getCacheStoreRes.getCfgMd5())) {
            Map<Long, FlowMap> tmpMap = new HashMap<>();
            List<GetCacheStoreRes.CacheStoreInfo> infoList = getCacheStoreRes.getInfoList();
            for (GetCacheStoreRes.CacheStoreInfo info : infoList) {
                FlowMap map = new FlowMap();
                tmpMap.put(Convert.toLong(info.getId()), map.append("*", MessageToMap.toMap(info)));
            }
            this.storeMap = tmpMap;
            this.storeMapMd5 = getCacheStoreRes.getCfgMd5();
        }
        return getCacheStoreRes.getRetCode();
    }

    public String getOrgName(String orgCode) {
        if (ObjectUtil.isEmpty(orgInfoMap.get(orgCode))) {
            return StrUtil.EMPTY;
        } else {
            return orgInfoMap.get(orgCode).ns("orgName");
        }
    }

    public String getOrgCode(String orgName) {
        if (StringUtils.isBlank(orgNameMap.get(orgName))) {
            return StrUtil.EMPTY;
        } else {
            return orgNameMap.get(orgName);
        }
    }

    public String getSapCode(String orgCode) {
        if (ObjectUtil.isEmpty(orgInfoMap.get(orgCode))) {
            return StrUtil.EMPTY;
        } else {
            return orgInfoMap.get(orgCode).ns("sapCode");
        }
    }

    public FlowMap getUserInfo(String username, boolean isNil) {
        if (ObjectUtil.isEmpty(userInfoMap.get(username)) && isNil) {
            return new FlowMap();
        } else {
            return userInfoMap.get(username);
        }
    }

    public FlowMap getVehicleModel(Long id, boolean isNil) {
        if (ObjectUtil.isEmpty(vehicleModelMap.get(id)) && isNil) {
            return new FlowMap();
        } else {
            return vehicleModelMap.get(id);
        }
    }

    public FlowMap getStore(Long id, boolean isNil) {
        if (ObjectUtil.isEmpty(storeMap.get(id)) && isNil) {
            return new FlowMap();
        } else {
            return storeMap.get(id);
        }
    }
}

