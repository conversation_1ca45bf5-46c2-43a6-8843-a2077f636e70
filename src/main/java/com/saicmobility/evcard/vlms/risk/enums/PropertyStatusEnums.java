package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PropertyStatusEnums {

    // 资产状态 0 : 在建工程 1 : 固定资产 2 : 固定资产（待报废）3 : 报废 4 : 固定资产(待处置) 5 : 固定资产(已处置) 6 : 以租代售 7 : 库存商品 8 : 已处置（未过户）

    ZJGC(0, "在建工程"),
    GDZC(1, "固定资产"),
    DBF(2, "固定资产（待报废）"),
    BF(3, "报废"),
    DCZ(4, "固定资产(待处置)"),
    YCZ(5, "固定资产(已处置)"),
    YCDS(6, "以租代售"),
    KCSP(7, "库存商品"),
    WGH(8, "已处置（未过户）");


    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (PropertyStatusEnums item : PropertyStatusEnums.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return "";
    }
}
