package com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation;

import com.saicmobility.evcard.md.mddataproxy.api.provinceInfo;
import com.saicmobility.evcard.vlms.risk.bo.QueryAllProvinceBO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class QueryAllProvinceDTORes {

    private List<QueryAllProvinceBO> provinceInfos;

    public static QueryAllProvinceDTORes toRes(List<provinceInfo> provinceInfosList) {
        QueryAllProvinceDTORes queryAllProvinceDTORes = new QueryAllProvinceDTORes();
        List<QueryAllProvinceBO> provinceInfos = new ArrayList<>();
        provinceInfosList.forEach(provinceInfo -> {
            QueryAllProvinceBO queryAllProvinceBO = new QueryAllProvinceBO();
            queryAllProvinceBO.setProvinceId(provinceInfo.getProvinceId());
            queryAllProvinceBO.setProvince(provinceInfo.getProvince());
            provinceInfos.add(queryAllProvinceBO);
        });
        queryAllProvinceDTORes.setProvinceInfos(provinceInfos);
        return queryAllProvinceDTORes;
    }
}
