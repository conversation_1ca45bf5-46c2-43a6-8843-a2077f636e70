package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.model.Attachment;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface TableAttachmentService {

    /**
     * 根据id查询附件
     * @param id
     * @return
     */
    Attachment selectById(Long id);

    /**
     * 新增附件
     * @param id
     * @return
     */
    void insert(Attachment attachment);

    /**
     * 批量新增附件
     * @param attachmentList
     * @return
     */
    void batchInsert(List<Attachment> attachmentList);


    /**
     * 查询附件列表
     * @param fileType
     * @param relationId
     * @return
     */
    List<Attachment> queryAttachmentByFileType(int fileType, String relationId);

    /**
     * 删除附件
     * @param fileType
     * @param relationId
     */
    void delete(int fileType, String relationId);

}
