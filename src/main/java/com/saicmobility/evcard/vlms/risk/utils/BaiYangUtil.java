package com.saicmobility.evcard.vlms.risk.utils;

import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * 白杨对接Util
 */
public class BaiYangUtil {
    /**
     * 包装白杨文件信息
     *
     * @param fileUrl   文件url列表
     * @param requestNo requestNo
     * @return 白杨文件信息
     */
    public static List<Map<String, Object>> getFilePathMap(String fileUrl, String requestNo) {
        Map<String, Object> filePathMap = new LinkedHashMap<>();
        try {
            URL url = new URL(fileUrl);
            File file = new File(url.getPath());

            filePathMap.put("uid", requestNo);
            filePathMap.put("name", file.getName());
            filePathMap.put("type", MimeTypeUtils.getContentTypeByFileName(file.getName() + ""));
            filePathMap.put("url", fileUrl);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD"); // 使用 HEAD 请求，它不会下载资源的内容
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                long contentLength = connection.getContentLengthLong(); // 获取内容长度
                filePathMap.put("size", contentLength);
            } else {
                System.out.println("Error: HTTP response code: " + responseCode);
                filePathMap.put("size", 0);
            }
            connection.disconnect();

            filePathMap.put("status", "done");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return Collections.singletonList(filePathMap);
    }

    /**
     * 包装白杨文件信息
     *
     * @param fileUrlList 文件url列表
     * @param requestNo   requestNo
     * @return 白杨文件信息
     */
    public static List<Map<String, Object>> getFilePathMapFromFileUrlList(List<String> fileUrlList, String requestNo) {
        List<Map<String, Object>> appealAttachments = new LinkedList<>();
        for (String appealAttachment : fileUrlList) {
            try {
                URL url = new URL(appealAttachment);
                File file = new File(url.getPath());

                Map<String, Object> filePathMap = new LinkedHashMap<>();
                filePathMap.put("uid", requestNo);
                filePathMap.put("name", file.getName());
                filePathMap.put("type", MimeTypeUtils.getContentTypeByFileName(file.getName() + ""));
                filePathMap.put("url", appealAttachment);
                filePathMap.put("size", file.length());
                filePathMap.put("status", "done");
                appealAttachments.add(filePathMap);
            } catch (MalformedURLException e) {
                throw new RuntimeException(e);
            }
        }
        return appealAttachments;
    }


    /**
     * 包装白杨文件信息
     * 处理类似https://wt-sit.gcsrental.com/vlms/gtw/downloadFile?filePath=/bizFile/202502/08/67a70d27e4b0741a6e0ee607.png这种
     *
     * @param filePath   文件路径
     * @param fileName   文件名称
     * @param requestNo requestNo
     * @return 白杨文件信息
     */
    public static List<Map<String, Object>> getFilePathMap(String filePath, String fileName, String requestNo) {
        Map<String, Object> filePathMap = new LinkedHashMap<>();
        try {

            URL url = new URL(filePath);

            filePathMap.put("uid", requestNo);
            filePathMap.put("name", fileName);
            filePathMap.put("type", MimeTypeUtils.getContentTypeByFileName(fileName + ""));
            filePathMap.put("url", filePath);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD"); // 使用 HEAD 请求，它不会下载资源的内容
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                long contentLength = connection.getContentLengthLong(); // 获取内容长度
                filePathMap.put("size", contentLength);
            } else {
                System.out.println("Error: HTTP response code: " + responseCode);
                filePathMap.put("size", 0);
            }
            connection.disconnect();

            filePathMap.put("status", "done");
        } catch (IOException e) {
            throw new ServiceException(ResultCode.COMMON_FAIL, StrUtil.format("附件解析异常，请检查附件链接文件名等相关配置！"));
        }
        return Collections.singletonList(filePathMap);
    }


    /**
     * 包装白杨文件信息
     * 处理类似https://wt-sit.gcsrental.com/vlms/gtw/downloadFile?filePath=/bizFile/202502/08/67a70d27e4b0741a6e0ee607.png这种
     *
     * @param attachmentInfoList 文件列表
     * @param requestNo   requestNo
     * @return 白杨文件信息
     */
    public static List<Map<String, Object>> getFilePathMapFromAttachmentInfoList(List<AttachmentInfo> attachmentInfoList, String requestNo) {
        List<Map<String, Object>> appealAttachments = new LinkedList<>();
        for (AttachmentInfo attachmentInfo : attachmentInfoList) {
            try {
                String filePath = attachmentInfo.getFilePath();
                String fileName = attachmentInfo.getFileName();

                URL url = new URL(filePath);

                Map<String, Object> filePathMap = new LinkedHashMap<>();
                filePathMap.put("uid", requestNo);
                filePathMap.put("name", fileName);
                filePathMap.put("type", MimeTypeUtils.getContentTypeByFileName(fileName + ""));
                filePathMap.put("url", filePath);

                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("HEAD"); // 使用 HEAD 请求，它不会下载资源的内容
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    long contentLength = connection.getContentLengthLong(); // 获取内容长度
                    filePathMap.put("size", contentLength);
                } else {
                    System.out.println("Error: HTTP response code: " + responseCode);
                    filePathMap.put("size", 0);
                }
                connection.disconnect();

                filePathMap.put("status", "done");
                appealAttachments.add(filePathMap);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return appealAttachments;
    }
}
