package com.saicmobility.evcard.vlms.risk.excutor;

import krpc.rpc.impl.TracablePool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 */
@Configuration
@EnableAsync
@Slf4j
public class ExecutorConfig {

    @Bean
    public Executor eventExecutor() {
        log.info("start asyncEventExecutor");
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        int processors = Runtime.getRuntime().availableProcessors();
        int threads = processors * 8;
        //配置核心线程数
        executor.setCorePoolSize(threads);//配置最大线程数
        executor.setMaxPoolSize(threads);
        //配置队列大小
        executor.setQueueCapacity(10000);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-event-");
        //线程池维护线程所允许的空闲时间,TimeUnit.SECONDS
        executor.setKeepAliveSeconds(60);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        // 线程池对拒绝任务的处理策略: CallerRunsPolicy策略，当线程池没有处理能力的时候，该策略会直接在 execute 方法的调用线程中运行被拒绝的任务；如果执行程序已关闭，则会丢弃该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor asyncServiceExecutor() {
        log.info("start asyncServiceExecutor");
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        ThreadPoolTaskExecutor executor = new VisiableThreadPoolTaskExecutor();
        int processors = Runtime.getRuntime().availableProcessors();
        int threads = processors * 8;
        //配置核心线程数
        executor.setCorePoolSize(threads);//配置最大线程数
        executor.setMaxPoolSize(threads);
        //配置队列大小
        executor.setQueueCapacity(10000);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service-");
        //线程池维护线程所允许的空闲时间,TimeUnit.SECONDS
        executor.setKeepAliveSeconds(30);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        // 线程池对拒绝任务的处理策略: CallerRunsPolicy策略，当线程池没有处理能力的时候，该策略会直接在 execute 方法的调用线程中运行被拒绝的任务；如果执行程序已关闭，则会丢弃该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean(initMethod = "init", destroyMethod = "close")
    TracablePool tracablePool() {
        TracablePool pool = new TracablePool();
        int processors = Runtime.getRuntime().availableProcessors();
        int threads = processors * 8;
        pool.setThreads(threads);
        pool.setMaxThreads(threads);
        pool.setQueueSize(10000);
        return pool;
    }
}