package com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation;

import com.saicmobility.evcard.vlms.risk.bo.ElectronicFenceRegulationBO;
import com.saicmobility.evcard.vlms.risk.dto.CurrentUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class ElectronicFenceConfigReq extends ElectronicFenceRegulationBO {

    private CurrentUser currentUser;

}
