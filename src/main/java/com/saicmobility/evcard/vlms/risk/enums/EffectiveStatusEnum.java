package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生效状态 1-进行中 2-已过期
 * <AUTHOR>
 * @date 2024-01-16
 */
@AllArgsConstructor
@Getter
public enum EffectiveStatusEnum {
    EFFECTIVE_STATUS(1, "生效中"),
    EFFECTIVE_RECOVER(2, "已过期");

    private Integer code;
    private String value;
    public static String getValueByCode(Integer code)
    {
        for (EffectiveStatusEnum effectiveStatusEnum : EffectiveStatusEnum.values()) {
            if (effectiveStatusEnum.getCode().equals(code)) {
                return effectiveStatusEnum.getValue();
            }
        }
        return null;
    }
}
