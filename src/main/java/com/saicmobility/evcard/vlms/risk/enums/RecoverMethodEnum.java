package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 恢复方式 1、人工 2、自动
 * <AUTHOR>
 * @date 2024-01-16
 */
@AllArgsConstructor
@Getter
public enum RecoverMethodEnum {
    RECOVER_METHOD(1, "人工"),
    AUTO_RECOVER(2, "自动");

    Integer code;
    String value;
    public static String getValueByCode(Integer code){
        for (RecoverMethodEnum item : RecoverMethodEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
