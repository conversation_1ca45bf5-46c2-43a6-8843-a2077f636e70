package com.saicmobility.evcard.vlms.risk.dto;

import com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import lombok.Data;

import java.util.List;

@Data
public class BaiYangRiskCheckCompleteRequest {
    private Long id;

    /**
     * 其他
     */
    private AttachmentInfo vehicleAttachement;

    /**
     * 备注
     */
    private String vehicleCheckDesc;

    /**
     * 收车时间：年月日，必填
     */
    private String collectCarDatetime;

    /**
     * 收车地点 省+市+具体地点，省市选择，地点文本输入。必填
     */
    private String collectCarPlace;

    /**
     * 收车人/供应商：文本输入，必填
     */
    private String collectCarPeople;

    /**
     * 收车方式：1:内部 2:委外，必填
     */
    private Integer collectCarType;

    /**
     * 风控类型: 1:风控车辆 2:失控车辆，必填
     */
    private Integer collectCarRiskType;

    /**
     * 【验车单】：附件（1个），必填
     */
    private AttachmentInfo checkVehicleFormInfo;

    /**
     * 【车辆清收音视频资料】：附件（1个），必填
     */
    private AttachmentInfo checkVehicleVideoInfo;

    /**
     * 【车内物品交接单】：附件（1个），必填
     */
    private AttachmentInfo vehicleItemInfo;

    /**
     * 其他
     */
    private List<AttachmentInfo> attachmentPaths;

    private CurrentUser currentUser;

    private String requestNo;

    // 1-是 2-否 自动完成长租收车
    private Integer isAutoComplete;

    // 长租收车任务编号
    private String rentTaskNo;
}
