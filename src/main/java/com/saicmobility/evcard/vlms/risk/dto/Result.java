package com.saicmobility.evcard.vlms.risk.dto;

public class Result {
	
	private Location location;

	private String formatted_address;
	
	private String business;
	
	private AddressComponent addressComponent;
	
	private Object pois;
	
	private Object roads;
	
	private Object poiRegions;
	
	private String sematic_description;
	
	private int cityCode;

	public Location getLocation() {
		return location;
	}

	public void setLocation(Location location) {
		this.location = location;
	}

	public String getFormatted_address() {
		return formatted_address;
	}

	public void setFormatted_address(String formatted_address) {
		this.formatted_address = formatted_address;
	}

	public String getBusiness() {
		return business;
	}

	public void setBusiness(String business) {
		this.business = business;
	}

	public AddressComponent getAddressComponent() {
		return addressComponent;
	}

	public void setAddressComponent(AddressComponent addressComponent) {
		this.addressComponent = addressComponent;
	}

	public Object getPois() {
		return pois;
	}

	public void setPois(Object pois) {
		this.pois = pois;
	}

	public Object getRoads() {
		return roads;
	}

	public void setRoads(Object roads) {
		this.roads = roads;
	}

	public Object getPoiRegions() {
		return poiRegions;
	}

	public void setPoiRegions(Object poiRegions) {
		this.poiRegions = poiRegions;
	}

	public String getSematic_description() {
		return sematic_description;
	}

	public void setSematic_description(String sematic_description) {
		this.sematic_description = sematic_description;
	}

	public int getCityCode() {
		return cityCode;
	}

	public void setCityCode(int cityCode) {
		this.cityCode = cityCode;
	}


}
