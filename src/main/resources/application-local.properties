spring.profiles.active=dev

krpc.registry.addrs=consul-dev.evcard.vip:8500
krpc.monitor.serverAddr=krpc-monitor-dev.gcsrental.com:7002
krpc.application.traceAdapter=cat:server=krpc-ktrace-dev.gcsrental.com:8003

krpc.webserver.port=32557
krpc.webserver.autoRoute=true

spring.datasource.druid.url=**************************************************************************************************************************************************************************************
spring.datasource.druid.username=devuser
spring.datasource.druid.password=blk2ZsEB

spring.redis.host=evcard-dev-lan.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.database=0
spring.redis.password=EiK2jUv9VTaF

apollo.meta= http://apollo-dev.evcard.vip:58080
dubbo.proxy.rest.prefix=http://inner-dev.evcard.vip/md-rest

