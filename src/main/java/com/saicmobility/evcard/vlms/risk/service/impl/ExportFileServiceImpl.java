package com.saicmobility.evcard.vlms.risk.service.impl;

import com.saicmobility.evcard.vlms.risk.service.ExportFileService;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-02-25
 */
@Service
public class ExportFileServiceImpl implements ExportFileService {

    @Resource
    private VlmsOperationService vlmsOperationService;

    @Override
    public Integer startExport(Integer fileSource, String fileName, String filePath, String operator,String userAccount,
                               String orgId, String orgName) {
        //拼接参数
        CurrentUser currentUser = CurrentUser.newBuilder()
                .setNickName(operator)
                .setUserAccount(userAccount)
                .setOrgId(orgId)
                .setOrgName(orgName)
                .build();

        AsyncCreateLifeCycleFileReq request = AsyncCreateLifeCycleFileReq.newBuilder()
                .setFileSource(fileSource)
                .setFileName(fileName)
                .setFilePath(filePath)
                .setFileSystem(1)
                .setCurrentUser(currentUser)
                .build();
        AsyncCreateLifeCycleFileRes response = vlmsOperationService.asyncCreateLifeCycleFile(request);
        return response.getFileId();
    }

    @Override
    public void exportSuccess(Integer fileId, String operator) {
        CurrentUser currentUser = CurrentUser.newBuilder().setNickName(operator).build();
        UpdateAsyncLifeCycleFileStatusReq request = UpdateAsyncLifeCycleFileStatusReq.newBuilder()
                .setFileId(fileId)
                .setFileStatus(2)
                .setCurrentUser(currentUser)
                .build();
        vlmsOperationService.updateAsyncLifeCycleFileStatus(request);

    }

    @Override
    public void exportFail(Integer fileId, String reason, String operator) {
        CurrentUser currentUser = CurrentUser.newBuilder().setNickName(operator).build();
        UpdateAsyncLifeCycleFileStatusReq request = UpdateAsyncLifeCycleFileStatusReq.newBuilder()
                .setFileId(fileId)
                .setFileStatus(3)
                .setRemark(reason)
                .setCurrentUser(currentUser)
                .build();
        vlmsOperationService.updateAsyncLifeCycleFileStatus(request);
    }
}
