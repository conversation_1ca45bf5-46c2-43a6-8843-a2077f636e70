package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理状态 1-执行中 2-已执行
 */
@AllArgsConstructor
@Getter
public enum DealStatusEnum {
    DEAL_STATUS(1, "执行中"),
    DEAL_RECOVER(2, "已执行"),
    DEAL_APPROVAL_PENDING(3, "审批中"),
    ;

    private Integer code;
    private String value;
    public static String getValueByCode(Integer code)
    {
        for (DealStatusEnum dealStatusEnum : DealStatusEnum.values()) {
            if (dealStatusEnum.getCode().equals(code)) {
                return dealStatusEnum.getValue();
            }
        }
        return null;
    }
}
