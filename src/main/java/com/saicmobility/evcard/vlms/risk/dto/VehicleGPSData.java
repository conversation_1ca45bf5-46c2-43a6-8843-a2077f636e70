package com.saicmobility.evcard.vlms.risk.dto;

import lombok.Data;

import java.util.Date;

@Data
public class VehicleGPSData {

    private Long gpsDateTime;

    private String latitude;

    private String longitude;

    private String cityName;

    private String lastAddress;

    private Date pauseDeadlineTime;

    /**
     * 初始值与gpsDateTime相同，后面不会被覆盖，存入最后一次定位时间
     */
    private Long oldGpsDateTime;

    private String penCityScope;
}
