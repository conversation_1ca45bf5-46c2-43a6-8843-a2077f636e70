package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckExtendInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckExtendInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskCheckExtendInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, riskCheckId, collectCarDatetime, collectCarPlace, collectCarPeople, collectCarType, collectCarRiskType, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName, province, city, address);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskCheckExtendInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskCheckExtendInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="risk_check_id", property="riskCheckId", jdbcType=JdbcType.BIGINT),
        @Result(column="collect_car_datetime", property="collectCarDatetime", jdbcType=JdbcType.VARCHAR),
        @Result(column="collect_car_place", property="collectCarPlace", jdbcType=JdbcType.VARCHAR),
        @Result(column="collect_car_people", property="collectCarPeople", jdbcType=JdbcType.VARCHAR),
        @Result(column="collect_car_type", property="collectCarType", jdbcType=JdbcType.INTEGER),
        @Result(column="collect_car_risk_type", property="collectCarRiskType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="province", property="province", jdbcType=JdbcType.VARCHAR),
        @Result(column="city", property="city", jdbcType=JdbcType.VARCHAR),
        @Result(column="address", property="address", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskCheckExtendInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskCheckExtendInfoResult")
    Optional<RiskCheckExtendInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskCheckExtendInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskCheckExtendInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int insert(RiskCheckExtendInfo row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckExtendInfo, c ->
            c.map(riskCheckId).toProperty("riskCheckId")
            .map(collectCarDatetime).toProperty("collectCarDatetime")
            .map(collectCarPlace).toProperty("collectCarPlace")
            .map(collectCarPeople).toProperty("collectCarPeople")
            .map(collectCarType).toProperty("collectCarType")
            .map(collectCarRiskType).toProperty("collectCarRiskType")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
            .map(province).toProperty("province")
            .map(city).toProperty("city")
            .map(address).toProperty("address")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int insertSelective(RiskCheckExtendInfo row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckExtendInfo, c ->
            c.map(riskCheckId).toPropertyWhenPresent("riskCheckId", row::getRiskCheckId)
            .map(collectCarDatetime).toPropertyWhenPresent("collectCarDatetime", row::getCollectCarDatetime)
            .map(collectCarPlace).toPropertyWhenPresent("collectCarPlace", row::getCollectCarPlace)
            .map(collectCarPeople).toPropertyWhenPresent("collectCarPeople", row::getCollectCarPeople)
            .map(collectCarType).toPropertyWhenPresent("collectCarType", row::getCollectCarType)
            .map(collectCarRiskType).toPropertyWhenPresent("collectCarRiskType", row::getCollectCarRiskType)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
            .map(province).toPropertyWhenPresent("province", row::getProvince)
            .map(city).toPropertyWhenPresent("city", row::getCity)
            .map(address).toPropertyWhenPresent("address", row::getAddress)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default Optional<RiskCheckExtendInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskCheckExtendInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default List<RiskCheckExtendInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskCheckExtendInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default List<RiskCheckExtendInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskCheckExtendInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default Optional<RiskCheckExtendInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskCheckExtendInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskCheckExtendInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(riskCheckId).equalTo(row::getRiskCheckId)
                .set(collectCarDatetime).equalTo(row::getCollectCarDatetime)
                .set(collectCarPlace).equalTo(row::getCollectCarPlace)
                .set(collectCarPeople).equalTo(row::getCollectCarPeople)
                .set(collectCarType).equalTo(row::getCollectCarType)
                .set(collectCarRiskType).equalTo(row::getCollectCarRiskType)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName)
                .set(province).equalTo(row::getProvince)
                .set(city).equalTo(row::getCity)
                .set(address).equalTo(row::getAddress);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskCheckExtendInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(riskCheckId).equalToWhenPresent(row::getRiskCheckId)
                .set(collectCarDatetime).equalToWhenPresent(row::getCollectCarDatetime)
                .set(collectCarPlace).equalToWhenPresent(row::getCollectCarPlace)
                .set(collectCarPeople).equalToWhenPresent(row::getCollectCarPeople)
                .set(collectCarType).equalToWhenPresent(row::getCollectCarType)
                .set(collectCarRiskType).equalToWhenPresent(row::getCollectCarRiskType)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
                .set(province).equalToWhenPresent(row::getProvince)
                .set(city).equalToWhenPresent(row::getCity)
                .set(address).equalToWhenPresent(row::getAddress);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int updateByPrimaryKey(RiskCheckExtendInfo row) {
        return update(c ->
            c.set(riskCheckId).equalTo(row::getRiskCheckId)
            .set(collectCarDatetime).equalTo(row::getCollectCarDatetime)
            .set(collectCarPlace).equalTo(row::getCollectCarPlace)
            .set(collectCarPeople).equalTo(row::getCollectCarPeople)
            .set(collectCarType).equalTo(row::getCollectCarType)
            .set(collectCarRiskType).equalTo(row::getCollectCarRiskType)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .set(province).equalTo(row::getProvince)
            .set(city).equalTo(row::getCity)
            .set(address).equalTo(row::getAddress)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    default int updateByPrimaryKeySelective(RiskCheckExtendInfo row) {
        return update(c ->
            c.set(riskCheckId).equalToWhenPresent(row::getRiskCheckId)
            .set(collectCarDatetime).equalToWhenPresent(row::getCollectCarDatetime)
            .set(collectCarPlace).equalToWhenPresent(row::getCollectCarPlace)
            .set(collectCarPeople).equalToWhenPresent(row::getCollectCarPeople)
            .set(collectCarType).equalToWhenPresent(row::getCollectCarType)
            .set(collectCarRiskType).equalToWhenPresent(row::getCollectCarRiskType)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .set(province).equalToWhenPresent(row::getProvince)
            .set(city).equalToWhenPresent(row::getCity)
            .set(address).equalToWhenPresent(row::getAddress)
            .where(id, isEqualTo(row::getId))
        );
    }
}