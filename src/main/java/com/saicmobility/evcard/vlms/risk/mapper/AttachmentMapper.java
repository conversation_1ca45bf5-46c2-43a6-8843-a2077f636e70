package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.AttachmentDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.Attachment;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

@Mapper
public interface AttachmentMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<Attachment> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    BasicColumn[] selectList = BasicColumn.columnList(id, fileType, relationId, fileName, filePath, fileSize, contentType, fileDesc, isDeleted, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<Attachment> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AttachmentResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="file_type", property="fileType", jdbcType=JdbcType.INTEGER),
        @Result(column="relation_id", property="relationId", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_name", property="fileName", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_path", property="filePath", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_size", property="fileSize", jdbcType=JdbcType.BIGINT),
        @Result(column="content_type", property="contentType", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_desc", property="fileDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<Attachment> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AttachmentResult")
    Optional<Attachment> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, attachment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, attachment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int insert(Attachment row) {
        return MyBatis3Utils.insert(this::insert, row, attachment, c ->
            c.map(fileType).toProperty("fileType")
            .map(relationId).toProperty("relationId")
            .map(fileName).toProperty("fileName")
            .map(filePath).toProperty("filePath")
            .map(fileSize).toProperty("fileSize")
            .map(contentType).toProperty("contentType")
            .map(fileDesc).toProperty("fileDesc")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int insertSelective(Attachment row) {
        return MyBatis3Utils.insert(this::insert, row, attachment, c ->
            c.map(fileType).toPropertyWhenPresent("fileType", row::getFileType)
            .map(relationId).toPropertyWhenPresent("relationId", row::getRelationId)
            .map(fileName).toPropertyWhenPresent("fileName", row::getFileName)
            .map(filePath).toPropertyWhenPresent("filePath", row::getFilePath)
            .map(fileSize).toPropertyWhenPresent("fileSize", row::getFileSize)
            .map(contentType).toPropertyWhenPresent("contentType", row::getContentType)
            .map(fileDesc).toPropertyWhenPresent("fileDesc", row::getFileDesc)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default Optional<Attachment> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, attachment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default List<Attachment> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, attachment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default List<Attachment> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, attachment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default Optional<Attachment> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, attachment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    static UpdateDSL<UpdateModel> updateAllColumns(Attachment row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(fileType).equalTo(row::getFileType)
                .set(relationId).equalTo(row::getRelationId)
                .set(fileName).equalTo(row::getFileName)
                .set(filePath).equalTo(row::getFilePath)
                .set(fileSize).equalTo(row::getFileSize)
                .set(contentType).equalTo(row::getContentType)
                .set(fileDesc).equalTo(row::getFileDesc)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(Attachment row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(fileType).equalToWhenPresent(row::getFileType)
                .set(relationId).equalToWhenPresent(row::getRelationId)
                .set(fileName).equalToWhenPresent(row::getFileName)
                .set(filePath).equalToWhenPresent(row::getFilePath)
                .set(fileSize).equalToWhenPresent(row::getFileSize)
                .set(contentType).equalToWhenPresent(row::getContentType)
                .set(fileDesc).equalToWhenPresent(row::getFileDesc)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int updateByPrimaryKey(Attachment row) {
        return update(c ->
            c.set(fileType).equalTo(row::getFileType)
            .set(relationId).equalTo(row::getRelationId)
            .set(fileName).equalTo(row::getFileName)
            .set(filePath).equalTo(row::getFilePath)
            .set(fileSize).equalTo(row::getFileSize)
            .set(contentType).equalTo(row::getContentType)
            .set(fileDesc).equalTo(row::getFileDesc)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    default int updateByPrimaryKeySelective(Attachment row) {
        return update(c ->
            c.set(fileType).equalToWhenPresent(row::getFileType)
            .set(relationId).equalToWhenPresent(row::getRelationId)
            .set(fileName).equalToWhenPresent(row::getFileName)
            .set(filePath).equalToWhenPresent(row::getFilePath)
            .set(fileSize).equalToWhenPresent(row::getFileSize)
            .set(contentType).equalToWhenPresent(row::getContentType)
            .set(fileDesc).equalToWhenPresent(row::getFileDesc)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}