package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskAlarmDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    public static final RiskAlarm riskAlarm = new RiskAlarm();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.id")
    public static final SqlColumn<Long> id = riskAlarm.id;

    /**
     * Database Column Remarks:
     *   风控报警编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_no")
    public static final SqlColumn<String> alarmNo = riskAlarm.alarmNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.vin")
    public static final SqlColumn<String> vin = riskAlarm.vin;

    /**
     * Database Column Remarks:
     *   关联订单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_seq")
    public static final SqlColumn<String> orderSeq = riskAlarm.orderSeq;

    /**
     * Database Column Remarks:
     *   订单类型（1、门店订单 2、渠道订单 3、长租订单 4、短租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.order_type")
    public static final SqlColumn<Integer> orderType = riskAlarm.orderType;

    /**
     * Database Column Remarks:
     *   报警类型 1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失控
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_type")
    public static final SqlColumn<Integer> alarmType = riskAlarm.alarmType;

    /**
     * Database Column Remarks:
     *   报警等级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_level")
    public static final SqlColumn<Integer> alarmLevel = riskAlarm.alarmLevel;

    /**
     * Database Column Remarks:
     *   报警状态（1-报警中 2-已恢复）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_status")
    public static final SqlColumn<Integer> alarmStatus = riskAlarm.alarmStatus;

    /**
     * Database Column Remarks:
     *   恢复方式（1-人工 2-自动）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_method")
    public static final SqlColumn<Integer> recoverMethod = riskAlarm.recoverMethod;

    /**
     * Database Column Remarks:
     *   恢复方式（1-梧桐 2-长租）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recover_channel")
    public static final SqlColumn<Integer> recoverChannel = riskAlarm.recoverChannel;

    /**
     * Database Column Remarks:
     *   报警系统（1-梧桐 2-长租）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_system")
    public static final SqlColumn<Integer> alarmSystem = riskAlarm.alarmSystem;

    /**
     * Database Column Remarks:
     *   报警时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_time")
    public static final SqlColumn<Date> alarmTime = riskAlarm.alarmTime;

    /**
     * Database Column Remarks:
     *   最后一次定位地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location")
    public static final SqlColumn<String> lastLocation = riskAlarm.lastLocation;

    /**
     * Database Column Remarks:
     *   最后一次定位时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.last_location_time")
    public static final SqlColumn<Date> lastLocationTime = riskAlarm.lastLocationTime;

    /**
     * Database Column Remarks:
     *   是否暂停报警（1-是 0-否）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_stop_alarm")
    public static final SqlColumn<Integer> isStopAlarm = riskAlarm.isStopAlarm;

    /**
     * Database Column Remarks:
     *   暂停报警升级时长（天）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.stop_alarm_day")
    public static final SqlColumn<Integer> stopAlarmDay = riskAlarm.stopAlarmDay;

    /**
     * Database Column Remarks:
     *   报警升级暂停截止时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.pause_deadline_time")
    public static final SqlColumn<Date> pauseDeadlineTime = riskAlarm.pauseDeadlineTime;

    /**
     * Database Column Remarks:
     *   恢复时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.recovery_date")
    public static final SqlColumn<Date> recoveryDate = riskAlarm.recoveryDate;

    /**
     * Database Column Remarks:
     *   报警说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.alarm_desc")
    public static final SqlColumn<String> alarmDesc = riskAlarm.alarmDesc;

    /**
     * Database Column Remarks:
     *   升级说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.upgrade_desc")
    public static final SqlColumn<String> upgradeDesc = riskAlarm.upgradeDesc;

    /**
     * Database Column Remarks:
     *   出围栏时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.exit_fence_date")
    public static final SqlColumn<Date> exitFenceDate = riskAlarm.exitFenceDate;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.is_deleted")
    public static final SqlColumn<Integer> isDeleted = riskAlarm.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.misc_desc")
    public static final SqlColumn<String> miscDesc = riskAlarm.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_time")
    public static final SqlColumn<Date> createTime = riskAlarm.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_account")
    public static final SqlColumn<String> createOperAccount = riskAlarm.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.create_oper_name")
    public static final SqlColumn<String> createOperName = riskAlarm.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_time")
    public static final SqlColumn<Date> updateTime = riskAlarm.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = riskAlarm.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_alarm.update_oper_name")
    public static final SqlColumn<String> updateOperName = riskAlarm.updateOperName;



    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    public static final class RiskAlarm extends AliasableSqlTable<RiskAlarm> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> alarmNo = column("alarm_no", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> orderSeq = column("order_seq", JDBCType.VARCHAR);

        public final SqlColumn<Integer> orderType = column("order_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> alarmType = column("alarm_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> alarmLevel = column("alarm_level", JDBCType.INTEGER);

        public final SqlColumn<Integer> alarmStatus = column("alarm_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> recoverMethod = column("recover_method", JDBCType.INTEGER);

        public final SqlColumn<Integer> recoverChannel = column("recover_channel", JDBCType.INTEGER);

        public final SqlColumn<Integer> alarmSystem = column("alarm_system", JDBCType.INTEGER);

        public final SqlColumn<Date> alarmTime = column("alarm_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> lastLocation = column("last_location", JDBCType.VARCHAR);

        public final SqlColumn<Date> lastLocationTime = column("last_location_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> isStopAlarm = column("is_stop_alarm", JDBCType.INTEGER);

        public final SqlColumn<Integer> stopAlarmDay = column("stop_alarm_day", JDBCType.INTEGER);

        public final SqlColumn<Date> pauseDeadlineTime = column("pause_deadline_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> recoveryDate = column("recovery_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> alarmDesc = column("alarm_desc", JDBCType.VARCHAR);

        public final SqlColumn<String> upgradeDesc = column("upgrade_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> exitFenceDate = column("exit_fence_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public RiskAlarm() {
            super("t_risk_alarm", RiskAlarm::new);
        }
    }
}