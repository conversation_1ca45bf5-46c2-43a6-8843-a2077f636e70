package com.saicmobility.evcard.vlms.risk.error;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 */
public class ServiceException extends RuntimeException{

    @Getter
    private final ResultCode resultCode;

    public ServiceException(String message) {
        super(message);
        this.resultCode = ResultCode.COMMON_FAIL;
    }

    public ServiceException(ResultCode resultCode){
        super(resultCode.getMessage());
        this.resultCode = resultCode;
    }

    public ServiceException(ResultCode resultCode, String msg){
        super(msg);
        this.resultCode = resultCode;
    }

    public ServiceException(ResultCode resultCode, Throwable cause){
        super(cause);
        this.resultCode  = resultCode;
    }

    public ServiceException(String msg, Throwable cause){
        super(msg, cause);
        this.resultCode = ResultCode.FAILURE;
    }


    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

    public Throwable doFillInStackTrace(){
        return super.fillInStackTrace();
    }
}
