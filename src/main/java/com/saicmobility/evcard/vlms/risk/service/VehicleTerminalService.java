package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

/**
 * <AUTHOR>
 * @date 2024-04-09
 */
public interface VehicleTerminalService {


    /**
     * 查询终端信息
     * @param queryTerminalInfoByVinReq
     * @return
     */
    QueryTerminalInfoByVinRes queryTerminalInfoByVin(QueryTerminalInfoByVinReq queryTerminalInfoByVinReq);

    /**
     * 查询车辆轨迹
     * @param queryVehicleTrackReq
     * @return
     */
    QueryVehicleTrackRes queryVehicleTrack(QueryVehicleTrackReq queryVehicleTrackReq);

    /**
     * 查询订单行程
     * @param queryOrderTravelListByOrderSeqReq
     * @return
     */
    QueryOrderTravelListByOrderSeqRes queryOrderTravelListByOrderSeq(QueryOrderTravelListByOrderSeqReq queryOrderTravelListByOrderSeqReq);

    /**
     * 查询城市配置
     * @param queryCityConfigListReq
     * @return
     */
    QueryCityConfigListRes queryCityConfigList(QueryCityConfigListReq queryCityConfigListReq);

    /**
     * 导出车辆轨迹
     * @param queryVehicleTrackReq
     * @return
     */
    ExportVehicleTrackRes exportVehicleTrack(QueryVehicleTrackReq queryVehicleTrackReq);
}
