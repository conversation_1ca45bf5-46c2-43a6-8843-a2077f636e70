spring.profiles.active=sit
spring.main.banner-mode=off
spring.main.web-application-type=none
spring.application.name=vlmsriskservice

git.name=vlms-risk-service
service.version=vlmsriskservice build @ 20221222
krpc.enabled=true
krpc.registry.type=consul
krpc.registry.addrs=consul-dev.evcard.vip:8500
#krpc.registry.aclToken=

krpc.application.dataDir=/opt/data/${git.name}
krpc.monitor.selfCheckPort=12557

krpc.server.port=22557
krpc.server.threads=100
krpc.server.maxThreads=200
krpc.service.interfaceName=com.saicmobility.evcard.vlms.vlmsriskservice.api.VlmsRiskService
krpc.client.maxPackageSize=50000000
krpc.server.maxPackageSize=50000000
krpc.monitor.serverAddr=krpc-monitor-dev.gcsrental.com:7002
krpc.application.traceAdapter=cat:server=krpc-ktrace-dev.gcsrental.com:8003


bpe.enabled=false
bpe.rest.enabled=false

#apollo
app.id = vlms-risk-service
apollo.bootstrap.enabled= true
apollo.bootstrap.namespaces= application
apollo.bootstrap.eagerLoad.enabled=true

#datasource
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.max-active=100
spring.datasource.druid.initial-size=1
spring.datasource.druid.min-idle=1
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=select 'x'
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false

mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

aes.key=vvvnnTaXTVVZ6k54
aes.iv=9990294116389916

oss.prefixUrl = http://evcard.oss-cn-shanghai.aliyuncs.com/
mybatis.mapper-locations=classpath*:mapper/*.xml

krpc.referers[0].interfaceName=com.saicmobility.team.hqoaauthservice.api.HqOaAuthService
krpc.referers[1].interfaceName=com.saicmobility.team.hqoaform.api.HqOaForm
##???????
krpc.referers[2].interfaceName=com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy
krpc.referers[2].timeout=30000
#krpc.referers[2].direct=localhost:22560
## 辆资产服务
krpc.referers[3].interfaceName=com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService
krpc.referers[3].timeout=30000
#krpc.referers[3].direct=localhost:22505
krpc.referers[4].interfaceName=com.saicmobility.evcard.vlms.vlmsoperationservice.api.VlmsOperationService
krpc.referers[4].timeout=30000
krpc.referers[5].interfaceName=com.saicmobility.evcard.vlms.vlmsmodelcenterservice.api.VlmsModelCenterService
krpc.referers[5].timeout=30000
krpc.referers[6].interfaceName=com.saicmobility.evcard.vlms.vlmsadmingtw.api.VlmsAdminGtw
krpc.referers[6].timeout=30000
krpc.referers[7].interfaceName=com.saicmobility.evcard.vlms.vlmsthirddependentservice.api.VlmsThirdDependentService
krpc.referers[7].timeout=30000
krpc.referers[8].interfaceName=com.saicmobility.evcard.vlms.vlmspurchaseservice.api.VlmsPurchaseService
krpc.referers[8].timeout=30000
krpc.referers[9].interfaceName=com.saicmobility.evcard.bfc.bfcinsuranceservice.api.BfcInsuranceService
krpc.referers[9].timeout=30000
krpc.referers[10].interfaceName=com.saicmobility.evcard.vlms.vlmsriskservice.api.VlmsRiskService
krpc.referers[10].timeout=30000
krpc.referers[11].interfaceName=com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService
krpc.referers[11].timeout=30000
krpc.referers[12].interfaceName=com.saicmobility.evcard.md.mdordercenter.api.MdOrderCenter
krpc.referers[12].timeout=30000
#krpc.referers[12].direct=localhost:22530
krpc.referers[13].interfaceName=com.saicmobility.evcard.md.mdworkservice.api.MdWorkService
krpc.referers[13].timeout=30000

dubbo.proxy.order.orderTravelList=${dubbo.proxy.rest.prefix}/api/order/orderTravelList
dubbo.proxy.vehicle.queryVehicleHistoryTrack=${dubbo.proxy.rest.prefix}/api/vehicle/queryVehicleHistoryTrack
#redis通用配置 ,
spring.redis.port = 6379
spring.redis.database = 0
spring.redis.timeout=30000
spring.redis.lettuce.pool.max-active=1000
spring.redis.lettuce.pool.max-idle=16

baiYangRiskCheckCompleteKey=risk_check_complete