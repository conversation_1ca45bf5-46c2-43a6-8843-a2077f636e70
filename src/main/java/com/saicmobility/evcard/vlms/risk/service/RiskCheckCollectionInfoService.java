package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

public interface RiskCheckCollectionInfoService {


    /**
     * 查询风控收车日志信息
     * @param queryRiskCheckCollectionInfoReq
     * @return
     */
    QueryRiskCheckCollectionInfoRes queryRiskCheckCollectionInfo(QueryRiskCheckCollectionInfoReq queryRiskCheckCollectionInfoReq);

    /**
     * 导出风控收车日志信息
     * @param queryRiskCheckCollectionInfoReq
     * @return
     */
    ExportRiskCheckCollectionInfoRes exportRiskCheckCollectionInfo(ExportRiskCheckCollectionInfoReq queryRiskCheckCollectionInfoReq);

    /**
     * 新增风控收车日志信息
     * @param addRiskCheckCollectionInfoReq
     * @return
     */
    AddRiskCheckCollectionInfoRes addRiskCheckCollectionInfo(AddRiskCheckCollectionInfoReq addRiskCheckCollectionInfoReq);

    /**
     * 批量新增风控收车日志信息
     * @param batchAddRiskCheckCollectionInfoReq
     * @return
     */
    batchAddRiskCheckCollectionInfoRes batchAddRiskCheckCollectionInfo(batchAddRiskCheckCollectionInfoReq batchAddRiskCheckCollectionInfoReq);
}
