package com.saicmobility.evcard.vlms.risk.database.impl;

import com.mysql.cj.xdevapi.DeleteStatement;
import com.saicmobility.evcard.vlms.risk.database.TableAttachmentService;
import com.saicmobility.evcard.vlms.risk.mapper.AttachmentMapper;
import com.saicmobility.evcard.vlms.risk.mapper.extend.AttachmentExtendMapper;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.AttachmentDynamicSqlSupport.attachment;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2024-01-19
 */
@Service
@Slf4j
public class TableAttachmentServiceImpl implements TableAttachmentService {

    @Resource
    private AttachmentMapper attachmentMapper;


    @Resource
    private AttachmentExtendMapper attachmentExtendMapper;

    @Override
    public Attachment selectById(Long id) {
        return attachmentMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public void insert(Attachment attachment) {
        attachmentMapper.insertSelective(attachment);
    }

    @Override
    public void batchInsert(List<Attachment> attachmentList) {
        attachmentExtendMapper.insertMultiple(attachmentList);
    }

    @Override
    public List<Attachment> queryAttachmentByFileType(int fileType, String relationId) {
        SelectStatementProvider selectStatement = select(attachment.allColumns())
                .from(attachment)
                .where(attachment.fileType,isEqualTo(fileType))
                .and(attachment.relationId,isEqualTo(relationId))
                .and(attachment.isDeleted,isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return attachmentExtendMapper.selectMany(selectStatement);
    }

    @Override
    public void delete(int fileType, String relationId) {
        UpdateStatementProvider updateProvider =  SqlBuilder.update(attachment)
                .set(attachment.isDeleted).equalTo(1)
                .where(attachment.relationId, isEqualTo(relationId))
                .and(attachment.fileType,isEqualTo(fileType)).build().render(RenderingStrategies.MYBATIS3);
        attachmentExtendMapper.update(updateProvider);
    }
}
