package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.model.VehicleMovementDayStatistics;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-21
 */
public interface TableVehicleMovementDayStatisticsService {

    /**
     * 根据车架号查询异动数据
     * @param vin
     * @return
     */
    List<VehicleMovementDayStatistics> queryVehicleMovementDayStatisticsByVin(String vin);

    /**
     * 根据车架号和时间查询
     * @param vin
     * @param yesterdayStr
     * @return
     */
    VehicleMovementDayStatistics queryDataByVinAndStatDate(String vin, String yesterdayStr);
}
