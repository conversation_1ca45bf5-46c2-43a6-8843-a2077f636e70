package com.saicmobility.evcard.vlms.risk.utils;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class DateUtil {
    public static final DateTimeFormatter DATE_TYPE1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    public static final DateTimeFormatter DATE_TYPE3 = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    public static final DateTimeFormatter DATE_TYPE4 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter DATE_TYPE5 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_TYPE6 = DateTimeFormatter.ofPattern("yy-MM-dd-HH-mm-ss");
    public static final DateTimeFormatter DATE_TYPE7 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter DATE_TYPE8 = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter DATE_TYPE9 = DateTimeFormatter.ofPattern("yyyy-M-d H:m:s:S");
    public static final DateTimeFormatter DATE_TYPE10 = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    public static final DateTimeFormatter DATE_TYPE11 = DateTimeFormatter.ofPattern("yyyy-M-d H:m:s");
    public static final DateTimeFormatter DATE_TYPE12 = DateTimeFormatter.ofPattern("yy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE13 = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE14 = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TYPE15 = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm");
    public static final DateTimeFormatter DATE_TYPE16 = DateTimeFormatter.ofPattern("MM月dd日 HH:mm");
    public static final DateTimeFormatter DATE_TYPE17 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    public static final DateTimeFormatter DATE_TYPE18 = DateTimeFormatter.ofPattern("yyMMddHHmmss");
    public static final DateTimeFormatter DATE_TYPE19 = DateTimeFormatter.ofPattern("yyyy-MM");
    public static final DateTimeFormatter DATE_TYPE20 = DateTimeFormatter.ofPattern("yyyyMM");
    public static final DateTimeFormatter DATE_TYPE21 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
    public static final DateTimeFormatter DATE_TYPE22 = DateTimeFormatter.ofPattern("yyyyMMdd H:mm");
    public static final DateTimeFormatter DATE_TYPE23 = DateTimeFormatter.ofPattern("HH:mm");
    public static final DateTimeFormatter DATE_TYPE24 = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'");
    public static final DateTimeFormatter DATE_TYPE25 = DateTimeFormatter.ofPattern("yyMMddHH");
    public static final DateTimeFormatter DATE_TYPE26 = DateTimeFormatter.ofPattern("yyMMdd");
    public static ZoneId timeZoneChina = ZoneId.of("Asia/Shanghai");
    public static TimeZone timeZoneChina2 = TimeZone.getTimeZone("Asia/Shanghai");// 获取时区
    public DateUtil() {
    }

    public static String dateToString(LocalDateTime date, DateTimeFormatter pattern) {
        return date.format(pattern);
    }

    public static String dateToString(Date date, DateTimeFormatter pattern) {
        if (date == null) {
            return "";
        } else {
            LocalDateTime ldt = date.toInstant().atZone(timeZoneChina).toLocalDateTime();
            return ldt.format(pattern);
        }
    }

    public static long getClockMillis() {
        Clock clock = Clock.systemDefaultZone();
        return clock.millis();
    }

    public static String timestampToDateStr(String timestamp, DateTimeFormatter format) {
        return Instant.ofEpochMilli(Long.parseLong(timestamp)).atZone(timeZoneChina).toLocalDateTime().format(format);
    }

    public static String timestampToDateStr(long timestamp, DateTimeFormatter format) {
        return Instant.ofEpochMilli(timestamp).atZone(timeZoneChina).toLocalDateTime().format(format);
    }

    public static LocalDateTime timestampToDate(long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(timeZoneChina).toLocalDateTime();
    }

    public static String dateToString(LocalDateTime date) {
        return dateToString(date, DATE_TYPE1);
    }

    public static String getSystemDate(DateTimeFormatter format) {
        return LocalDateTime.now().atZone(timeZoneChina).format(format);
    }

    public static LocalDateTime getSystemDate() {
        return LocalDateTime.now().atZone(timeZoneChina).toLocalDateTime();
    }

    public static LocalDateTime AddMin(LocalDateTime date, int min) {
        return date.plusMinutes((long)min);
    }

    public static LocalDateTime addMin(Date date, int min) {
        LocalDateTime datetime = dateToLocalTimeDate(date);
        return AddMin(datetime, min);
    }

    public static LocalDateTime addSeconds(LocalDateTime date, int second) {
        return date.plusSeconds((long)second);
    }

    public static LocalDateTime addSeconds(Date time, int second) {
        LocalDateTime date = dateToLocalTimeDate(time);
        return addSeconds(date, second);
    }

    public static Date localTimeToDate(LocalDateTime date) {
        return Date.from(date.atZone(timeZoneChina).toInstant());
    }

    public static Date getSystemLocalDate2Date() {
        return localTimeToDate(getSystemDate());
    }

    public static LocalDateTime getDateFromStr(String dateStr, DateTimeFormatter fromType) {
        return LocalDateTime.parse(dateStr, fromType);
    }

    public static LocalDate getLocalDateFromStr(String dateStr, DateTimeFormatter fromType) {
        return LocalDate.parse(dateStr, fromType);
    }

    public static Date getDateFromTimeStr(String dateStr, DateTimeFormatter fromType) {
        return localTimeToDate(LocalDateTime.parse(dateStr, fromType));
    }

    public static Date getDateFromTimeStr(String dateStr, DateTimeFormatter fromType, Date defaultValue) {
        try {
            return localTimeToDate(LocalDateTime.parse(dateStr, fromType));
        } catch (Exception var4) {
            return defaultValue;
        }
    }

    public static Date getDateFromDateStr(String dateStr, DateTimeFormatter fromType) {
        LocalDate localDate = LocalDate.parse(dateStr, fromType);
        return Date.from(localDate.atStartOfDay(timeZoneChina).toInstant());
    }

    public static Date getDateFromDateStr1(String dateStr, DateTimeFormatter fromType) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateStr, fromType);
        return Date.from(localDateTime.atZone(timeZoneChina).toInstant());
    }

    public static LocalDateTime addDay(LocalDateTime date, int day) {
        return date.plusDays((long)day);
    }

    public static LocalDateTime addDay(Date time, int day) {
        LocalDateTime date = dateToLocalTimeDate(time);
        return date.plusDays((long)day);
    }

    public static LocalDateTime dateToLocalTimeDate(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        return localDateTime;
    }

    public static String getFormatDate(String dateStr, DateTimeFormatter fromType, DateTimeFormatter toType) {
        return dateToString(getDateFromStr(dateStr, fromType), toType);
    }

    public static long getDateUnix(LocalDateTime dateTime) {
        return dateTime.atZone(timeZoneChina).toEpochSecond();
    }

    public static long getDateUnixMilli(LocalDateTime dateTime) {
        return dateTime.atZone(timeZoneChina).toInstant().toEpochMilli();
    }

    public static boolean isValidDate(String str, DateTimeFormatter formatter) {
        try {
            getDateFromStr(str, formatter);
            return true;
        } catch (DateTimeParseException var3) {
            return false;
        }
    }

    public static boolean isValidOnlyDate(String str, DateTimeFormatter formatter) {
        try {
            getLocalDateFromStr(str, formatter);
            return true;
        } catch (DateTimeParseException var3) {
            return false;
        }
    }

    public static String format(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    public static String format(long millis, String pattern) {
        return DateFormatUtils.format(millis, pattern);
    }

    public static Date parse(String str, String parsePattern) {
        try {
            return DateUtils.parseDate(str, new String[]{parsePattern});
        } catch (ParseException var3) {
            return null;
        }
    }

    public static boolean isSameDay(Date day1, Date day2) {
        String ds1 = dateToString(day1, DATE_TYPE8);
        String ds2 = dateToString(day2, DATE_TYPE8);
        return ds1.equals(ds2);
    }

    public static boolean isSameDay(long day1, long day2) {
        String ds1 = dateToString(new Date(day1), DATE_TYPE8);
        String ds2 = dateToString(new Date(day2), DATE_TYPE8);
        return ds1.equals(ds2);
    }

    public static int dayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(7) - 1;
        if (w < 0) {
            w = 0;
        }

        return w;
    }

    public static Date monthFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(5, 1);
        return calendar.getTime();
    }

    public static Date nextMonthFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(5, 1);
        calendar.add(2, 1);
        return calendar.getTime();
    }

    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getEndOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(timeZoneChina).toInstant());
    }

    public static Date getEndOfDay(String dateStr, DateTimeFormatter fromType) {
        LocalDate localDate = LocalDate.parse(dateStr, fromType);
        LocalDateTime endOfDay = localDate.atStartOfDay().with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(timeZoneChina).toInstant());
    }

    public static Date getStartOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(2, 0);
        cal.set(5, 1);
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        return cal.getTime();
    }

    public static Date getEndOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(2, 1);
        cal.set(5, 0);
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        return cal.getTime();
    }

    public static Date getStartOfWeek(Date date) {
        date = getDateFromDateStr(dateToString(date, DATE_TYPE8), DATE_TYPE8);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayWeek = cal.get(7);
        if (1 == dayWeek) {
            cal.add(5, -1);
        }

        cal.setFirstDayOfWeek(2);
        int day = cal.get(7);
        cal.add(5, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    public static Date getEndOfWeek() {
        Calendar currentDate = Calendar.getInstance();
        currentDate.setFirstDayOfWeek(2);
        currentDate.set(11, 23);
        currentDate.set(12, 59);
        currentDate.set(13, 59);
        currentDate.set(7, 1);
        return currentDate.getTime();
    }

    public static Long dayBetween(Date date1, Date date2) {
        LocalDate ld1 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date1.getTime()), ZoneId.systemDefault()).toLocalDate();
        LocalDate ld2 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date2.getTime()), ZoneId.systemDefault()).toLocalDate();
        return ld1.until(ld2, ChronoUnit.DAYS);
    }

    public static Long secondBetween(Date date1, Date date2) {
        LocalDateTime ld1 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date1.getTime()), ZoneId.systemDefault());
        LocalDateTime ld2 = LocalDateTime.ofInstant(Instant.ofEpochMilli(date2.getTime()), ZoneId.systemDefault());
        return ld1.until(ld2, ChronoUnit.SECONDS);
    }

    public static Date getDateFromStr(String dateStr, String fromType) {
        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            return dateFromFmt.parse(dateStr);
        } catch (ParseException var3) {
            var3.printStackTrace();
            return null;
        }
    }
    public static String convertDatetimeStr(String datetimeStr){
        LocalDateTime parse = LocalDateTime.parse(datetimeStr, DATE_TYPE1);
        String format = parse.format(DATE_TYPE4);
        return format;
    }

    /**
     * 计算分钟数(起始时间向下取整，结束时间向上取整)
     * @param smdate
     * @param bdate
     * @return
     */
    public static int minutesBetween(Date smdate, Date bdate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis() / (1000 * 60);
        cal.setTime(bdate);
        double time2 = cal.getTimeInMillis() / (1000 * 60.00);
        //分钟数
        BigDecimal minutes = new BigDecimal(time2 - time1);
        minutes = minutes.setScale(0, RoundingMode.UP);
        return minutes.intValue();
    }

    public static int getMonthByDate(Date date){
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        return  ca.get(Calendar.MONTH);
    }


    public static String  convertDateStr(Date date){
       if (date != null){
           return DateUtil.format(date, "yyyy-MM-dd");
       }
        return null;
    }


    public static Date  convertStrToDate(String dateStr, String format){
        if (dateStr != null){
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            try {
                return formatter.parse(dateStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 获取昨天的日期
     * @return
     */
    public static String getYesterdayDateStr(Integer num){
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(num);
        // 创建一个日期格式器用于格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return yesterday.format(formatter);
    }

    /**
     * 获取两个日期之间相差的天数(不满一天算0天)
     * @param afterDay
     * @param beforeDay
     * @return
     */
    public static long daysBetween(Date afterDay, Date beforeDay) {
        long difference =  (afterDay.getTime()-beforeDay.getTime())/86400000;
        return Math.abs(difference);
    }

    /**
     * 实时数据库时间转化成指定格式的字符串
     *
     * @param nSec       秒
     * @param nMsec      毫秒
     * @param formatType 转化后的格式
     * @return
     */
    public static String hdTimeTODateStr(long nSec, short nMsec, String formatType) {
        try {
            Date date = new Date(nSec + nMsec);
            return getFormatDate(date, formatType);
        } catch (Exception e) {
            return "";
        }
    }

    public static String getFormatDate(Date date, String toType) {

        try {
            DateFormat dateToFmt = new SimpleDateFormat(toType);
            dateToFmt.setTimeZone(timeZoneChina2);
            // 非空检查
            if (date == null) {
                return "";
            } else {
                return dateToFmt.format(date);
            }
        } catch (Exception e) {
            return "";
        }
    }
    // 假设时间戳格式是毫秒级的数字字符串
    private static final String TIMESTAMP_PATTERN = "\\d+";
    public static boolean compareDate(String dateTimeOne, String dateTimeTwo) {
        long timeOne = 0L;
        long timeTwo = 0L;
        if(dateTimeOne.matches(TIMESTAMP_PATTERN)){
            timeOne = Long.parseLong(dateTimeOne);
        }else {
            timeOne = DateUtil.getDateFromTimeStr(dateTimeOne, DateUtil.DATE_TYPE1).getTime();
        }
        if(dateTimeTwo.matches(TIMESTAMP_PATTERN)){
            timeTwo = Long.parseLong(dateTimeTwo);
        }else {
            timeTwo = DateUtil.getDateFromTimeStr(dateTimeTwo, DateUtil.DATE_TYPE1).getTime();
        }
        return timeOne >= timeTwo;
    }
}

