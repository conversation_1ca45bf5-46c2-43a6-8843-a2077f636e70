package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.md.mdordercenter.api.*;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckService;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.*;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 定时恢复风控报警
 * <AUTHOR>
 * @date 2024-02-22
 */

@Slf4j
@JobHandler(value = "autoRecoveryRisk")
@Component
public class AutoRecoveryRiskHandler extends IJobHandler {

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private TableRiskCheckService tableRiskCheckService;

    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private MdOrderCenter mdOrderCenter;

    @Resource
    private RiskCommonService baseService;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private SubRiskAlaramService subRiskAlaramService;

    private static final int LONG_TERM_PRODUCT_LINE_DAYS = 7;
    private static final int SHORT_TERM_PRODUCT_LINE_DAYS = 3;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        // 查询订单
        doOrder();
        // 查询 年检到期和保险到期的车辆
        doInsuranceExpire();
        return ReturnT.SUCCESS;
    }

    private void doInsuranceExpire() {
        // 年检临期
        doAnnualInsurance();
        // 保险临期
        doInsurance();
    }

    /**
     * 保险临期
     */
    private void doInsurance() {
        // 保险到期
        List<RiskAlarm> insuranceRiskAlarmList = tableRiskAlarmService.queryDataByType(AlarmTypeEnum.INSURANCE_ADVENT.getCode());
        if (CollectionUtil.isEmpty(insuranceRiskAlarmList)){
            return;
        }
        for (RiskAlarm riskAlarm : insuranceRiskAlarmList) {
            try {
                AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskAlarm.getVin());
                if (vehicleInfoByVin == null){
                    continue;
                }
                // 车管中心车辆自动解除风险
                if (vehicleInfoByVin.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode()){
                    removeRiskAlarm(riskAlarm, "车辆产品线是车管中心，自动解除风险");
                    continue;
                }
                Integer vehicleStep = getVehicleStep(vehicleInfoByVin);
                if (!ObjectUtil.equal(vehicleStep, VehiclePhaseEnum.OPERATING.getCode())){
                    removeRiskAlarm(riskAlarm, "车辆不在固资运营阶段，自动解除风险");
                }
                String compulsoryInsuranceExpireStr = vehicleInfoByVin.getCompulsoryInsuranceExpire();
                if (StringUtils.isBlank(compulsoryInsuranceExpireStr)){
                    continue;
                }
                String expireDateStr = compulsoryInsuranceExpireStr.split(" ")[0];
                // 数据库存的是年月日，需要加一天
                LocalDate expireDate = LocalDate.parse(expireDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);

                long overDay = expireDate.toEpochDay() - LocalDate.now().toEpochDay();
                if (overDay >= 14){
                    removeRiskAlarm(riskAlarm, "交强险到期日大于当前时间，自动解除风险");
                }
            }catch (Exception e){
                log.error("恢复异常,车架号：{}", riskAlarm.getVin());
            }
        }

    }

    // 年检临期
    private void doAnnualInsurance() {
        log.info("年检临期处理");
        List<RiskAlarm> annualRiskAlarmList = tableRiskAlarmService.queryDataByType(AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
        if (CollectionUtil.isEmpty(annualRiskAlarmList)){
            return;
        }

        for (RiskAlarm riskAlarm : annualRiskAlarmList) {
            log.info("车架号:{}年检临期处理", riskAlarm.getVin());
            try {
                AssetsVehicle vehicle =  getVehicleInfoByVin(riskAlarm.getVin());
                String inspectionExpireDateStr = vehicle.getInspectionExpireDate();
                log.info("车架号:{}年检临期时间", inspectionExpireDateStr);
                if (StringUtils.isBlank(inspectionExpireDateStr)){
                    continue;
                }
                String expireDateStr = inspectionExpireDateStr.split(" ")[0];
                // 数据库存的是年月日，需要加一天
                LocalDate expireDate = LocalDate.parse(expireDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);

                // 开始时间
                RiskAlarmConfig alarmConfig = getAlarmStartTime(vehicle.getVin(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
                if (alarmConfig == null){
                    log.info("车架号:{} 配置为空", riskAlarm.getVin());
                    continue;
                }

                LocalDate startTime = formatDateToLocalDate(alarmConfig.getAlarmStartTime());
                if (startTime.isAfter(expireDate)){
                    expireDate = startTime;
                }
                // 如果 产品线是车管中心 expireDate 和当前时间比小于俩年则自动恢复
                if (vehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode() && ChronoUnit.YEARS.between(expireDate, LocalDate.now()) < 2){
                    removeRiskAlarm(riskAlarm, "产品线为车管中心，未超期2年以上，自动解除风险");
                    log.info("车架号:{} 是车管中心", riskAlarm.getVin());
                    continue;
                }

                // 获取车辆阶段
                Integer vehicleStep = getVehicleStep(vehicle);
                if (!ObjectUtil.equal(vehicleStep, VehiclePhaseEnum.OPERATING.getCode())){
                    // 非固资运营阶段 年检到期日和报警开始时间比较 小于俩年则恢复
                    Period difference = Period.between(expireDate, LocalDate.now());
                    if (difference.getYears() < 2){
                        removeRiskAlarm(riskAlarm, "自动解除风险");
                    }
                }else {
                    int productLine = vehicle.getProductLine();
                    if (productLine == ProductLineEnum.PRODUCT_LINE_ENUM_2.getCode()){
                        // 产品线长租
                        // 取年检到期时间，和报警开始时间比较，（取年检到期时间，报警开始时间，较大的时间开始计算）年检有效期不足7天，再比较报警升级暂停截止日期，如果大于暂停截止日期，则报警。
                        checkAnnualAlarm(LONG_TERM_PRODUCT_LINE_DAYS, riskAlarm, expireDate);
                    }else if (productLine == ProductLineEnum.PRODUCT_LINE_ENUM_3.getCode()){
                        // 产品线短租
                        // 取年检到期时间，和报警开始时间比较，（取年检到期时间，报警开始时间，较大的时间开始计算）年检有效期不足3天，再比较报警升级暂停截止日期，如果大于暂停截止日期，则报警
                        checkAnnualAlarm(SHORT_TERM_PRODUCT_LINE_DAYS, riskAlarm, expireDate);
                    }
                }



                long overDay = expireDate.toEpochDay() - LocalDate.now().toEpochDay();
                if (overDay >= 14){
                    removeRiskAlarm(riskAlarm, "年检到期日大于当前时间，自动解除风险");
                }
            }catch (Exception e){
                log.error("恢复处理错误:{}", riskAlarm.getVin());
            }
        }
    }

    private void checkAnnualAlarm(int days, RiskAlarm riskAlarm, LocalDate expireDate) {

        // 相差天数
        long diffDay = LocalDate.now().toEpochDay() - expireDate.toEpochDay();
        log.info("车架号:{}年检比较相差天数:{}", riskAlarm.getVin(), diffDay);
        // longTermProductLineDays 的绝对值
        if (diffDay < -days){
            removeRiskAlarm(riskAlarm, "自动解除风险");
        }
    }

    /**
     * date 转localDate
     * @param date
     * @return
     */
    public LocalDate formatDateToLocalDate(Date date){
        // 将Date对象转换为Instant对象
        Instant instant = date.toInstant();

        // 使用Instant对象创建ZonedDateTime对象（默认时区）
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        // 从ZonedDateTime对象获取LocalDate对象
        LocalDate localDate = zonedDateTime.toLocalDate();
        return localDate;
    }


    private RiskAlarmConfig getAlarmStartTime(String vin, Integer alarmType){
        RiskAlarmConfig riskAlarmConfig = tableRiskAlarmConfigService.selectRiskAlarmConfig(vin, alarmType);
        if (riskAlarmConfig != null){
            return riskAlarmConfig;
        }
        return null;
    }

    /**
     * 订单恢复逻辑
     */
    private void doOrder() {
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.queryDataByType(AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
        if (CollectionUtil.isEmpty(riskAlarmList)){
            return;
        }

        for (RiskAlarm riskAlarm : riskAlarmList) {
            OrderTypeEnum orderTypeEnum = OrderTypeEnum.getEnumByType(riskAlarm.getOrderType());
            switch (orderTypeEnum){
                case MD_ORDER:
                    doMdOrder(riskAlarm);
                    break;
                case PARTNER_ORDER:
                    doPartnerOrder(riskAlarm);
                    break;
                case SAIC_ORDER:
                    doSaicOrder(riskAlarm);
                    break;
                case LONG_RENT_ORDER:
                    doLongRentOrder(riskAlarm);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 分时订单恢复处理逻辑
     * @param riskAlarm
     */
    private void doSaicOrder(RiskAlarm riskAlarm) {
        AssetsVehicle assetsVehicle = getVehicleInfoByVin(riskAlarm.getVin());
        if (assetsVehicle != null){
            // 车管中心车辆自动解除风险
            if (assetsVehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode()){
                removeRiskAlarm(riskAlarm, "车辆产品线是车管中心，自动解除风险");
                return;
            }
        }
        SearchRiskOrderInfoListReq build = SearchRiskOrderInfoListReq.newBuilder()
                //.setVin(riskAlarm.getVin())
                .setContractId(riskAlarm.getOrderSeq())
                .build();
        SearchRiskOrderInfoListRes searchRiskOrderInfoListRes = mdDataProxy.searchRiskOrderInfoList(build);

        List<SearchRiskOrderInfoListRes.SearchCarRentalContractDTO> infoList = searchRiskOrderInfoListRes.getInfoList();
        if (CollectionUtil.isEmpty(infoList)){
            return;
        }
        SearchRiskOrderInfoListRes.SearchCarRentalContractDTO carRentalContract = infoList.get(0);

        try {
            // 换车
            if (!carRentalContract.getVin().equals(riskAlarm.getVin())){
                removeRiskAlarm(riskAlarm, "换车，自动解除风险");
                return;
            }
            // 还车
            if (carRentalContract.getContractStatus() != 3){
                removeRiskAlarm(riskAlarm, "还车，自动解除风险");
                return;
            }
            // 续签
            if (formatDateToLocalDate(carRentalContract.getLatestEndTime()).isAfter(LocalDate.now())){
                removeRiskAlarm(riskAlarm, "续签，自动解除风险");
                return;
            }
        }catch (Exception e){
            log.error("处理数据异常.合同号：{}, 车架号:{}", riskAlarm.getOrderSeq(), riskAlarm.getVin());
        }
    }

    /**
     * 格式化dateStr
     * @param dateStr 20240307142300
     * @return
     */
    private LocalDate formatDateToLocalDate(String dateStr){
        // 创建一个DateTimeFormatter实例，定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 使用formatter解析字符串到LocalDateTime对象
        return LocalDateTime.parse(dateStr, formatter).toLocalDate();
    }

    /**
     * 长租订单恢复逻辑
     * @param riskAlarm
     */
    private void doLongRentOrder(RiskAlarm riskAlarm) {
        try {
            AssetsVehicle assetsVehicle = getVehicleInfoByVin(riskAlarm.getVin());
            if (assetsVehicle != null){
                // 车管中心车辆自动解除风险
                if (assetsVehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode()){
                    removeRiskAlarm(riskAlarm, "车辆产品线是车管中心，自动解除风险");
                    return;
                }
            }
            List<QueryRentContractInfo> queryRentContractInfos = new ArrayList<>();
            queryRentContractInfos.add(QueryRentContractInfo
                    .newBuilder()
                    .setContractId(riskAlarm.getOrderSeq())
                    .setVin(riskAlarm.getVin())
                    .build());

            //查询长租订单
            QueryRentContractByVinAndContractRes queryRentContractByVinAndContractRes = mdDataProxy.queryRentContractByVinAndContract(QueryRentContractByVinAndContractReq.newBuilder()
                    .addAllInfo(queryRentContractInfos)
                    .build());
            List<String> contractList = queryRentContractByVinAndContractRes.getInfoList();
            if (CollectionUtil.isEmpty(contractList)){
                return;
            }
            String orderSeq = contractList.get(0);
            if (StringUtils.isBlank(orderSeq)){
                return;
            }
            if (orderSeq.equals(riskAlarm.getOrderSeq())){
                removeRiskAlarm(riskAlarm, "订单还车/续签，自动解除风险");
            }
        }catch (Exception e){
            log.error("处理数据异常.合同号：{}, 车架号:{}", riskAlarm.getOrderSeq(), riskAlarm.getVin());
        }
    }

    /**
     * 门店订单恢复逻辑（还车、续租、换车）
     * @param riskAlarm
     */
    private void doMdOrder(RiskAlarm riskAlarm) {
        try {
            AssetsVehicle assetsVehicle = getVehicleInfoByVin(riskAlarm.getVin());
            if (assetsVehicle != null){
                // 车管中心车辆自动解除风险
                if (assetsVehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode()){
                    removeRiskAlarm(riskAlarm, "车辆产品线是车管中心，自动解除风险");
                    return;
                }
            }

            GetContractDetailsReq req = GetContractDetailsReq.newBuilder().setContractId(riskAlarm.getOrderSeq()).build();
            GetContractDetailsRes contractDetails = mdOrderCenter.getContractDetails(req);

            CarRentalContractInfo carRentalContractInfo = contractDetails.getCarRentalContractInfo();
            if (carRentalContractInfo == null){
                return;
            }
            if (!carRentalContractInfo.getVin().equals(riskAlarm.getVin())){
                removeRiskAlarm(riskAlarm, "订单换车， 自动解除风险");
                return;
            }

            long contractStatus = carRentalContractInfo.getContractStatus();
            // != 3 代表已还车
            if (contractStatus != 3 && contractStatus != 4){
                removeRiskAlarm(riskAlarm, "订单还车，自动解除风险");
                return;
            }
            // 最晚还车时间大于当前时间(相当于续租操作)
            String lastReturnDateTime = carRentalContractInfo.getLastReturnDateTime();
            Date lastReturnDate = DateUtil.getDateFromStr(lastReturnDateTime, "yyyy-MM-dd HH:mm:ss");
            if (lastReturnDate.after(new Date())){
                removeRiskAlarm(riskAlarm, "订单续租，自动解除风险");
            }
        }catch (Exception e){
            log.error("处理数据异常.合同号：{}, 车架号:{}", riskAlarm.getOrderSeq(), riskAlarm.getVin());
        }
    }

    /**
     * 解除风险
     * @param riskAlarm
     * @param opereateContent
     */
    public void removeRiskAlarm(RiskAlarm riskAlarm, String opereateContent){
        log.info("车架号:{},解除风控", riskAlarm.getVin());
        int maxVehicleStatus = baseService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo());
        riskAlarm.setRecoverMethod(RecoverMethodEnum.AUTO_RECOVER.getCode());
        riskAlarm.setRecoveryDate(new Date());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
        //  自动完成车辆巡查任务
        baseService.autoCompleteSingleVehiclePatrolTask(riskAlarm);
        // 处理恢复风控逻辑
        riskAlarmService.handelRiskAlarm(riskAlarm, RiskOperateEnum.OPERATE_REMOVE.getCode(), CurrentUser.newBuilder().setNickName("定时任务").setUserAccount("System").build());
        saveOperateLog(opereateContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode());

        // 车辆日志
        saveVehicleLog(riskAlarm.getVin(), opereateContent);
        // 同步更新车辆风控状态
        subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), maxVehicleStatus);
        // 通知长租
        subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
    }

    /**
     * 渠道订单车查询
     */
    private void doPartnerOrder(RiskAlarm riskAlarm) {
        try {
            AssetsVehicle assetsVehicle = getVehicleInfoByVin(riskAlarm.getVin());
            if (assetsVehicle != null){
                // 车管中心车辆自动解除风险
                if (assetsVehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode()){
                    removeRiskAlarm(riskAlarm, "车辆产品线是车管中心，自动解除风险");
                    return;
                }
            }
            BatchQueryThirdPartnerOrderListReq req = BatchQueryThirdPartnerOrderListReq.newBuilder().addAllInnerOrderNo(Arrays.asList(riskAlarm.getOrderSeq())).build();
            BatchQueryThirdPartnerOrderListRes thirdPartnerOrderListRes = mdOrderCenter.batchQueryThirdPartnerOrderList(req);
            List<ThirdPartnerOrderInfo> partnerOrderList = thirdPartnerOrderListRes.getPartnerOrderList();
            if (CollectionUtil.isEmpty(partnerOrderList)){
                return;
            }
            ThirdPartnerOrderInfo thirdPartnerOrderInfo = partnerOrderList.get(0);
            if (thirdPartnerOrderInfo.getOrderStatus() != 3 && StringUtils.isNotBlank(thirdPartnerOrderInfo.getRealReturnDateTime())){
                removeRiskAlarm(riskAlarm, "订单还车/续租/换车，自动解除风险");
                return;
            }
            String vehicleNo = thirdPartnerOrderInfo.getVehicleNo();
            if (StringUtils.isNotBlank(vehicleNo)){
                GetVehicleInfoByVehicleNoRes vehicleInfo = mdDataProxy.getVehicleInfoByVehicleNo(GetVehicleInfoByVehicleNoReq.newBuilder().setVehicleNo(vehicleNo).build());
                if (vehicleInfo == null){
                    return;
                }
                if (!vehicleInfo.getVin().equals(riskAlarm.getVin())){
                    removeRiskAlarm(riskAlarm, "订单还车/续租/换车，自动解除风险");
                }
            }
        }catch (Exception e){
            log.error("处理错误,合同号:{}, 车架号:{}", riskAlarm.getAlarmNo(), riskAlarm.getVin());
        }
    }

    public void saveOperateLog(String operateContent, String relationKey, Integer operateType){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");

        tableOperateLogService.save(operateLog);
    }


    /**
     * 根据车架号查询车辆信息
     * @param vin
     * @return
     */
    public AssetsVehicle getVehicleInfoByVin(String vin){
        GetAssetsVehicleByVinRes res = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vin).build());
        List<AssetsVehicle> vehicles = res.getInfoList();
        if (CollectionUtil.isEmpty(vehicles)) {
            return null;
        }
        return vehicles.get(0);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vinList
     * @param operateContent
     */
    public void saveVehicleLog(List<String> vinList, String operateContent){
        if (CollectionUtils.isEmpty(vinList)){
            return;
        }
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        for (String vin : vinList) {
            SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
            logList.add(log);
        }
        // 新增日志
        insertVehicleOperateLog(logList);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vin
     * @param operateContent
     */
    public void saveVehicleLog(String vin, String operateContent){
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
        logList.add(log);
        // 新增日志
        insertVehicleOperateLog(logList);
    }

    /**
     * 新增操作
     * @param logList
     */
    public void insertVehicleOperateLog(List<SaveVehicleOperateLog> logList){
        com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser user = com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser
                .newBuilder()
                .setNickName("定时任务")
                .setUserNo("")
                .setUserAccount("System").build();
        // 保存车辆预风控记录
        SaveVehicleOperateLogRes res = vlmsAssetsService.saveVehicleOperateLog(SaveVehicleOperateLogReq.newBuilder().addAllLogs(logList).setBusinessType(3).setCurrentUser(user).build());
        if (res.getRetCode() != 0) {
            throw new ServiceException( "保存车辆风控操作日志失败");
        }
    }


    /**
     * 获取车辆阶段
     * propertyStatus: 资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
     * putInStatus 投运状态 1:未投运 2:投运成功 3:投运审批中
     * @param assetsVehicle
     * @return
     */
    public Integer getVehicleStep(AssetsVehicle assetsVehicle){
        int propertyStatus = assetsVehicle.getPropertyStatus();
        int putInStatus = assetsVehicle.getPutInStatus();
        switch (propertyStatus) {
            case 0:
                return VehiclePhaseEnum.IN.getCode();
            case 1:
            case 6:
                switch (putInStatus) {
                    case 2:
                        return VehiclePhaseEnum.OPERATING.getCode();
                    case 1:
                    case 3:
                        return VehiclePhaseEnum.RETURN.getCode();
                }
            case 2:
            case 3:
            case 4:
            case 5:
            case 8:
                return VehiclePhaseEnum.DISPOSAL.getCode();
            default:
                return 0;
        }
    }
}
