package com.saicmobility.evcard.vlms.risk.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * @className ExportPreDisposeTask
 * @description 导出错误消息
 * @create 2023/08/28 16:22
 **/
@Data
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ExportErrorInfo {

    /**
     * 车架号
     */
    @ExcelProperty(value = "车架号")
    @ColumnWidth(20)
    private String vin;

    /**
     * 错误消息
     */
    @ExcelProperty(value = "错误消息")
    @ColumnWidth(20)
    private String error;
}
