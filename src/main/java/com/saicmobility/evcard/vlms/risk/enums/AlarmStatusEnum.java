package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 报警状态 1、报警中 2、已恢复
 * <AUTHOR>
 * @date 2024-01-16
 */
@AllArgsConstructor
@Getter
public enum AlarmStatusEnum {

    ALARM_STATUS(1, "报警中"),
    ALARM_RECOVER(2, "已恢复");

    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (AlarmStatusEnum item : AlarmStatusEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
