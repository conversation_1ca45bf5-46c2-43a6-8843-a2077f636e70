package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskWhitelistConfigDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    public static final RiskWhitelistConfig riskWhitelistConfig = new RiskWhitelistConfig();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.id")
    public static final SqlColumn<Long> id = riskWhitelistConfig.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.vin")
    public static final SqlColumn<String> vin = riskWhitelistConfig.vin;

    /**
     * Database Column Remarks:
     *   生效状态 1-进行中 2-已过期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.effective_status")
    public static final SqlColumn<Integer> effectiveStatus = riskWhitelistConfig.effectiveStatus;

    /**
     * Database Column Remarks:
     *   报警类型（1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.alarm_type")
    public static final SqlColumn<Integer> alarmType = riskWhitelistConfig.alarmType;

    /**
     * Database Column Remarks:
     *   有效期截止日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.expiration_date")
    public static final SqlColumn<Date> expirationDate = riskWhitelistConfig.expirationDate;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.is_deleted")
    public static final SqlColumn<Integer> isDeleted = riskWhitelistConfig.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.misc_desc")
    public static final SqlColumn<String> miscDesc = riskWhitelistConfig.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_time")
    public static final SqlColumn<Date> createTime = riskWhitelistConfig.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_account")
    public static final SqlColumn<String> createOperAccount = riskWhitelistConfig.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_name")
    public static final SqlColumn<String> createOperName = riskWhitelistConfig.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_time")
    public static final SqlColumn<Date> updateTime = riskWhitelistConfig.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = riskWhitelistConfig.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_name")
    public static final SqlColumn<String> updateOperName = riskWhitelistConfig.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    public static final class RiskWhitelistConfig extends AliasableSqlTable<RiskWhitelistConfig> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Integer> effectiveStatus = column("effective_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> alarmType = column("alarm_type", JDBCType.INTEGER);

        public final SqlColumn<Date> expirationDate = column("expiration_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public RiskWhitelistConfig() {
            super("t_risk_whitelist_config", RiskWhitelistConfig::new);
        }
    }
}