package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.database.TablePauseAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.model.PauseAlarmConfig;
import com.saicmobility.evcard.vlms.risk.service.PauseAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import jodd.util.StringUtil;
import krpc.rpc.util.BeanToMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class PauseAlarmConfigServiceImpl extends BaseService implements PauseAlarmConfigService {

    @Resource
    private TablePauseAlarmConfigService tablePauseAlarmConfigService;

    @Override
    public QueryPauseAlarmDurationConfigRes queryPauseAlarmDurationConfig(QueryPauseAlarmDurationConfigReq req) {
        QueryPauseAlarmDurationConfigRes.Builder builder = QueryPauseAlarmDurationConfigRes.newBuilder();
        List<PauseAlarmConfig> pauseAlarmConfigs = tablePauseAlarmConfigService.selectList(req);
        for (PauseAlarmConfig pauseAlarmConfig : pauseAlarmConfigs) {
            builder.addList(PbConvertUtil.generateProtoBuffer(pauseAlarmConfig, PauseAlarmDurationConfig.class));
        }
        return builder.build();
    }

    @Override
    public UpdatePauseAlarmDurationConfigRes updatePauseAlarmDurationConfig(UpdatePauseAlarmDurationConfigReq req) {
        PauseAlarmConfig pauseAlarmConfig = tablePauseAlarmConfigService.selectById(req.getId());
        if (pauseAlarmConfig == null){
            throw new ServiceException("暂停报警时长配置不存在");
        }
        // 更新
        pauseAlarmConfig.setMaxPauseDay(req.getMaxPauseDay());
        pauseAlarmConfig.setUpdateTime(new Date());
        tablePauseAlarmConfigService.updateById(pauseAlarmConfig);

        // 日志
        String remark = StrUtil.format("修改暂停报警时长配置,最长暂停时长【{}】天", req.getMaxPauseDay());
        if (StringUtil.isNotBlank(req.getMiscDesc())){
            remark += "，备注："+req.getMiscDesc();
        }
        saveOperateLog(remark, req.getId()+"", OperateTypeEnum.OPERATE_PAUSE_CONFIG.getCode(), req.getCurrentUser());
        return UpdatePauseAlarmDurationConfigRes.ok();
    }
}
