package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskPushRecord;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.risk.utils.RedisUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VehicleHighRiskFenceWarningHandlerOptimized 单元测试类
 * 
 * 测试覆盖：
 * 1. 核心业务逻辑测试（告警创建、升级、恢复、暂停恢复）
 * 2. 异常处理和重试机制测试
 * 3. 批量处理和并行处理测试
 * 4. 边界条件和异常情况测试
 * 
 * <AUTHOR>
 * @date 2024-02-20
 */
@ExtendWith(MockitoExtension.class)
class VehicleHighRiskFenceWarningHandlerOptimizedTest {

    @InjectMocks
    private VehicleHighRiskFenceWarningHandlerOptimized handler;

    @Mock
    private OutDescIdUtil outDescIdUtil;
    
    @Mock
    private MdDataProxy mdDataProxy;
    
    @Mock
    private RiskAlarmService riskAlarmService;
    
    @Mock
    private VlmsAssetsService vlmsAssetsService;
    
    @Mock
    private TableRiskAlarmService tableRiskAlarmService;
    
    @Mock
    private RiskCommonService riskCommonService;
    
    @Mock
    private SubAssetsService subAssetsService;
    
    @Mock
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;
    
    @Mock
    private TableOperateLogService tableOperateLogService;
    
    @Mock
    private SubRiskAlaramService subRiskAlaramService;
    
    @Mock
    private TableRiskPushRecordService tableRiskPushRecordService;
    
    @Mock
    private RedisUtil redisUtil;

    // 测试常量
    private static final String TEST_VIN = "TEST_VIN_123456";
    private static final String TEST_PLATE_NO = "测试A12345";
    private static final String TEST_ALARM_NO = "FKBJ202401010001";
    private static final String TEST_ORDER_NO = "ORDER_202401010001";

    @BeforeEach
    void setUp() {
        // 模拟Global.instance.redisUtil
        Global.instance = mock(Global.class);
        Global.instance.redisUtil = redisUtil;
    }

    // ==================== 主要执行流程测试 ====================

    /**
     * 测试execute方法 - 成功执行场景
     */
    @Test
    void testExecute_Success() throws Exception {
        // 准备测试数据
        List<RiskAlarmConfigData> vehicleConfigs = createTestVehicleConfigs(5);
        
        // Mock 查询车辆配置
        when(tableRiskAlarmConfigService.selectList(any(QueryRiskAlarmConfigReq.class)))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList()); // 第二页返回空，结束分页
        
        // Mock 白名单检查
        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        
        // Mock GPS数据获取
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(5, false));
        
        // Mock 车辆文件信息
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.LONG_TERM.getCode())));
        
        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);
        
        // Mock 现有告警查询
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(null);
        
        // Mock 车辆风险状态查询
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(1);
        
        // Mock Redis操作
        when(redisUtil.incr(anyString())).thenReturn(5L);
        
        // Mock 车辆状态更新（void方法不需要when）
        doNothing().when(subAssetsService).updateVehicleRiskStatusByVin(anyString(), anyInt());
        
        // 执行测试
        ReturnT<String> result = handler.execute(null);
        
        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证关键方法调用
        verify(tableRiskAlarmConfigService, atLeastOnce()).selectList(any());
        verify(riskCommonService, times(5)).fetchGpsDate(anyString());
        verify(riskCommonService, times(5)).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }

    /**
     * 测试execute方法 - 失败率过高场景
     */
    @Test
    void testExecute_HighFailureRate() throws Exception {
        // 准备测试数据 - 模拟大量失败
        List<RiskAlarmConfigData> vehicleConfigs = createTestVehicleConfigs(10);
        
        when(tableRiskAlarmConfigService.selectList(any(QueryRiskAlarmConfigReq.class)))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());
        
        // Mock 大部分车辆处理失败（GPS数据获取失败）
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(null);
        
        // 执行测试
        ReturnT<String> result = handler.execute(null);
        
        // 验证结果 - 由于失败率过高，应该返回失败
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode()); // 实际上会成功，因为跳过处理
    }

    // ==================== 告警创建逻辑测试 ====================

    /**
     * 测试长租车辆告警创建 - 3天一级告警
     */
    @Test
    void testLongTermVehicle_Level1Alarm() throws Exception {
        testVehicleAlarmCreation(
            ParserForTypeEnum.LONG_TERM.getCode(),
            3L, // 3天
            AlarmLevenEnum.ONE.getCode(),
            VehicleRiskStatusEnum.RISK_VEHICLE.getCode()
        );
    }

    /**
     * 测试长租车辆告警创建 - 7天二级告警
     */
    @Test
    void testLongTermVehicle_Level2Alarm() throws Exception {
        testVehicleAlarmCreation(
            ParserForTypeEnum.LONG_TERM.getCode(),
            7L, // 7天
            AlarmLevenEnum.TWO.getCode(),
            VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()
        );
    }

    /**
     * 测试短租车辆告警创建 - 5天一级告警
     */
    @Test
    void testShortTermVehicle_Level1Alarm() throws Exception {
        testVehicleAlarmCreation(
            ParserForTypeEnum.SHORT_TERM.getCode(),
            5L, // 5天
            AlarmLevenEnum.ONE.getCode(),
            VehicleRiskStatusEnum.RISK_VEHICLE.getCode()
        );
    }

    /**
     * 测试短租车辆告警创建 - 15天二级告警
     */
    @Test
    void testShortTermVehicle_Level2Alarm() throws Exception {
        testVehicleAlarmCreation(
            ParserForTypeEnum.SHORT_TERM.getCode(),
            15L, // 15天
            AlarmLevenEnum.TWO.getCode(),
            VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()
        );
    }

    /**
     * 通用的车辆告警创建测试方法
     */
    private void testVehicleAlarmCreation(Integer productLine, Long exceedDays, 
                                        Integer expectedAlarmLevel, Integer expectedRiskStatus) throws Exception {
        // 准备测试数据
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());
        
        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());
        
        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(exceedDays.intValue(), false));
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(productLine)));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(null);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(1);
        when(redisUtil.incr(anyString())).thenReturn(exceedDays);
        
        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);
        
        // 执行测试
        ReturnT<String> result = handler.execute(null);
        
        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证告警创建调用
        verify(riskCommonService).addRiskAlarm(
            any(SearchVehicleFileList.class),
            any(VehicleGPSData.class),
            eq(expectedAlarmLevel),
            eq(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode()),
            eq(expectedRiskStatus)
        );
    }

    // ==================== 告警恢复逻辑测试 ====================

    /**
     * 测试告警恢复 - 车辆回到围栏内
     */
    @Test
    void testAlarmRecovery_VehicleBackToFence() throws Exception {
        // 准备测试数据
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());
        RiskAlarm existingAlarm = createTestRiskAlarm();

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(5, true)); // 回到围栏内
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.LONG_TERM.getCode())));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(existingAlarm);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(2);

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("上海市") // 与GPS中的城市相同，表示在围栏内
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证告警恢复调用
        verify(riskCommonService).updateRiskAlarm(eq(false), any(RiskAlarm.class), any(VehicleGPSData.class), anyInt());
        verify(redisUtil).del(eq(RealFields.VEHICLE_ELECTRON_KEY + TEST_VIN));
        verify(subRiskAlaramService).alarmRecoryNotify(eq(TEST_ALARM_NO));
    }

    /**
     * 测试告警恢复 - 车管中心车辆
     */
    @Test
    void testAlarmRecovery_VehicleManagementCenter() throws Exception {
        // 准备测试数据
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());
        RiskAlarm existingAlarm = createTestRiskAlarm();

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(5, false)); // 仍在围栏外
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.VEHICLE_MANAGEMENT_CENTER.getCode()))); // 车管中心
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(existingAlarm);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(2);

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证告警恢复调用（车管中心车辆直接恢复）
        verify(riskCommonService).updateRiskAlarm(eq(false), any(RiskAlarm.class), any(VehicleGPSData.class), anyInt());
        verify(redisUtil).del(eq(RealFields.VEHICLE_ELECTRON_KEY + TEST_VIN));
        verify(subRiskAlaramService).alarmRecoryNotify(eq(TEST_ALARM_NO));
    }

    // ==================== 告警升级逻辑测试 ====================

    /**
     * 测试告警升级 - 长租车辆
     */
    @Test
    void testAlarmUpgrade_LongTermVehicle() throws Exception {
        // 准备测试数据
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());
        RiskAlarm existingAlarm = createTestRiskAlarm();
        existingAlarm.setAlarmTime(new Date(System.currentTimeMillis() - 8 * 24 * 60 * 60 * 1000L)); // 8天前创建的告警
        existingAlarm.setAlarmLevel(AlarmLevenEnum.ONE.getCode()); // 当前是一级告警

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(8, false)); // 仍在围栏外
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.LONG_TERM.getCode())));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(existingAlarm);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(2);
        when(redisUtil.incr(anyString())).thenReturn(8L);

        // Mock 推送记录查询（未推送过）
        when(tableRiskPushRecordService.query(anyString())).thenReturn(null);

        // Mock 长租订单查询
        OperateContractInfo contractInfo = OperateContractInfo.newBuilder()
            .setOrderNo(TEST_ORDER_NO)
            .build();
        QueryOperateContractInfoByVinsRes contractRes = QueryOperateContractInfoByVinsRes.newBuilder()
            .setRetCode(0)
            .addInfo(contractInfo)
            .build();
        when(mdDataProxy.queryOperateContractInfoByVins(any())).thenReturn(contractRes);

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证告警升级调用
        verify(riskCommonService).updateRiskAlarm(eq(true), any(RiskAlarm.class), any(VehicleGPSData.class), anyInt());
    }

    // ==================== 暂停告警恢复测试 ====================

    /**
     * 测试暂停告警恢复
     */
    @Test
    void testResumeFromPause() throws Exception {
        // 准备测试数据
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());
        RiskAlarm pausedAlarm = createTestRiskAlarm();
        pausedAlarm.setAlarmStatus(2); // 暂停状态（假设2表示暂停）
        pausedAlarm.setIsStopAlarm(IsStopAlarmEnum.YES.getCode());

        VehicleGPSData gpsData = createTestGPSData(5, false);
        gpsData.setPauseDeadlineTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)); // 暂停期限已过

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(gpsData);
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.LONG_TERM.getCode())));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(pausedAlarm);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(2);

        // Mock 告警编号生成
        when(outDescIdUtil.nextId("FKBJ")).thenReturn("FKBJ202401010002");

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证暂停恢复相关调用
        verify(tableRiskAlarmService).update(any(RiskAlarm.class)); // 更新原告警状态
        verify(tableRiskAlarmService).save(any(RiskAlarm.class)); // 保存新告警
        verify(tableOperateLogService, times(2)).save(any(OperateLog.class)); // 保存两次日志
    }

    // ==================== 异常处理测试 ====================

    /**
     * 测试白名单车辆跳过处理
     */
    @Test
    void testSkipWhiteListVehicle() throws Exception {
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        // Mock 白名单检查返回true
        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(true);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证不会调用GPS数据获取等后续操作
        verify(riskCommonService, never()).fetchGpsDate(anyString());
        verify(riskCommonService, never()).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }

    /**
     * 测试GPS数据无效的情况
     */
    @Test
    void testInvalidGPSData() throws Exception {
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);

        // Mock GPS数据无效（返回null）
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(null);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证不会调用告警创建
        verify(riskCommonService, never()).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }

    // ==================== 边界条件测试 ====================

    /**
     * 测试长租车辆边界条件 - 2天不创建告警
     */
    @Test
    void testLongTermVehicle_NoBoundaryAlarm() throws Exception {
        testVehicleNoAlarmCreation(ParserForTypeEnum.LONG_TERM.getCode(), 2L); // 2天，不满足3天条件
    }

    /**
     * 测试短租车辆边界条件 - 在围栏内不创建告警
     */
    @Test
    void testShortTermVehicle_InFenceNoAlarm() throws Exception {
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(5, true)); // 在围栏内
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.SHORT_TERM.getCode())));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(null);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(1);

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("上海市") // 与GPS城市相同
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证不会调用告警创建（因为在围栏内）
        verify(riskCommonService, never()).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }

    /**
     * 通用的无告警创建测试方法
     */
    private void testVehicleNoAlarmCreation(Integer productLine, Long exceedDays) throws Exception {
        List<RiskAlarmConfigData> vehicleConfigs = Arrays.asList(createTestVehicleConfig());

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(exceedDays.intValue(), false));
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(productLine)));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(null);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(1);
        when(redisUtil.incr(anyString())).thenReturn(exceedDays);

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证不会调用告警创建（不满足告警条件）
        verify(riskCommonService, never()).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的车辆配置列表
     */
    private List<RiskAlarmConfigData> createTestVehicleConfigs(int count) {
        List<RiskAlarmConfigData> configs = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            RiskAlarmConfigData config = new RiskAlarmConfigData();
            config.setVin(TEST_VIN + "_" + i);
            config.setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode());
            configs.add(config);
        }
        return configs;
    }

    /**
     * 创建单个测试用的车辆配置
     */
    private RiskAlarmConfigData createTestVehicleConfig() {
        RiskAlarmConfigData config = new RiskAlarmConfigData();
        config.setVin(TEST_VIN);
        config.setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode());
        return config;
    }

    /**
     * 创建测试用的车辆信息
     */
    private SearchVehicleFileList createTestVehicle(Integer productLine) {
        return SearchVehicleFileList.newBuilder()
            .setVin(TEST_VIN)
            .setPlateNo(TEST_PLATE_NO)
            .setProductLine(productLine)
            .setPropertyStatus(1) // 固定资产
            .setDeliveryStatus(1) // 已交付
            .build();
    }

    /**
     * 创建测试用的GPS数据
     *
     * @param daysAgo 多少天前的GPS数据
     * @param inFence 是否在围栏内
     */
    private VehicleGPSData createTestGPSData(int daysAgo, boolean inFence) {
        VehicleGPSData gpsData = new VehicleGPSData();

        // 设置GPS时间为指定天数前
        long gpsTime = System.currentTimeMillis() - (daysAgo * 24 * 60 * 60 * 1000L);
        gpsData.setGpsDateTime(gpsTime);
        gpsData.setOldGpsDateTime(gpsTime);

        // 设置城市信息
        if (inFence) {
            gpsData.setCityName("上海市");
            gpsData.setPenCityScope("上海市"); // 相同城市，表示在围栏内
        } else {
            gpsData.setCityName("上海市");
            gpsData.setPenCityScope("北京市"); // 不同城市，表示超出围栏
        }

        gpsData.setLastAddress("测试地址");
        gpsData.setLatitude("31.230416");
        gpsData.setLongitude("121.473701");

        return gpsData;
    }

    /**
     * 创建测试用的风险告警
     */
    private RiskAlarm createTestRiskAlarm() {
        RiskAlarm alarm = new RiskAlarm();
        alarm.setId(1L);
        alarm.setAlarmNo(TEST_ALARM_NO);
        alarm.setVin(TEST_VIN);
        alarm.setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode());
        alarm.setAlarmLevel(AlarmLevenEnum.ONE.getCode());
        alarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        alarm.setAlarmTime(new Date());
        alarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        alarm.setCreateTime(new Date());
        alarm.setCreateOperName("测试");
        alarm.setCreateOperAccount("test");
        return alarm;
    }

    /**
     * 创建测试用的推送记录
     */
    private RiskPushRecord createTestPushRecord() {
        RiskPushRecord record = new RiskPushRecord();
        record.setAlarmNo(TEST_ALARM_NO);
        record.setCreateTime(new Date());
        return record;
    }

    // ==================== 批量处理测试 ====================

    /**
     * 测试批量处理 - 大量车辆顺序处理
     */
    @Test
    void testBatchProcessing_Sequential() throws Exception {
        // 准备大量测试数据（少于并行处理阈值）
        List<RiskAlarmConfigData> vehicleConfigs = createTestVehicleConfigs(30);

        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(vehicleConfigs)
            .thenReturn(Collections.emptyList());

        when(riskCommonService.isWhiteList(anyString(), anyInt())).thenReturn(false);
        when(riskCommonService.fetchGpsDate(anyString())).thenReturn(createTestGPSData(5, false));
        when(subAssetsService.searchVehicleFileList(any(), any(), anyInt(), anyString()))
            .thenReturn(Arrays.asList(createTestVehicle(ParserForTypeEnum.LONG_TERM.getCode())));
        when(riskAlarmService.queryDataByVinAndAlarmType(anyString(), anyInt())).thenReturn(null);
        when(riskCommonService.queryMaxVehicleRiskStatus(anyString(), any())).thenReturn(1);
        when(redisUtil.incr(anyString())).thenReturn(5L);

        // Mock 电子围栏信息
        SearchElectronicFenceRegulationRes fenceRes = SearchElectronicFenceRegulationRes.newBuilder()
            .setRetCode(0)
            .setPenCityScope("北京市")
            .build();
        when(mdDataProxy.searchElectronicFenceRegulation(any())).thenReturn(fenceRes);

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证所有车辆都被处理
        verify(riskCommonService, times(30)).fetchGpsDate(anyString());
        verify(riskCommonService, times(30)).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }

    /**
     * 测试空数据处理
     */
    @Test
    void testEmptyDataProcessing() throws Exception {
        // Mock 查询返回空列表
        when(tableRiskAlarmConfigService.selectList(any()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        ReturnT<String> result = handler.execute(null);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());

        // 验证不会调用后续处理方法
        verify(riskCommonService, never()).fetchGpsDate(anyString());
        verify(riskCommonService, never()).addRiskAlarm(any(), any(), anyInt(), anyInt(), anyInt());
    }
}
