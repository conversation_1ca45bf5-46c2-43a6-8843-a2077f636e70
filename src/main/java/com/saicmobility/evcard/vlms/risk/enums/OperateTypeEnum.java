package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1-风控报警 2-风控收车 3-风控报警配置 4-暂停时长配置 5-白名单配置
 * Created by ch<PERSON><PERSON><PERSON> on 2024/01/24.
 */
@AllArgsConstructor
@Getter
public enum OperateTypeEnum {

    OPERATE_TYPE(1, "风控报警"),
    OPERATE_RECEIVE_VEHICLE(2, "风控收车"),
    OPERATE_CONFIG(3, "风控报警配置"),
    OPERATE_PAUSE_CONFIG(4, "暂停时长配置"),
    OPERATE_WHITE_LIST_CONFIG(5, "白名单配置"),
    ;
    final Integer code;
    final String value;
    public static String getValueByCode(Integer code) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getCode() == code) {
                return operateTypeEnum.getValue();
            }
        }
        return null;
    }
}
