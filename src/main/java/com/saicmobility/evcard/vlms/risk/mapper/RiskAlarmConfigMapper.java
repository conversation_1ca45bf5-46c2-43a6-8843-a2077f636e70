package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmConfigDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskAlarmConfigMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, alarmType, pauseDay, alarmStartTime, pauseDeadlineTime, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskAlarmConfig> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskAlarmConfigResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="alarm_type", property="alarmType", jdbcType=JdbcType.INTEGER),
        @Result(column="pause_day", property="pauseDay", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_start_time", property="alarmStartTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="pause_deadline_time", property="pauseDeadlineTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskAlarmConfig> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskAlarmConfigResult")
    Optional<RiskAlarmConfig> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskAlarmConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskAlarmConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int insert(RiskAlarmConfig row) {
        return MyBatis3Utils.insert(this::insert, row, riskAlarmConfig, c ->
            c.map(vin).toProperty("vin")
            .map(alarmType).toProperty("alarmType")
            .map(pauseDay).toProperty("pauseDay")
            .map(alarmStartTime).toProperty("alarmStartTime")
            .map(pauseDeadlineTime).toProperty("pauseDeadlineTime")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int insertSelective(RiskAlarmConfig row) {
        return MyBatis3Utils.insert(this::insert, row, riskAlarmConfig, c ->
            c.map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(alarmType).toPropertyWhenPresent("alarmType", row::getAlarmType)
            .map(pauseDay).toPropertyWhenPresent("pauseDay", row::getPauseDay)
            .map(alarmStartTime).toPropertyWhenPresent("alarmStartTime", row::getAlarmStartTime)
            .map(pauseDeadlineTime).toPropertyWhenPresent("pauseDeadlineTime", row::getPauseDeadlineTime)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default Optional<RiskAlarmConfig> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskAlarmConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default List<RiskAlarmConfig> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskAlarmConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default List<RiskAlarmConfig> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskAlarmConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default Optional<RiskAlarmConfig> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskAlarmConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskAlarmConfig row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(row::getVin)
                .set(alarmType).equalTo(row::getAlarmType)
                .set(pauseDay).equalTo(row::getPauseDay)
                .set(alarmStartTime).equalTo(row::getAlarmStartTime)
                .set(pauseDeadlineTime).equalTo(row::getPauseDeadlineTime)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskAlarmConfig row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(row::getVin)
                .set(alarmType).equalToWhenPresent(row::getAlarmType)
                .set(pauseDay).equalToWhenPresent(row::getPauseDay)
                .set(alarmStartTime).equalToWhenPresent(row::getAlarmStartTime)
                .set(pauseDeadlineTime).equalToWhenPresent(row::getPauseDeadlineTime)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int updateByPrimaryKey(RiskAlarmConfig row) {
        return update(c ->
            c.set(vin).equalTo(row::getVin)
            .set(alarmType).equalTo(row::getAlarmType)
            .set(pauseDay).equalTo(row::getPauseDay)
            .set(alarmStartTime).equalTo(row::getAlarmStartTime)
            .set(pauseDeadlineTime).equalTo(row::getPauseDeadlineTime)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm_config")
    default int updateByPrimaryKeySelective(RiskAlarmConfig row) {
        return update(c ->
            c.set(vin).equalToWhenPresent(row::getVin)
            .set(alarmType).equalToWhenPresent(row::getAlarmType)
            .set(pauseDay).equalToWhenPresent(row::getPauseDay)
            .set(alarmStartTime).equalToWhenPresent(row::getAlarmStartTime)
            .set(pauseDeadlineTime).equalToWhenPresent(row::getPauseDeadlineTime)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}