package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckCompleteApprove;

public interface RiskCheckCompleteApproveService {

    void insertSelective(RiskCheckCompleteApprove newApprove);

    /**
     * 根据applicationId查询
     * @param applicationId
     * @return
     */
    RiskCheckCompleteApprove selectByApplicationId(String applicationId);

    /**
     * 更新审批状态
     * @param applicationId
     * @param status
     */
    void updateApproveStatus(String applicationId, int status);

    /**
     * 根据checkNo查询最新一条记录
     * @param checkNo
     * @return
     */
    RiskCheckCompleteApprove selectLatestRequestNoByCheckNo(String checkNo);
}
