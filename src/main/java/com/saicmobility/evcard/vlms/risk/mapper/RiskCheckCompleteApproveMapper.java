package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckCompleteApprove;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskCheckCompleteApproveMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    BasicColumn[] selectList = BasicColumn.columnList(id, applicationId, approveStatus, requestNo, status, remark, userName, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskCheckCompleteApprove> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskCheckCompleteApproveResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="application_id", property="applicationId", jdbcType=JdbcType.VARCHAR),
        @Result(column="approve_status", property="approveStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="request_no", property="requestNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.DECIMAL),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskCheckCompleteApprove> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskCheckCompleteApproveResult")
    Optional<RiskCheckCompleteApprove> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskCheckCompleteApprove, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskCheckCompleteApprove, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int insert(RiskCheckCompleteApprove row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckCompleteApprove, c ->
            c.map(applicationId).toProperty("applicationId")
            .map(approveStatus).toProperty("approveStatus")
            .map(requestNo).toProperty("requestNo")
            .map(status).toProperty("status")
            .map(remark).toProperty("remark")
            .map(userName).toProperty("userName")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int insertSelective(RiskCheckCompleteApprove row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckCompleteApprove, c ->
            c.map(applicationId).toPropertyWhenPresent("applicationId", row::getApplicationId)
            .map(approveStatus).toPropertyWhenPresent("approveStatus", row::getApproveStatus)
            .map(requestNo).toPropertyWhenPresent("requestNo", row::getRequestNo)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(userName).toPropertyWhenPresent("userName", row::getUserName)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default Optional<RiskCheckCompleteApprove> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskCheckCompleteApprove, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default List<RiskCheckCompleteApprove> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskCheckCompleteApprove, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default List<RiskCheckCompleteApprove> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskCheckCompleteApprove, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default Optional<RiskCheckCompleteApprove> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskCheckCompleteApprove, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskCheckCompleteApprove row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applicationId).equalTo(row::getApplicationId)
                .set(approveStatus).equalTo(row::getApproveStatus)
                .set(requestNo).equalTo(row::getRequestNo)
                .set(status).equalTo(row::getStatus)
                .set(remark).equalTo(row::getRemark)
                .set(userName).equalTo(row::getUserName)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskCheckCompleteApprove row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applicationId).equalToWhenPresent(row::getApplicationId)
                .set(approveStatus).equalToWhenPresent(row::getApproveStatus)
                .set(requestNo).equalToWhenPresent(row::getRequestNo)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(userName).equalToWhenPresent(row::getUserName)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int updateByPrimaryKey(RiskCheckCompleteApprove row) {
        return update(c ->
            c.set(applicationId).equalTo(row::getApplicationId)
            .set(approveStatus).equalTo(row::getApproveStatus)
            .set(requestNo).equalTo(row::getRequestNo)
            .set(status).equalTo(row::getStatus)
            .set(remark).equalTo(row::getRemark)
            .set(userName).equalTo(row::getUserName)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    default int updateByPrimaryKeySelective(RiskCheckCompleteApprove row) {
        return update(c ->
            c.set(applicationId).equalToWhenPresent(row::getApplicationId)
            .set(approveStatus).equalToWhenPresent(row::getApproveStatus)
            .set(requestNo).equalToWhenPresent(row::getRequestNo)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(userName).equalToWhenPresent(row::getUserName)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}