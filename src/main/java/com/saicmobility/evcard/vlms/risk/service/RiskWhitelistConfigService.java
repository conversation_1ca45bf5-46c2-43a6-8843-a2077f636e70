package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface RiskWhitelistConfigService {

    /**
     * 查询白名单列表
     * @param queryWhiteListReq
     * @return
     */
    QueryWhiteListRes queryWhiteList(QueryWhiteListReq queryWhiteListReq);

    /**
     * 导入车辆
     * @param importVehicleReq
     * @return
     */
    ImportVehicleRes importVehicles(ImportVehicleReq importVehicleReq);

    /**
     * 更新白名单配置
     * @param updateWhiteListConfigReq
     * @return
     */
    UpdateWhiteListConfigRes updateWhiteListConfig(UpdateWhiteListConfigReq updateWhiteListConfigReq);

    /**
     * 导出白名单列表
     * @param queryWhiteListReq
     * @return
     */
    QueryWhiteListRes exportWhiteList(QueryWhiteListReq queryWhiteListReq);

    /**
     * 关闭白名单配置
     * @param closeWhiteListConfigReq
     * @return
     */
    CloseWhiteListConfigRes closeWhiteListConfig(CloseWhiteListConfigReq closeWhiteListConfigReq);
}
