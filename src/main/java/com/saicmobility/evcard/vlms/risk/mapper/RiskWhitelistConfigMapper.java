package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskWhitelistConfigDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskWhitelistConfigMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, effectiveStatus, alarmType, expirationDate, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskWhitelistConfig> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskWhitelistConfigResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="effective_status", property="effectiveStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_type", property="alarmType", jdbcType=JdbcType.INTEGER),
        @Result(column="expiration_date", property="expirationDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskWhitelistConfig> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskWhitelistConfigResult")
    Optional<RiskWhitelistConfig> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskWhitelistConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskWhitelistConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int insert(RiskWhitelistConfig row) {
        return MyBatis3Utils.insert(this::insert, row, riskWhitelistConfig, c ->
            c.map(vin).toProperty("vin")
            .map(effectiveStatus).toProperty("effectiveStatus")
            .map(alarmType).toProperty("alarmType")
            .map(expirationDate).toProperty("expirationDate")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int insertSelective(RiskWhitelistConfig row) {
        return MyBatis3Utils.insert(this::insert, row, riskWhitelistConfig, c ->
            c.map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(effectiveStatus).toPropertyWhenPresent("effectiveStatus", row::getEffectiveStatus)
            .map(alarmType).toPropertyWhenPresent("alarmType", row::getAlarmType)
            .map(expirationDate).toPropertyWhenPresent("expirationDate", row::getExpirationDate)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default Optional<RiskWhitelistConfig> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskWhitelistConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default List<RiskWhitelistConfig> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskWhitelistConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default List<RiskWhitelistConfig> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskWhitelistConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default Optional<RiskWhitelistConfig> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskWhitelistConfig, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskWhitelistConfig row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(row::getVin)
                .set(effectiveStatus).equalTo(row::getEffectiveStatus)
                .set(alarmType).equalTo(row::getAlarmType)
                .set(expirationDate).equalTo(row::getExpirationDate)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskWhitelistConfig row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(row::getVin)
                .set(effectiveStatus).equalToWhenPresent(row::getEffectiveStatus)
                .set(alarmType).equalToWhenPresent(row::getAlarmType)
                .set(expirationDate).equalToWhenPresent(row::getExpirationDate)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int updateByPrimaryKey(RiskWhitelistConfig row) {
        return update(c ->
            c.set(vin).equalTo(row::getVin)
            .set(effectiveStatus).equalTo(row::getEffectiveStatus)
            .set(alarmType).equalTo(row::getAlarmType)
            .set(expirationDate).equalTo(row::getExpirationDate)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    default int updateByPrimaryKeySelective(RiskWhitelistConfig row) {
        return update(c ->
            c.set(vin).equalToWhenPresent(row::getVin)
            .set(effectiveStatus).equalToWhenPresent(row::getEffectiveStatus)
            .set(alarmType).equalToWhenPresent(row::getAlarmType)
            .set(expirationDate).equalToWhenPresent(row::getExpirationDate)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}