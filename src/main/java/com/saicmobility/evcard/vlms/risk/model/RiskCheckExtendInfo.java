package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   风控收车补充表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_check_extend_info
 */
public class RiskCheckExtendInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   风控收车id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.risk_check_id")
    private Long riskCheckId;

    /**
     * Database Column Remarks:
     *   收车时间：年月日
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_datetime")
    private String collectCarDatetime;

    /**
     * Database Column Remarks:
     *   收车地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_place")
    private String collectCarPlace;

    /**
     * Database Column Remarks:
     *   收车人/供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_people")
    private String collectCarPeople;

    /**
     * Database Column Remarks:
     *   收车方式：1:内部 2:委外
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_type")
    private Integer collectCarType;

    /**
     * Database Column Remarks:
     *   风控类型: 1:风控车辆 2:失控车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_risk_type")
    private Integer collectCarRiskType;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_account")
    private String updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_name")
    private String updateOperName;

    /**
     * Database Column Remarks:
     *   省
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.province")
    private String province;

    /**
     * Database Column Remarks:
     *   市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.city")
    private String city;

    /**
     * Database Column Remarks:
     *   地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.address")
    private String address;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.risk_check_id")
    public Long getRiskCheckId() {
        return riskCheckId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.risk_check_id")
    public void setRiskCheckId(Long riskCheckId) {
        this.riskCheckId = riskCheckId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_datetime")
    public String getCollectCarDatetime() {
        return collectCarDatetime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_datetime")
    public void setCollectCarDatetime(String collectCarDatetime) {
        this.collectCarDatetime = collectCarDatetime == null ? null : collectCarDatetime.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_place")
    public String getCollectCarPlace() {
        return collectCarPlace;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_place")
    public void setCollectCarPlace(String collectCarPlace) {
        this.collectCarPlace = collectCarPlace == null ? null : collectCarPlace.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_people")
    public String getCollectCarPeople() {
        return collectCarPeople;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_people")
    public void setCollectCarPeople(String collectCarPeople) {
        this.collectCarPeople = collectCarPeople == null ? null : collectCarPeople.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_type")
    public Integer getCollectCarType() {
        return collectCarType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_type")
    public void setCollectCarType(Integer collectCarType) {
        this.collectCarType = collectCarType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_risk_type")
    public Integer getCollectCarRiskType() {
        return collectCarRiskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_risk_type")
    public void setCollectCarRiskType(Integer collectCarRiskType) {
        this.collectCarRiskType = collectCarRiskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.province")
    public String getProvince() {
        return province;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.province")
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.city")
    public String getCity() {
        return city;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.city")
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.address")
    public String getAddress() {
        return address;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.address")
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }
}