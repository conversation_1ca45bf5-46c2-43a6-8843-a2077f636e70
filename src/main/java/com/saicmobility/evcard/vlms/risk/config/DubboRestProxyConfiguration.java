package com.saicmobility.evcard.vlms.risk.config;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class DubboRestProxyConfiguration {

    @Value("${dubbo.proxy.order.orderTravelList}")
    private String orderTravelListUrl;

    @Value("${dubbo.proxy.vehicle.queryVehicleHistoryTrack}")
    private String queryVehicleHistoryTrackUrl;
}