package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 车辆阶段 1 入库阶段、2 固资运营阶段、3 固资退运阶段、4 处置阶段
 * @create: 2020-03-18 14:10
 **/
@AllArgsConstructor
@Getter
public enum VehiclePhaseEnum {

    IN(1, "入库阶段"),
    OPERATING(2, "固资运营阶段"),
    RETURN(3, "固资退运阶段"),
    DISPOSAL(4, "处置阶段")
    ;
    Integer code;
    String value;
    public static String getValueByCode(Integer code) {
        for (VehiclePhaseEnum vehiclePhaseEnum : VehiclePhaseEnum.values()) {
            if (vehiclePhaseEnum.getCode().equals(code)) {
                return vehiclePhaseEnum.getValue();
            }
        }
        return null;
    }
}
