package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableRiskWhitelistConfigService;
import com.saicmobility.evcard.vlms.risk.dto.whitelist.RiskWhitelistConfigData;
import com.saicmobility.evcard.vlms.risk.mapper.RiskWhitelistConfigMapper;
import com.saicmobility.evcard.vlms.risk.mapper.extend.RiskWhitelistConfigExtendMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryWhiteListReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmConfigDynamicSqlSupport.riskAlarmConfig;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmDynamicSqlSupport.riskAlarm;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskWhitelistConfigDynamicSqlSupport.riskWhitelistConfig;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.assetsVehicle;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.VehicleLicenseInfoDynamicSqlSupport.vehicleLicenseInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Service
@Slf4j
public class TableRiskWhitelistConfigServiceImpl implements TableRiskWhitelistConfigService {

    @Resource
    private RiskWhitelistConfigMapper riskWhitelistConfigMapper;

    @Resource
    private RiskWhitelistConfigExtendMapper riskWhitelistConfigExtendMapper;

    @Override
    public RiskWhitelistConfig selectById(Long id) {
        return riskWhitelistConfigMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<RiskWhitelistConfigData> selectList(QueryWhiteListReq req) {
        String userOrgId = req.getCurrentUser().getOrgId();
        int pageNum = req.getPageNum() == 0 ? 1 : req.getPageNum();
        int pageSize = req.getPageSize() == 0 ? 10 : req.getPageSize();
        int limitNum = (pageNum -1) * pageSize;
        // 条件查询
        SelectStatementProvider render = select(
                riskWhitelistConfig.id,
                riskWhitelistConfig.vin,
                riskWhitelistConfig.effectiveStatus,
                riskWhitelistConfig.alarmType,
                riskWhitelistConfig.expirationDate,
                riskWhitelistConfig.miscDesc,
                riskWhitelistConfig.createTime,
                riskWhitelistConfig.createOperName,
                assetsVehicle.propertyOrgId,
                assetsVehicle.propertyOrgName,
                assetsVehicle.operationOrgId,
                assetsVehicle.operationOrgName,
                vehicleLicenseInfo.plateNo
        )
                .from(riskWhitelistConfig)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskWhitelistConfig.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskWhitelistConfig.vin))
                .where()
                .and(riskWhitelistConfig.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(req.getOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)),
                        or(assetsVehicle.operationOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId))))
                .and(assetsVehicle.operationOrgId, isEqualToWhenPresent(req.getOperateOrgId()).filter(StringUtils::isNotBlank))
                .and(riskWhitelistConfig.alarmType, isEqualToWhenPresent(req.getAlarmType()).filter(v -> v!= 0))
                .and(riskWhitelistConfig.effectiveStatus, isEqualToWhenPresent(req.getEffectiveStatus()).filter(v -> v!= 0))
                .and(riskWhitelistConfig.isDeleted, isEqualTo(0))
                .orderBy(riskWhitelistConfig.id.descending())
                .limit(pageSize)
                .offset(limitNum)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        //return riskWhitelistConfigMapper.selectMany(render);
        return  riskWhitelistConfigExtendMapper.selectConfigList(render);
    }

    @Override
    public Long selectTotal(QueryWhiteListReq req) {
        String userOrgId = req.getCurrentUser().getOrgId();
        // 条件查询
        SelectStatementProvider render = select(count())
                .from(riskWhitelistConfig)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskWhitelistConfig.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskWhitelistConfig.vin))
                .where()
                .and(riskWhitelistConfig.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(req.getOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)),
                        or(assetsVehicle.operationOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId))))
                .and(assetsVehicle.operationOrgId, isEqualToWhenPresent(req.getOperateOrgId()).filter(StringUtils::isNotBlank))
                .and(riskWhitelistConfig.alarmType, isEqualToWhenPresent(req.getAlarmType()).filter(v -> v!= 0))
                .and(riskWhitelistConfig.effectiveStatus, isEqualToWhenPresent(req.getEffectiveStatus()).filter(v -> v!= 0))
                .and(riskWhitelistConfig.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskWhitelistConfigMapper.count(render);
    }

    @Override
    public void insert(RiskWhitelistConfig riskWhitelistConfig) {
        riskWhitelistConfigMapper.insertSelective(riskWhitelistConfig);
    }

    @Override
    public void updateById(RiskWhitelistConfig riskWhitelistConfig) {
        riskWhitelistConfigMapper.updateByPrimaryKeySelective(riskWhitelistConfig);
    }

    @Override
    public RiskWhitelistConfig selectConfig(RiskWhitelistConfig req) {
        SelectStatementProvider render = select(
                riskWhitelistConfig.allColumns())
                .from(riskWhitelistConfig)
                .where()
                .and(riskWhitelistConfig.vin, isEqualToWhenPresent(req.getVin()))
                .and(riskWhitelistConfig.alarmType, isEqualToWhenPresent(req.getAlarmType()))
                .and(riskWhitelistConfig.effectiveStatus, isEqualToWhenPresent(req.getEffectiveStatus()))
                .orderBy(riskWhitelistConfig.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskWhitelistConfigMapper.selectOne(render).orElse(null);
    }

    @Override
    public List<RiskWhitelistConfig> selectAll(RiskWhitelistConfig config) {
        SelectStatementProvider render = select(
                riskWhitelistConfig.allColumns())
                .from(riskWhitelistConfig)
                .where()
                .and(riskWhitelistConfig.vin, isEqualToWhenPresent(config.getVin()))
                .and(riskWhitelistConfig.alarmType, isEqualToWhenPresent(config.getAlarmType()))
                .and(riskWhitelistConfig.effectiveStatus, isEqualToWhenPresent(config.getEffectiveStatus()))
                .orderBy(riskWhitelistConfig.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskWhitelistConfigMapper.selectMany(render);
    }
}
