package com.saicmobility.evcard.vlms.risk.error;


import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description 错误码
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    /**
     * CODE:200 操作成功
     */
    SUCCESS(0, "Operation is Successful"),

    /**
     * CODE:401 未授权
     */
    UN_AUTHORIZED(-HttpServletResponse.SC_UNAUTHORIZED, "Request Unauthorized"),

    /**
     * CODE:400 业务异常
     */
    FAILURE(-HttpServletResponse.SC_BAD_REQUEST, "Biz Exception"),

    /**
     * CODE:400 参数异常
     */
    PARAM_VALID_ERROR(-HttpServletResponse.SC_BAD_REQUEST, "Parameter Validation Error"),

    /**
     * CODE:400 参数缺失
     */
    PARAM_TYPE_ERROR(-HttpServletResponse.SC_BAD_REQUEST, "缺少参数，请确认"),

    /**
     * CODE:500 未知内部错误
     */
    INTERNAL_SERVER_ERROR(-HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Internal Server Error"),


    /**
     * 通用失败code
     */
    COMMON_FAIL(-2577001, "Biz Exception"),

    /**
     * 未上传文件
     */
    FILE_NOT_FIND(-2577014, "请上传文件"),

    Param_Err(-2577014, "参数错误"),

    Excel_Err(-2577015, "excel错误"),

    Zip_Err(-2577016, "zip文件上传失败"),

    Fail(-2577999, "业务错误"),

    Cache_Err(-2577017, "缓存错误"),
    ;

    final int code;

    final String message;

}
