package com.saicmobility.evcard.vlms.risk.job;

import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * VehicleHighRiskFenceWarningHandlerOptimized 测试类
 * 
 * 主要测试暂停告警恢复逻辑的正确性
 */
public class VehicleHighRiskFenceWarningHandlerOptimizedTest {

    @Mock
    private VehicleHighRiskFenceWarningHandlerOptimized handler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试暂停告警恢复逻辑 - 长租车辆场景
     */
    @Test
    void testResumeAlarmFromPause_LongTermVehicle() {
        // 准备测试数据
        SearchVehicleFileList vehicle = createTestVehicle(ParserForTypeEnum.LONG_TERM.getCode());
        VehicleGPSData gpsData = createTestGPSData(5); // 5天前的GPS数据
        RiskAlarm existingAlarm = createTestRiskAlarm();
        Integer currentRiskStatus = VehicleRiskStatusEnum.NO_RISK.getCode();

        // 验证业务逻辑
        // 长租车辆，5天超时，应该创建二级告警
        assertTrue(5 >= 3 && 5 < 7, "应该满足长租一级告警条件");
        
        // 如果是7天以上，应该是二级告警
        VehicleGPSData gpsData7Days = createTestGPSData(8);
        assertTrue(8 >= 7, "应该满足长租二级告警条件");
    }

    /**
     * 测试暂停告警恢复逻辑 - 短租车辆场景
     */
    @Test
    void testResumeAlarmFromPause_ShortTermVehicle() {
        // 准备测试数据
        SearchVehicleFileList vehicle = createTestVehicle(ParserForTypeEnum.SHORT_TERM.getCode());
        VehicleGPSData gpsData = createTestGPSData(5); // 5天前的GPS数据
        RiskAlarm existingAlarm = createTestRiskAlarm();
        Integer currentRiskStatus = VehicleRiskStatusEnum.NO_RISK.getCode();

        // 验证业务逻辑
        // 短租车辆，5天超时，应该创建一级告警
        assertTrue(5 < 10, "应该满足短租一级告警条件");
        
        // 如果是10天以上，应该是二级告警
        VehicleGPSData gpsData10Days = createTestGPSData(11);
        assertTrue(11 >= 10, "应该满足短租二级告警条件");
    }

    /**
     * 测试告警级别计算逻辑
     */
    @Test
    void testAlarmLevelCalculation() {
        // 测试长租告警级别计算
        assertEquals(AlarmLevenEnum.ONE.getCode(), 1, "一级告警代码应该是1");
        assertEquals(AlarmLevenEnum.TWO.getCode(), 2, "二级告警代码应该是2");
        
        // 测试车辆风险状态
        assertEquals(VehicleRiskStatusEnum.RISK_VEHICLE.getCode(), 2, "风险车辆状态代码应该是2");
        assertEquals(VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode(), 3, "风控车辆状态代码应该是3");
    }

    /**
     * 测试暂停状态枚举
     */
    @Test
    void testStopAlarmEnum() {
        assertEquals(IsStopAlarmEnum.NO.getCode(), Integer.valueOf(0), "非暂停状态代码应该是0");
        assertEquals(IsStopAlarmEnum.YES.getCode(), Integer.valueOf(1), "暂停状态代码应该是1");
    }

    // 辅助方法

    private SearchVehicleFileList createTestVehicle(Integer productLine) {
        SearchVehicleFileList vehicle = new SearchVehicleFileList();
        vehicle.setVin("TEST_VIN_123");
        vehicle.setPlateNo("测试A12345");
        vehicle.setProductLine(productLine);
        vehicle.setPropertyStatus(1); // 固定资产
        vehicle.setDeliveryStatus(1); // 已交付
        return vehicle;
    }

    private VehicleGPSData createTestGPSData(int daysAgo) {
        VehicleGPSData gpsData = new VehicleGPSData();
        
        // 设置GPS时间为指定天数前
        long gpsTime = System.currentTimeMillis() - (daysAgo * 24 * 60 * 60 * 1000L);
        gpsData.setGpsDateTime(gpsTime);
        gpsData.setOldGpsDateTime(gpsTime);
        
        gpsData.setCityName("上海市");
        gpsData.setPenCityScope("北京市"); // 不同城市，表示超出围栏
        gpsData.setLastAddress("测试地址");
        gpsData.setLatitude("31.230416");
        gpsData.setLongitude("121.473701");
        
        return gpsData;
    }

    private RiskAlarm createTestRiskAlarm() {
        RiskAlarm alarm = new RiskAlarm();
        alarm.setId(1L);
        alarm.setAlarmNo("FKBJ202401010001");
        alarm.setVin("TEST_VIN_123");
        alarm.setAlarmType(AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode());
        alarm.setAlarmLevel(AlarmLevenEnum.ONE.getCode());
        alarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        alarm.setAlarmTime(new Date());
        alarm.setIsStopAlarm(IsStopAlarmEnum.YES.getCode()); // 暂停状态
        return alarm;
    }
}
