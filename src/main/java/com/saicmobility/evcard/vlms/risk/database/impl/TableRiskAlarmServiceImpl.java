package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.dto.riskalarm.RiskAlarmData;
import com.saicmobility.evcard.vlms.risk.enums.AlarmStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.IsStopAlarmEnum;
import com.saicmobility.evcard.vlms.risk.enums.RecoverMethodEnum;
import com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmMapper;
import com.saicmobility.evcard.vlms.risk.mapper.extend.RiskAlarmExtendMapper;
import com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.ObjectValidUtil;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmListReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.QueryExpressionDSL;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.OperationVehicleDynamicSqlSupport.operationVehicle;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmDynamicSqlSupport.riskAlarm;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.assetsVehicle;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.VehicleLicenseInfoDynamicSqlSupport.vehicleLicenseInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-01-18
 */
@Service
@Slf4j
public class TableRiskAlarmServiceImpl implements TableRiskAlarmService {

    @Resource
    private RiskAlarmMapper riskAlarmMapper;

    @Resource
    private RiskAlarmExtendMapper riskAlarmExtendMapper;

    @Resource
    private AssetsVehicleMapper assetsVehicleMapper;

    @Override
    public List<RiskAlarmData> selectList(QueryRiskAlarmListReq req) {

        String userOrgId = req.getCurrentUser().getOrgId();
        int pageNum = req.getPageNum() == 0 ? 1 : req.getPageNum();
        int pageSize = req.getPageSize() == 0 ? 10 : req.getPageSize();
        int limitNum = (pageNum -1) * pageSize;

        String alarmTimeStart = req.getAlarmTimeStart();
        if (req.getAlarmTimeStart().length() != 19) {
            alarmTimeStart = req.getAlarmTimeStart() + " 00:00:00";
        }
        String alarmTimeEnd = req.getAlarmTimeEnd();
        if (req.getAlarmTimeEnd().length() != 19) {
            alarmTimeEnd = req.getAlarmTimeEnd() + " 23:59:59";
        }
        String recoveryTimeStart = req.getRecoveryTimeStart();
        if (req.getRecoveryTimeStart().length() != 19) {
            recoveryTimeStart = req.getRecoveryTimeStart() + " 00:00:00";
        }
        String recoveryTimeEnd = req.getRecoveryTimeEnd();
        if (req.getRecoveryTimeEnd().length() != 19) {
            recoveryTimeEnd = req.getRecoveryTimeEnd() + " 23:59:59";
        }

        Date alarmBeginTime = DateUtil.parse(alarmTimeStart, "yyyy-MM-dd HH:mm:ss");
        Date alarmEndTime = DateUtil.parse(alarmTimeEnd, "yyyy-MM-dd HH:mm:ss");
        Date recoveryStartTime = DateUtil.parse(recoveryTimeStart, "yyyy-MM-dd HH:mm:ss");
        Date recoveryEndTime = DateUtil.parse(recoveryTimeEnd, "yyyy-MM-dd HH:mm:ss");

        Integer subProductLine = req.getSubProductLine();
        log.info("产品线：{}",subProductLine);

        int recoverMethod = req.getRecoverMethod();

        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder sqlBuild = select(
                riskAlarm.allColumns(),
                assetsVehicle.propertyOrgId,
                assetsVehicle.propertyOrgName,
                assetsVehicle.operationOrgId,
                assetsVehicle.operationOrgName,
                assetsVehicle.vehicleRiskStatus,
                assetsVehicle.propertyStatus,
                vehicleLicenseInfo.plateNo,
                operationVehicle.subProductLine
        )
                .from(riskAlarm)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskAlarm.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskAlarm.vin))
                .leftJoin(operationVehicle).on(operationVehicle.vin, equalTo(riskAlarm.vin))
                .where()
                .and(riskAlarm.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(riskAlarm.alarmNo, isEqualToWhenPresent(req.getAlarmNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.operationOrgId, isEqualToWhenPresent(req.getOperateOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(req.getOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)),
                        or(assetsVehicle.operationOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId))))
                .and(riskAlarm.alarmType, isEqualToWhenPresent(req.getAlarmType()).filter(v -> v != 0))
                .and(riskAlarm.alarmType, isInWhenPresent(req.getAlarmTypesList()))
                .and(riskAlarm.alarmStatus, isEqualToWhenPresent(req.getAlarmStatus()).filter(v -> v != 0))
                .and(riskAlarm.recoverMethod, isEqualToWhenPresent(req.getRecoverMethod()).filter(v -> v != 0))
                .and(riskAlarm.alarmSystem, isEqualToWhenPresent(req.getAlarmSystem()).filter(v -> v != 0))
                .and(riskAlarm.recoverChannel, isEqualToWhenPresent(req.getRecoverChannel()).filter(v -> v != 0))
                .and(riskAlarm.alarmLevel, isEqualToWhenPresent(req.getAlarmLevel()).filter(v -> v != 0))
                .and(riskAlarm.alarmTime, isGreaterThanOrEqualTo(alarmBeginTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.alarmTime, isLessThanOrEqualTo(alarmEndTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.recoveryDate, isGreaterThanOrEqualTo(recoveryStartTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.recoveryDate, isLessThanOrEqualTo(recoveryEndTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.isDeleted, isEqualTo(0))
                .and(operationVehicle.subProductLine, isInWhenPresent(req.getSubProductLinesList()))
                .and(operationVehicle.subProductLine, isEqualToWhenPresent(subProductLine).filter(v -> v != 0));


        SelectStatementProvider render = sqlBuild.orderBy(riskAlarm.alarmStatus, riskAlarm.id.descending())
                .limit(pageSize)
                .offset(limitNum)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return riskAlarmExtendMapper.selectRiskAlarmList(render);
    }

    @Override
    public long selectTotal(QueryRiskAlarmListReq req) {
        String userOrgId = req.getCurrentUser().getOrgId();

        Date alarmBeginTime = DateUtil.parse(req.getAlarmTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date alarmEndTime = DateUtil.parse(req.getAlarmTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        Date recoveryStartTime = DateUtil.parse(req.getRecoveryTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date recoveryEndTime = DateUtil.parse(req.getRecoveryTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        int subProductLine = req.getSubProductLine();
        int recoverMethod = req.getRecoverMethod();

        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder sqlBuild = select(
                count(riskAlarm.id)
        )
                .from(riskAlarm)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskAlarm.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskAlarm.vin))
                .leftJoin(operationVehicle).on(operationVehicle.vin, equalTo(riskAlarm.vin))
                .where()
                .and(riskAlarm.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(riskAlarm.alarmNo, isEqualToWhenPresent(req.getAlarmNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.operationOrgId, isEqualToWhenPresent(req.getOperateOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(req.getOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)),
                        or(assetsVehicle.operationOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId))))
                .and(riskAlarm.alarmType, isEqualToWhenPresent(req.getAlarmType()).filter(v -> v != 0))
                .and(riskAlarm.alarmType, isInWhenPresent(req.getAlarmTypesList()))
                .and(riskAlarm.alarmStatus, isEqualToWhenPresent(req.getAlarmStatus()).filter(v -> v != 0))
                .and(riskAlarm.recoverMethod, isEqualToWhenPresent(req.getRecoverMethod()).filter(v -> v != 0))
                .and(riskAlarm.alarmLevel, isEqualToWhenPresent(req.getAlarmLevel()).filter(v -> v != 0))
                .and(riskAlarm.alarmTime, isGreaterThanOrEqualTo(alarmBeginTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.alarmTime, isLessThanOrEqualTo(alarmEndTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.recoveryDate, isGreaterThanOrEqualTo(recoveryStartTime).filter(ObjectValidUtil::isValid))
                .and(riskAlarm.recoveryDate, isLessThanOrEqualTo(recoveryEndTime).filter(ObjectValidUtil::isValid))
                .and(operationVehicle.subProductLine, isEqualToWhenPresent(subProductLine).filter(v -> v != 0))
                .and(operationVehicle.subProductLine, isInWhenPresent(req.getSubProductLinesList()))
                .and(riskAlarm.isDeleted, isEqualTo(0));
        SelectStatementProvider render = sqlBuild.build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmMapper.count(render);
    }

    @Override
    public RiskAlarm queryRiskAlarm(RiskAlarm req) {
        // 条件查询
        SelectStatementProvider render = select(
                riskAlarm.allColumns())
                .from(riskAlarm)
                .where()
                .and(riskAlarm.vin, isEqualToWhenPresent(req.getVin()))
                .and(riskAlarm.alarmNo, isEqualToWhenPresent(req.getAlarmNo()))
                .and(riskAlarm.alarmType, isEqualToWhenPresent(req.getAlarmType()))
                .and(riskAlarm.alarmStatus, isEqualToWhenPresent(req.getAlarmStatus()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmMapper.selectOne(render).orElse(null);
    }

    @Override
    public RiskAlarm queryRiskAlarm(String vin, Integer alarmType, Integer alarmStatus) {
        RiskAlarm riskAlarmQuery = new RiskAlarm();
        riskAlarmQuery.setVin(vin);
        riskAlarmQuery.setAlarmType(alarmType);
        riskAlarmQuery.setAlarmStatus(alarmStatus);
        return this.queryRiskAlarm(riskAlarmQuery);
    }

    @Override
    public RiskAlarm queryRiskAlarmByAlarmNo(String alarmNo) {
        RiskAlarm riskAlarmQuery = new RiskAlarm();
        riskAlarmQuery.setAlarmNo(alarmNo);
        return this.queryRiskAlarm(riskAlarmQuery);
    }

    @Override
    public RiskAlarm selectById(Long id) {
        return riskAlarmMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public void save(RiskAlarm riskAlarm) {
        riskAlarmMapper.insertSelective(riskAlarm);
    }

    @Override
    public List<RiskAlarm> selectAlarmVin(String vin, Integer alarmStatus, Integer queryType) {

        // 条件查询
        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder qew = select(
                riskAlarm.allColumns())
                .from(riskAlarm)
                .where()
                .and(riskAlarm.vin, isEqualToWhenPresent(vin));
        // 前端查询
        if (queryType == 1){
            if (alarmStatus.equals(AlarmStatusEnum.ALARM_STATUS.getCode())){
                // 报警中
                qew.and(riskAlarm.alarmStatus, isEqualToWhenPresent(alarmStatus)).and(riskAlarm.isStopAlarm, isEqualTo(IsStopAlarmEnum.NO.getCode()));
            }else if (alarmStatus.equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
                // 已恢复
                qew.and(riskAlarm.alarmStatus, isEqualToWhenPresent(alarmStatus),
                        or(riskAlarm.alarmStatus, isEqualTo(AlarmStatusEnum.ALARM_STATUS.getCode()),
                                and(riskAlarm.isStopAlarm, isEqualTo(IsStopAlarmEnum.YES.getCode()))));
            }
        }else {
            // 后端查询
            qew.and(riskAlarm.alarmStatus, isEqualToWhenPresent(alarmStatus));
        }
        return riskAlarmMapper.selectMany(qew.build().render(RenderingStrategies.MYBATIS3));
    }

    @Override
    public void update(RiskAlarm riskAlarm) {
        riskAlarmMapper.updateByPrimaryKeySelective(riskAlarm);
    }

    @Override
    public RiskAlarm queryDataByVinAndType(String vin, Integer alarmType) {
        SelectStatementProvider render = select(
                riskAlarm.allColumns())
                .from(riskAlarm)
                .where()
                .and(riskAlarm.vin, isEqualToWhenPresent(vin))
                .and(riskAlarm.alarmType, isEqualToWhenPresent(alarmType))
                .and(riskAlarm.alarmStatus, isEqualToWhenPresent(AlarmStatusEnum.ALARM_STATUS.getCode())
                        ,or(riskAlarm.isStopAlarm, isEqualToWhenPresent(IsStopAlarmEnum.YES.getCode())))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmMapper.selectOne(render).orElse(null);
    }

    @Override
    public List<RiskAlarm> queryDataByType(Integer alarmType) {
        SelectStatementProvider render = select(
                riskAlarm.allColumns())
                .from(riskAlarm)
                .where()
                .and(riskAlarm.alarmType, isEqualToWhenPresent(alarmType))
                .and(riskAlarm.alarmStatus, isEqualToWhenPresent(AlarmStatusEnum.ALARM_STATUS.getCode()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmMapper.selectMany(render);
    }

    @Override
    public List<RiskAlarm> queryAllDataByType() {
        SelectStatementProvider render = select(
                riskAlarm.allColumns())
                .from(riskAlarm)
                .where()
                .and(riskAlarm.alarmStatus, isEqualToWhenPresent(AlarmStatusEnum.ALARM_STATUS.getCode()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskAlarmMapper.selectMany(render);
    }

    @Override
    public void updateByAlarmNo(RiskAlarm alarm) {
        UpdateStatementProvider updateProvider =  SqlBuilder.update(riskAlarm)
                .set(riskAlarm.recoverMethod).equalToWhenPresent(alarm.getRecoverMethod())
                .set(riskAlarm.recoveryDate).equalToWhenPresent(alarm.getRecoveryDate())
                .where(riskAlarm.alarmNo, isEqualTo(alarm.getAlarmNo()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        riskAlarmMapper.update(updateProvider);
    }

}
