package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExcelTypeEnum {

    EXCEL_TYPE_1(1, "风控报警详情导出", "ExportRiskAlarm.xlsx", "风控报警详情导出"),
    EXCEL_TYPE_2(2, "风控收车详情导出", "ExportRiskAlarm.xlsx", "风控收车详情导出"),
    EXCEL_TYPE_3(3, "风控报警配置详情导出", "ExportRiskAlarm.xlsx", "风控报警配置详情导出"),
    EXCEL_TYPE_4(4, "白名单详情导出", "ExportRiskAlarm.xlsx", "白名单详情导出"),
    EXCEL_TYPE_5(5, "电子围栏-单车规则列表导出", "ExportElectronicFenceConfig.xlsx", "电子围栏-单车规则列表导出"),
    EXCEL_TYPE_6(6, "电子围栏-默认规则列表导出", "ExportElectronicFenceConfig.xlsx", "电子围栏-默认规则列表导出"),
    EXCEL_TYPE_7(7, "车辆轨迹导出", "ExportVehicleTrack.xlsx", "车辆轨迹导出"),
    ;

    private final Integer code;
    private final String value;
    private final String fileName;
    private final String template;

    public static String getValueByCode(Integer code){
        for (ExcelTypeEnum item : ExcelTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return "";
    }

    public static String getFileNameByCode(Integer code){
        for (ExcelTypeEnum item : ExcelTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getFileName();
            }
        }
        return "";
    }

    public static String getTemplateByCode(Integer code){
        for (ExcelTypeEnum item : ExcelTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getTemplate();
            }
        }
        return "";
    }
}
