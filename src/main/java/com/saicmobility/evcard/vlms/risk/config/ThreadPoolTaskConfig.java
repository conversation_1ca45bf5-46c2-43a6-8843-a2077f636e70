//package com.saicmobility.evcard.vlms.risk.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
//import java.util.concurrent.ThreadPoolExecutor;
//
///**
//* @Desc 线程池配置
//* <AUTHOR>
//* @Date 2022年6月7日 下午5:12:23
//*/
//@Configuration
//public class ThreadPoolTaskConfig {
//	//线程池
//	@Bean
//	public ThreadPoolTaskExecutor initTaskExecutor() {
//		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
//		taskExecutor.setCorePoolSize(5);
//		taskExecutor.setKeepAliveSeconds(200);
//		taskExecutor.setMaxPoolSize(10);
//		taskExecutor.setQueueCapacity(10);
//		taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//		return taskExecutor;
//	}
//}
//
