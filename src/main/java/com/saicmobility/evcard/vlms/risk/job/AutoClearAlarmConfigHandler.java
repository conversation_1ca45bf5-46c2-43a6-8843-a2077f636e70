package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.model.extend.AssetsVehicle;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.assetsVehicle;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 自动清理车架号不在梧桐系统中的报警配置
 */
@Slf4j
@JobHandler(value = "autoClearAlarmConfig")
@Component
public class AutoClearAlarmConfigHandler extends IJobHandler {

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private AssetsVehicleMapper assetsVehicleMapper;

    private static final Integer PAGE_SIZE = 10000; // 每次查询的条数
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("自动清理车架号不在梧桐系统中的报警配置开始");

        int pageNum = 0;
        do {
            pageNum ++; // 下一页
            log.info("自动清理车架号不在梧桐系统中的报警配置开始,pageNum:{}", pageNum);
            List<RiskAlarmConfig> configList = tableRiskAlarmConfigService.selectConfigGroupByVin(pageNum, PAGE_SIZE);
            if (CollectionUtil.isEmpty(configList)){
                break;
            }
            // 将configList根据Vin 去重
            List<String> vinList = configList.stream().map(RiskAlarmConfig::getVin).distinct().collect(Collectors.toList());
            // 条件查询
            SelectStatementProvider render = select(
                    assetsVehicle.vin
            )
                    .from(assetsVehicle)
                    .where()
                    .and(assetsVehicle.vin, isIn(vinList))
                    .and(assetsVehicle.isDeleted, isEqualTo(0))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            List<AssetsVehicle> assetsVehicles = assetsVehicleMapper.selectMany(render);
            if (CollectionUtil.isEmpty(assetsVehicles)){
                // 删除
                tableRiskAlarmConfigService.deleteByVinList(vinList);
                log.info("删除车辆条数:{}", vinList.size());
                continue;
            }
            // 系统中存在的车
            List<String> wtVinList = assetsVehicles.stream().map(AssetsVehicle::getVin).collect(Collectors.toList());
            // 需要删除的车
            List<String> deleteVinList = vinList.stream().filter(vin -> !wtVinList.contains(vin)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deleteVinList)){
                // 删除
                tableRiskAlarmConfigService.deleteByVinList(deleteVinList);
                log.info("删除车辆条数:{}", deleteVinList.size());
            }
        }while (true);

        log.info("自动清理车架号不在梧桐系统中的报警配置完成");
        return ReturnT.SUCCESS;
    }
}
