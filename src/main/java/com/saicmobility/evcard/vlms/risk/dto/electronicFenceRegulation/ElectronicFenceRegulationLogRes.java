package com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation;

import com.saicmobility.evcard.md.mddataproxy.api.ElectronicFenceRegulation;
import com.saicmobility.evcard.md.mddataproxy.api.QueryElectronicFenceDefaultRegulationLogRes;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ElectronicFenceRegulationLogRes {
    private List<ElectronicFenceRegulationLog> list;

    private Long total;

    public ElectronicFenceRegulationLogRes toRes(QueryElectronicFenceDefaultRegulationLogRes queryElectronicFenceDefaultRegulationLogRes) {
        ElectronicFenceRegulationLogRes electronicFenceRegulationLogRes = new ElectronicFenceRegulationLogRes();
        List<ElectronicFenceRegulation> listList = queryElectronicFenceDefaultRegulationLogRes.getListList();
        List<ElectronicFenceRegulationLog> logs = new ArrayList<>();
        listList.forEach(electronicFenceRegulation -> {
            ElectronicFenceRegulationLog electronicFenceRegulationLog = new ElectronicFenceRegulationLog();
            electronicFenceRegulationLog.setId(electronicFenceRegulation.getId());
            electronicFenceRegulationLog.setElectronicFenceRegulationId(electronicFenceRegulation.getElectronicFenceRegulationId());
            electronicFenceRegulationLog.setCreateTime(electronicFenceRegulation.getCreateTime());
            electronicFenceRegulationLog.setCreateOperName(electronicFenceRegulation.getCreateOperName());
            electronicFenceRegulationLog.setOperateType(electronicFenceRegulation.getOperateType());
            electronicFenceRegulationLog.setOperateContent(electronicFenceRegulation.getOperateContent());
            electronicFenceRegulationLog.setMiscDesc(electronicFenceRegulation.getMiscDesc());
            logs.add(electronicFenceRegulationLog);
        });
        electronicFenceRegulationLogRes.setList(logs);
        electronicFenceRegulationLogRes.setTotal(queryElectronicFenceDefaultRegulationLogRes.getTotal());
        return electronicFenceRegulationLogRes;
    }
}
