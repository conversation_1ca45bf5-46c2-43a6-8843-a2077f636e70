package com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-02-25
 */
@Data
public class RiskAlarmConfigData {

    private Long id;
    private Integer alarmType;
    private String alarmTypeDesc; // 1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失
    private String plateNo; // 车牌号
    private String vin; // 车辆vin
    private String alarmStartTimeStr; // 报警开始时间
    private Date alarmStartTime; // 报警开始时间
    private Integer pauseDay; // 暂停天数
    private Date pauseDeadlineTime; // 报警升级暂停截止日期
    private String pauseDeadlineTimeStr; // 报警升级暂停截止日期
}
