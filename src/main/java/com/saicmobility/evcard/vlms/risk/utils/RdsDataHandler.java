package com.saicmobility.evcard.vlms.risk.utils;

import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.extracme.evcard.dts.avro.TbByteString;
import com.extracme.evcard.dts.avro.TbField;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

@Slf4j
public class RdsDataHandler {
    public static Map<String,String> returnMap(List<TbField> fieldList, String opt){
        Map<String, String> map = Maps.newConcurrentMap();
        if(!fieldList.isEmpty()){
            if(StringUtils.equals("UPDATE", opt)){
                int size = fieldList.size();
                TbField field = null;
                for(int i=1 ;i < size; i = i + 2){
                    field = fieldList.get(i);
                    getTbFieldMaps(map, field);
                }
            }else{
                //修改或者删除
                fieldList.forEach(field -> getTbFieldMaps(map, field));
            }
        }
        return map;
    }

    public static Map<String,String> returnOldMap(List<TbField> fieldList, String opt){
        Map<String, String> map = Maps.newConcurrentMap();
        if(!fieldList.isEmpty()){
            if(StringUtils.equals("UPDATE", opt)){
                int size = fieldList.size();
                TbField field;
                for(int i=0 ;i < size; i = i + 2){
                    field = fieldList.get(i);
                    getTbFieldMaps(map, field);
                }
            }else{
                //修改或者删除
                fieldList.forEach(field -> getTbFieldMaps(map, field));
            }
        }
        return map;
    }

    public static void getTbFieldMaps(Map<String, String> map, TbField field){
        TbByteString byteString = field.getTbByteString();
        try {
            String fieldValue = StringUtils.EMPTY;
            if(byteString != null){
                fieldValue = getTbByteStringVal(byteString,field.getEncoding().toString()).trim();
            }
            map.put(field.getFieldname().toString(), fieldValue);
        } catch (Exception e) {
            // 异常处理：写log, 赋空值
            map.put(field.getFieldname().toString(), StringUtils.EMPTY);
            log.error("处理字段异常：{}|{}|{}",field,byteString ,e.getMessage(),e);
        }
    }

    public static String getTbByteStringVal(TbByteString bs, String encoding) throws UnsupportedEncodingException {
        if (bs.getLen() == 0) {
            return StringUtils.EMPTY;
        }
        if (encoding.equalsIgnoreCase("binary")) {
            throw new UnsupportedEncodingException("field encoding: binary, use getBytes() instead of toString()");
        }
        String realEncoding = encoding;
        if ((encoding.isEmpty()) || (encoding.equalsIgnoreCase("null"))){
            realEncoding = "ASCII";}
        else if (encoding.equalsIgnoreCase("utf8mb4")){
            realEncoding = "utf8";}
        else if (encoding.equalsIgnoreCase("latin1")){
            realEncoding = "cp1252";}
        else if (encoding.equalsIgnoreCase("latin2")) {
            realEncoding = "iso-8859-2";
        }
        return new String(bs.getBytes().array(), realEncoding);
    }
}
