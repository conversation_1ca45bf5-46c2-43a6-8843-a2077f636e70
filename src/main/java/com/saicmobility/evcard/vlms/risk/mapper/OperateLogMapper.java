package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.OperateLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface OperateLogMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, foreignId, operateType, operateContent, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<OperateLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="OperateLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="foreign_id", property="foreignId", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_type", property="operateType", jdbcType=JdbcType.INTEGER),
        @Result(column="operate_content", property="operateContent", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<OperateLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("OperateLogResult")
    Optional<OperateLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, operateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, operateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int insert(OperateLog row) {
        return MyBatis3Utils.insert(this::insert, row, operateLog, c ->
            c.map(foreignId).toProperty("foreignId")
            .map(operateType).toProperty("operateType")
            .map(operateContent).toProperty("operateContent")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int insertSelective(OperateLog row) {
        return MyBatis3Utils.insert(this::insert, row, operateLog, c ->
            c.map(foreignId).toPropertyWhenPresent("foreignId", row::getForeignId)
            .map(operateType).toPropertyWhenPresent("operateType", row::getOperateType)
            .map(operateContent).toPropertyWhenPresent("operateContent", row::getOperateContent)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default Optional<OperateLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, operateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default List<OperateLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, operateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default List<OperateLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, operateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default Optional<OperateLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, operateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    static UpdateDSL<UpdateModel> updateAllColumns(OperateLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(foreignId).equalTo(row::getForeignId)
                .set(operateType).equalTo(row::getOperateType)
                .set(operateContent).equalTo(row::getOperateContent)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(OperateLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(foreignId).equalToWhenPresent(row::getForeignId)
                .set(operateType).equalToWhenPresent(row::getOperateType)
                .set(operateContent).equalToWhenPresent(row::getOperateContent)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int updateByPrimaryKey(OperateLog row) {
        return update(c ->
            c.set(foreignId).equalTo(row::getForeignId)
            .set(operateType).equalTo(row::getOperateType)
            .set(operateContent).equalTo(row::getOperateContent)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    default int updateByPrimaryKeySelective(OperateLog row) {
        return update(c ->
            c.set(foreignId).equalToWhenPresent(row::getForeignId)
            .set(operateType).equalToWhenPresent(row::getOperateType)
            .set(operateContent).equalToWhenPresent(row::getOperateContent)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}