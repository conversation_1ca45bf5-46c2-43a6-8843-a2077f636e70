package com.saicmobility.evcard.vlms.risk.dto.terminal;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.md.mddataproxy.api.CityDataList;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryCityConfigListRes;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class QueryCityConfigListDTORes {

    private List<CityConfig> cityDataList;

    public static QueryCityConfigListRes toRes(List<CityDataList> cityDataListList) {
        QueryCityConfigListRes.Builder builder = QueryCityConfigListRes.newBuilder();
        if(CollectionUtil.isNotEmpty(cityDataListList)){
            List<com.saicmobility.evcard.vlms.vlmsriskservice.api.CityDataList> cityConfigs = new ArrayList<>();
            for (CityDataList dataList : cityDataListList) {
                com.saicmobility.evcard.vlms.vlmsriskservice.api.CityDataList cityDataList =
                        com.saicmobility.evcard.vlms.vlmsriskservice.api.CityDataList.newBuilder().
                                addAllCityNameList(dataList.getCityNameListList()).setCitySort(dataList.getCitySort()).build();
                cityConfigs.add(cityDataList);
            }
            builder.addAllCityDataList(cityConfigs);
        }
        return builder.build();
    }
}
