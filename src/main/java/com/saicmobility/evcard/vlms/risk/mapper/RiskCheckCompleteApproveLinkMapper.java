package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveLinkDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckCompleteApproveLink;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskCheckCompleteApproveLinkMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    BasicColumn[] selectList = BasicColumn.columnList(id, applicationId, checkNo);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskCheckCompleteApproveLink> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskCheckCompleteApproveLinkResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="application_id", property="applicationId", jdbcType=JdbcType.VARCHAR),
        @Result(column="check_no", property="checkNo", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskCheckCompleteApproveLink> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskCheckCompleteApproveLinkResult")
    Optional<RiskCheckCompleteApproveLink> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskCheckCompleteApproveLink, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskCheckCompleteApproveLink, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int insert(RiskCheckCompleteApproveLink row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckCompleteApproveLink, c ->
            c.map(applicationId).toProperty("applicationId")
            .map(checkNo).toProperty("checkNo")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int insertSelective(RiskCheckCompleteApproveLink row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckCompleteApproveLink, c ->
            c.map(applicationId).toPropertyWhenPresent("applicationId", row::getApplicationId)
            .map(checkNo).toPropertyWhenPresent("checkNo", row::getCheckNo)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default Optional<RiskCheckCompleteApproveLink> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskCheckCompleteApproveLink, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default List<RiskCheckCompleteApproveLink> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskCheckCompleteApproveLink, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default List<RiskCheckCompleteApproveLink> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskCheckCompleteApproveLink, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default Optional<RiskCheckCompleteApproveLink> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskCheckCompleteApproveLink, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskCheckCompleteApproveLink row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applicationId).equalTo(row::getApplicationId)
                .set(checkNo).equalTo(row::getCheckNo);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskCheckCompleteApproveLink row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applicationId).equalToWhenPresent(row::getApplicationId)
                .set(checkNo).equalToWhenPresent(row::getCheckNo);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int updateByPrimaryKey(RiskCheckCompleteApproveLink row) {
        return update(c ->
            c.set(applicationId).equalTo(row::getApplicationId)
            .set(checkNo).equalTo(row::getCheckNo)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    default int updateByPrimaryKeySelective(RiskCheckCompleteApproveLink row) {
        return update(c ->
            c.set(applicationId).equalToWhenPresent(row::getApplicationId)
            .set(checkNo).equalToWhenPresent(row::getCheckNo)
            .where(id, isEqualTo(row::getId))
        );
    }
}