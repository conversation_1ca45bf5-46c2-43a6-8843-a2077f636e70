package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   风控收车完成任务白杨审批表-风控收车任务编号关联表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_check_complete_approve_link
 */
public class RiskCheckCompleteApproveLink implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   白杨审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.application_id")
    private String applicationId;

    /**
     * Database Column Remarks:
     *   风控收车任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.check_no")
    private String checkNo;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.application_id")
    public String getApplicationId() {
        return applicationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.application_id")
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId == null ? null : applicationId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.check_no")
    public String getCheckNo() {
        return checkNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.check_no")
    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo == null ? null : checkNo.trim();
    }
}