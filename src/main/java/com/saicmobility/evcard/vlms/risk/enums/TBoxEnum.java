package com.saicmobility.evcard.vlms.risk.enums;

/**
 * @Description: 终端枚举
 * @Author: liu<PERSON><PERSON>
 * @CreateTime: 2023/2/2 14:01
 */
public enum TBoxEnum {

    HONG_LING(1,"HL","hongLing"),//宏瓴
    XIAO_PENG(2,"XP","xiao<PERSON><PERSON>"),//小鹏
    DONG_FENG(3,"DF","dongFeng"),//东风
    NE_ZHA(4,"NZ","neZha"),//哪吒
    BEN_TENG(5,"BT","benTeng"),//奔腾
    V_6(6,"V6","V6"),//V6
    T_BOX(7,"TB","T_BOX"),//T_BOX
    ZHONG_RUI(8,"ZR","zhongRui"),//中瑞有源
    TU_JUN(9,"TJ","tuJun"),//涂俊
    SGT_50(10,"S5","SGT_50"),//SGT_50
    BMW(11,"BMW","BMW"),//BMW
    WAN_WEI(12,"WW","wanWei"),//万位
    OTHER(13,"OTHER","other"),//其他
    DA_TONG(14,"DT","daTong"),//大通
    ZHONG_RUI_WU_YUAN(15,"ZRWY","zhongruiwuyuan"),//中瑞无源
    ZHI_FU_BAO(16,"ZFB","zhifubao"),//支付宝
    ZHI_FU_BAO_ETC(17,"ZFB_ETC","alipay"),//支付宝
    SAIC_CYC(18,"SAIC_CYC","SAICPassengerCars"),//上汽乘用车
    XCQL(19,"qingLu","qingLu"),//擎路携程终端
    SG_YY(20,"SG_YY","saiGeYY"),//赛格有源
    SG_WY(21,"SG_WY","saiGeWY"),//赛格无源
    SG_YC(22,"SG_YC","saiGeYC"),//赛格无源
    HR_HAO_CHE(23,"hrHaoChe","hrHaoChe"),//好人好车
    ;

    private Integer code;

    private String type;

    private String desc;

    public static String getEnumDescByCode(String type) {
        for (TBoxEnum boxEnum : TBoxEnum.values()) {
            if (boxEnum.getType().equals(type)) {
                return boxEnum.getDesc();
            }
        }
        return null;
    }

    private TBoxEnum(Integer code, String type, String desc){
        this.code = code;
        this.type = type;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public void setCode(Integer code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
}
