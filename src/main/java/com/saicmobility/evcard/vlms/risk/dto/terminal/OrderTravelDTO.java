package com.saicmobility.evcard.vlms.risk.dto.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 行程单信息
 *
 * <AUTHOR>
 * @date 2024/4/16 15:18
 */
@Data
public class OrderTravelDTO {

    /**
     * 行程ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderSeq;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 取车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickupTime;

    /**
     * 还车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date returnTime;

    /**
     * 取车时电量百分比
     */
    private Integer electricGetSoc;

    /**
     * 还车时电量百分比
     */
    private Integer electricReturnSoc;

    /**
     * 取车时油量百分比
     */
    private Integer oilGetSoc;

    /**
     * 取车时油量（L）
     */
    private Integer oilGetCapacity;

    /**
     * 还车时油量百分比
     */
    private Integer oilReturnSoc;

    /**
     * 还车时油量
     */
    private Integer oilReturnCapacity;
}
