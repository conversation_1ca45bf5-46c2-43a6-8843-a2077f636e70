package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface RiskAlarmService {
    /**
     * 查询风险告警列表
     * @param queryRiskAlarmListReq
     * @return
     */
    QueryRiskAlarmListRes queryRiskAlarmList(QueryRiskAlarmListReq queryRiskAlarmListReq);

    /**
     * 添加风险告警
     * @param addRiskAlarmReq
     * @return
     */
    AddRiskAlarmRes addRiskAlarm(AddRiskAlarmReq addRiskAlarmReq);

    /**
     * 导出风险告警
     * @param queryRiskAlarmListReq
     * @return
     */
    QueryRiskAlarmListRes exportRiskAlarm(QueryRiskAlarmListReq queryRiskAlarmListReq);

    /**
     * 查询风险告警详情
     * @param queryRiskAlarmDetailReq
     * @return
     */
    QueryRiskAlarmDetailRes queryRiskAlarmDetail(QueryRiskAlarmDetailReq queryRiskAlarmDetailReq);

    /**
     * 暂停风险告警
     * @param pauseRiskAlarmReq
     * @return
     */
    PauseRiskAlarmRes pauseRiskAlarm(PauseRiskAlarmReq pauseRiskAlarmReq);

    /**
     * 报警升级
     * @param upgradeRiskAlarmDescReq
     * @return
     */
    UpgradeRiskAlarmDescRes upgradeRiskAlarmDesc(UpgradeRiskAlarmDescReq upgradeRiskAlarmDescReq);

    /**
     * 解除报警
     * @param removeRiskAlarmReq
     * @return
     */
    RemoveRiskAlarmRes removeRiskAlarm(RemoveRiskAlarmReq removeRiskAlarmReq);

    /**
     * 查询车辆当前所有报警类型
     * @param queryVehicleRiskAlarmTypeReq
     * @return
     */
    QueryVehicleRiskAlarmTypeRes queryVehicleRiskAlarmType(QueryVehicleRiskAlarmTypeReq queryVehicleRiskAlarmTypeReq);

    /**
     * 根据车架号和报警类型查询风险告警
     * @param vin
     * @param gpsSignalOffline
     * @return
     */
    RiskAlarm queryDataByVinAndAlarmType(String vin, Integer gpsSignalOffline);

    /**
     * 处理风险告警（新增、升级、解除）
     * @param riskAlarm
     * @param type
     * @param currentUser
     */
    void handelRiskAlarm(RiskAlarm riskAlarm, Integer type,  CurrentUser currentUser);

    /**
     * 批量暂停
     * @param batchPauseRiskAlarmReq
     * @return
     */
    BatchPauseRiskAlarmRes batchPauseRiskAlarm(BatchPauseRiskAlarmReq batchPauseRiskAlarmReq);

    /**
     * 批量解除
     * @param batchRemoveRiskAlarmReq
     * @return
     */
    BatchRemoveRiskAlarmRes batchRemoveRiskAlarm(BatchRemoveRiskAlarmReq batchRemoveRiskAlarmReq);

    /**
     * 批量报警升级
     * @param batchUpgradeRiskAlarmDescReq
     * @return
     */
    UpgradeRiskAlarmDescRes batchUpgradeRiskAlarmDesc(BatchUpgradeRiskAlarmDescReq batchUpgradeRiskAlarmDescReq);

    /**
     * 完成工单(长租调用)
     * @param finishWorkOrderReq
     * @return
     */
    FinishWorkOrderRes finishWorkOrder(FinishWorkOrderReq finishWorkOrderReq);

    /**
     * 车管风控收车-风控逻辑
     * @param addRiskCheckForReturnCarReq
     * @return
     */
    AddRiskCheckForReturnCarRes addRiskCheckForReturnCar(AddRiskCheckForReturnCarReq addRiskCheckForReturnCarReq);

    /**
     * 长租撤回工单
     * @param cancelWorkOrderReq
     * @return
     */
    CancelWorkOrderRes cancelWorkOrder(CancelWorkOrderReq cancelWorkOrderReq);

    /**
     * 手动取消风控告警
     * @param handleCancelRiskAlarmReq
     * @return
     */
    HandleCancelRiskAlarmRes handleCancelRiskAlarm(HandleCancelRiskAlarmReq handleCancelRiskAlarmReq);

    /**
     * 批量添加风控信息
     * @param batchAddRiskCheckCollectionInfoReq
     * @return
     */
    batchAddRiskCheckCollectionInfoRes batchAddRiskCheckCollectionInfo(batchAddRiskCheckCollectionInfoReq batchAddRiskCheckCollectionInfoReq);
}
