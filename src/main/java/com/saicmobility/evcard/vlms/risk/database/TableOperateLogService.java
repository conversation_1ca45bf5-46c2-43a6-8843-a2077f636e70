package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryOperationLogReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface TableOperateLogService {

    /**
     * 保存操作日志
     * @param operateLog
     */
    void save(OperateLog operateLog);

    /**
     * 查询操作日志总条数
     * @param req
     * @return
     */
    long selectTotal(QueryOperationLogReq req);

    /**
     * 查询操作日志列表
     * @param req
     * @return
     */
    List<OperateLog> selectList(QueryOperationLogReq req);
}
