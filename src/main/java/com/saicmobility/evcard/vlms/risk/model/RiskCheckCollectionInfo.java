package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   风控收车日志表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_check_collection_info
 */
public class RiskCheckCollectionInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   风控收车id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.risk_check_id")
    private Long riskCheckId;

    /**
     * Database Column Remarks:
     *   收车人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_people")
    private String collectCarPeople;

    /**
     * Database Column Remarks:
     *   收车方式：1:内部 2:委外
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_type")
    private Integer collectCarType;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_account")
    private String updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.risk_check_id")
    public Long getRiskCheckId() {
        return riskCheckId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.risk_check_id")
    public void setRiskCheckId(Long riskCheckId) {
        this.riskCheckId = riskCheckId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_people")
    public String getCollectCarPeople() {
        return collectCarPeople;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_people")
    public void setCollectCarPeople(String collectCarPeople) {
        this.collectCarPeople = collectCarPeople == null ? null : collectCarPeople.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_type")
    public Integer getCollectCarType() {
        return collectCarType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_type")
    public void setCollectCarType(Integer collectCarType) {
        this.collectCarType = collectCarType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}