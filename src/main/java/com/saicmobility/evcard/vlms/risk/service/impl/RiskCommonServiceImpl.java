package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy;
import com.saicmobility.evcard.md.mddataproxy.api.OperateContractInfo;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsReq;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsRes;
import com.saicmobility.evcard.md.mdworkservice.api.AutoCompleteSingleVehiclePatrolTaskReq;
import com.saicmobility.evcard.md.mdworkservice.api.MdWorkService;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.database.*;
import com.saicmobility.evcard.vlms.risk.dto.Address;
import com.saicmobility.evcard.vlms.risk.dto.ReverseGeocoding;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import com.saicmobility.evcard.vlms.risk.service.RiskCheckService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.HttpClientUtils;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.*;

import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.vehicleRiskStatus;

/**
 * <AUTHOR>
 * @date 2024-02-24
 */
@Service
@Slf4j
public class RiskCommonServiceImpl extends BaseService implements RiskCommonService {

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskCheckService riskCheckService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private TableRiskWhitelistConfigService tableRiskWhitelistConfigService;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private TableRiskCheckService tableRiskCheckService;

    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;

    @Resource
    private MdWorkService mdWorkService;

    // 假设时间戳格式是毫秒级的数字字符串
    private static final String TIMESTAMP_PATTERN = "\\d+";

    // 年月日时分秒格式假设为 "yyyy-MM-dd HH:mm:ss"
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Override
    public boolean isWhiteList(String vin, Integer alarmType){
        RiskWhitelistConfig whitelistConfig = new RiskWhitelistConfig();
        whitelistConfig.setVin(vin);
        whitelistConfig.setAlarmType(alarmType);
        whitelistConfig.setEffectiveStatus(EffectiveStatusEnum.EFFECTIVE_STATUS.getCode());
        RiskWhitelistConfig whitelistConfigData = tableRiskWhitelistConfigService.selectConfig(whitelistConfig);
        if (whitelistConfigData == null){
            return false;
        }
        return true;
    }

    @Override
    public Long updateRiskAlarmConfig(String vin, Integer alarmType, Date alarmStartTime, Integer pauseDay, Date pauseDeadlineTime, CurrentUser currentUser) {
        RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
        riskAlarmConfig.setVin(vin);
        riskAlarmConfig.setAlarmType(alarmType);
        RiskAlarmConfig config = tableRiskAlarmConfigService.selectRiskAlarmConfig(riskAlarmConfig);

        riskAlarmConfig.setAlarmStartTime(alarmStartTime);
        riskAlarmConfig.setPauseDay(pauseDay);
        riskAlarmConfig.setPauseDeadlineTime(pauseDeadlineTime);
        riskAlarmConfig.setCreateOperName(currentUser.getNickName());
        riskAlarmConfig.setCreateOperAccount(currentUser.getUserAccount());
        tableRiskAlarmConfigService.updateRiskAlarmConfig(riskAlarmConfig);
        return config.getId();
    }

    @Override
    public void addRiskAlarm(SearchVehicleFileList assetsVehicle, VehicleGPSData vehicleGPSData, Integer alarmLevel, Integer alarmType, Integer vehicleRiskStatus) {
        String vin = assetsVehicle.getVin();
        String alarmNo = outDescIdUtil.nextId("FKBJ");
        RiskAlarm riskAlarm = new RiskAlarm();
        Date now = new Date();
        riskAlarm.setAlarmNo(alarmNo);
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmLevel(alarmLevel);
        riskAlarm.setAlarmType(alarmType);
        riskAlarm.setAlarmTime(now);
        riskAlarm.setVin(vin);
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
        if(alarmType == AlarmTypeEnum.VEHICLE_MOVEMENT.getCode()){
            riskAlarm.setExitFenceDate(DateUtil.getDateFromDateStr(DateUtil.getYesterdayDateStr(1),DateUtil.DATE_TYPE5));
        }
        riskAlarm.setCreateOperName("定时任务");
        riskAlarm.setCreateOperAccount("System");
        riskAlarm.setCreateTime(now);
        riskAlarm.setOrderSeq("");
        tableRiskAlarmService.save(riskAlarm);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("添加风控类型【{}】", AlarmTypeEnum.getValueByCode(alarmType)));


        /**
         * 更改车辆风险状态
         */
        Integer status = queryMaxVehicleRiskStatus(assetsVehicle.getVin(), null);

        if(alarmLevel.equals(AlarmLevenEnum.TWO.getCode()) && RiskUtils.isNeedCreateCheck(assetsVehicle.getProductLine(), alarmType)){
            //风控收车
            riskCheckService.autoCreateRiskCheck(riskAlarm,null);
            // 更新车辆报警所有等级
            subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), vehicleRiskStatus > status?vehicleRiskStatus:status);
        }else if (assetsVehicle.getProductLine() == 2 && alarmLevel.equals(AlarmLevenEnum.TWO.getCode())){
            // 如果产品线是长租，且有进行中的长租订单 则推送长租
            QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(assetsVehicle.getVin()).build());
            if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                log.error("查询长租订单合同信息失败! vin:{}", vin);
                return;
            }
            List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
            if (CollectionUtil.isEmpty(operateContractInfoList)){
                log.error("查询长租订单合同信息为空! vin:{}", vin);
                return;
            }
            // 获取订单号
            OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
            String orderNo = operateContractInfo.getOrderNo();
            // 请求长租
            RiskUtils.notifyRiskWorkOrderToLongRent(orderNo, assetsVehicle.getPlateNo(), alarmLevel, alarmNo, alarmType, 1);
            // 记录推送标识，恢复报警需要判断是否已经推送过
            tableRiskPushRecordService.insert(alarmNo);
            // 更新车辆报警所有等级
            subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), vehicleRiskStatus > status?vehicleRiskStatus:status);
        }
    }

    public void updateRiskAlarm(Boolean flag, RiskAlarm riskAlarm,VehicleGPSData vehicleGPSData,Integer productLine) {
        String content;
        Integer status;
        Date now = new Date();
        if(flag){
            if(riskAlarm.getAlarmLevel() == AlarmLevenEnum.TWO.getCode()){
                return;
            }
            content = "升级";
            if(riskAlarm.getAlarmLevel() == AlarmLevenEnum.THREE.getCode()){
                riskAlarm.setAlarmLevel(AlarmLevenEnum.THREE.getCode());
            }else {
                riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
            }
            //风控收车
            if (!RiskUtils.isLongShortGroup(productLine)){
                riskCheckService.autoCreateRiskCheck(riskAlarm,null);
            }
            status = queryMaxVehicleRiskStatus(riskAlarm.getVin(), null);
            if(status < VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()){
                status = VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
            }
        }else {
            content = "恢复";
            riskAlarm.setRecoverMethod(RecoverMethodEnum.AUTO_RECOVER.getCode());
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
            riskAlarm.setRecoveryDate(now);
            status = queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo());

            //  自动完成车辆巡查任务
            autoCompleteSingleVehiclePatrolTask(riskAlarm);
        }
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
        riskAlarm.setUpdateTime(now);
        riskAlarm.setUpdateOperName("定时任务");
        riskAlarm.setUpdateOperAccount("System");
        tableRiskAlarmService.update(riskAlarm);
        subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(),status);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("【{}】告警", content));
    }

    /**
     * 查询最大的风险等级
     * @param vin
     * @param alarmNo 传alarmNo 则代表移除或者恢复
     */
    public Integer queryMaxVehicleRiskStatus(String vin, String alarmNo) {
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(vin,AlarmStatusEnum.ALARM_STATUS.getCode(),2);
        int vehicleRiskStatus = VehicleRiskStatusEnum.NO_RISK.getCode();
        for(RiskAlarm alarm : riskAlarmList){
            if (StringUtils.isNotBlank(alarmNo) && alarm.getAlarmNo().equals(alarmNo)){
                continue;
            }
            if(alarm.getAlarmLevel()+1 > vehicleRiskStatus){
                vehicleRiskStatus = alarm.getAlarmLevel()+1;
            }
        }
        return vehicleRiskStatus;
    }

    /**
     * gps数据
     * @param vin
     * @return
     */
    public VehicleGPSData fetchGpsDate(String vin) {
        VehicleGPSData vehicleGPSData = new VehicleGPSData();
        String latitude = StringUtils.EMPTY;
        String longitude = StringUtils.EMPTY;
        String gpsDateTime = (String) Global.instance.redisUtil.hget(RealFields.VEHICLE_REALDATA_KEY + vin, RealFields.gpsDateTime);
        String wwGpsDateTime = (String) Global.instance.redisUtil.hget(RealFields.VEHICLE_THIRD_REALDATA_KEY + vin, RealFields.gpsDateTime);
        if(StringUtils.isEmpty(wwGpsDateTime) || (StringUtils.isNotEmpty(gpsDateTime) && StringUtils.isNotEmpty(wwGpsDateTime) && DateUtil.compareDate(gpsDateTime,wwGpsDateTime))){
            latitude = (String) Global.instance.redisUtil.hget(RealFields.VEHICLE_REALDATA_KEY + vin, RealFields.Latitude);
            longitude = (String) Global.instance.redisUtil.hget(RealFields.VEHICLE_REALDATA_KEY + vin, RealFields.Longitude);
        }else if(StringUtils.isEmpty(gpsDateTime) || (StringUtils.isNotEmpty(gpsDateTime) && StringUtils.isNotEmpty(wwGpsDateTime) && DateUtil.compareDate(wwGpsDateTime,gpsDateTime))){
            gpsDateTime = wwGpsDateTime;
            latitude = (String) Global.instance.redisUtil.hget(RealFields.VEHICLE_THIRD_REALDATA_KEY + vin, RealFields.Latitude);
            longitude = (String) Global.instance.redisUtil.hget(RealFields.VEHICLE_THIRD_REALDATA_KEY + vin, RealFields.Longitude);
        }
        if(StringUtils.isEmpty(gpsDateTime)){
            vehicleGPSData.setGpsDateTime(null);
            vehicleGPSData.setOldGpsDateTime(null);
        }else {
            if (gpsDateTime.matches(TIMESTAMP_PATTERN)) {
                vehicleGPSData.setGpsDateTime(Long.valueOf(gpsDateTime));
                vehicleGPSData.setOldGpsDateTime(Long.valueOf(gpsDateTime));
            }else {
                vehicleGPSData.setGpsDateTime(DateUtil.getDateFromTimeStr(gpsDateTime, DateUtil.DATE_TYPE1).getTime());
                vehicleGPSData.setOldGpsDateTime(DateUtil.getDateFromTimeStr(gpsDateTime, DateUtil.DATE_TYPE1).getTime());
            }
        }
        if (StringUtils.isNotEmpty(latitude) && StringUtils.isNotEmpty(longitude)) {
            vehicleGPSData.setLatitude(latitude);
            vehicleGPSData.setLongitude(longitude);
            getCityInfoFromBaidu(vehicleGPSData);
        }
        return vehicleGPSData;
    }

    /**
     * 根据经度纬度 调用百度获取城市信息
     * @param vehicleGPSData
     */
    public String  getCityInfoFromBaidu(VehicleGPSData vehicleGPSData){
        String cityName = StringUtils.EMPTY;
        try {
            BigDecimal latitudeDecimel = new BigDecimal(vehicleGPSData.getLatitude());
            BigDecimal longitudeDecimel = new BigDecimal(vehicleGPSData.getLongitude());
            if(longitudeDecimel.compareTo(new BigDecimal(1000))>0){
                longitudeDecimel = new BigDecimal(vehicleGPSData.getLongitude()).divide(BigDecimal.valueOf(1000000));
            }
            if(latitudeDecimel.compareTo(new BigDecimal(1000))>0){
                latitudeDecimel = new BigDecimal(vehicleGPSData.getLatitude()).divide(BigDecimal.valueOf(1000000));
            }
            String ak = "W9iYXHFsSU9Zrs3gX4yDV3l3HztM98f9"; // 百度API密钥
            String url = String.format("http://api.map.baidu.com/reverse_geocoding/v3/?ak=%s&output=json&location=%f,%f", ak, latitudeDecimel.doubleValue(), longitudeDecimel.doubleValue());

            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            CloseableHttpResponse response = httpClient.execute(httpGet);

            String responseBody = EntityUtils.toString(response.getEntity());

            JSONObject json = new JSONObject(responseBody);
            JSONObject result = json.getJSONObject("result");
            cityName = result.getJSONObject("addressComponent").getStr("city");
            vehicleGPSData.setCityName(cityName);
            vehicleGPSData.setLastAddress(StringUtils.isEmpty(result.getStr("formatted_address"))?"":result.getStr("formatted_address"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cityName;
    }
    //百度Web服务URL
    private String URL_BAIDU_GEOCODER = "http://api.map.baidu.com/geocoder/v2/";
    //百度地图服务KEY
    private String KEY_BAIDU = "9a3IloBwW0WGT5Y0Dz0tbywvi3p48H0k";

    private static final String wgs84ll = "wgs84ll";
    private static final String gcj02ll = "gcj02ll";
    private static final String bd09ll = "bd09ll";


    public Address reverseGeocoding(double latitude, double longitude, int type){
        String coordType = wgs84ll;
        if(type == 1) {
            coordType = gcj02ll;
        } else if(type == 2) {
            coordType = bd09ll;
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("ak", KEY_BAIDU);
        params.put("coordtype", coordType);
        params.put("output", "json");
        params.put("location", latitude + "," + longitude);
        try {
            String result = HttpClientUtils.httpGetRequest(URL_BAIDU_GEOCODER, params);
            ReverseGeocoding geocoding = JSON.parseObject(result, ReverseGeocoding.class);
            if(geocoding == null || geocoding.getStatus() != 0) {
                return null;
            }
            Address address = new Address();
            BeanCopyUtils.copyProperties(geocoding.getResult().getAddressComponent(), address);
            address.setFormattedAddress(geocoding.getResult().getFormatted_address());
            address.setLat(geocoding.getResult().getLocation().getLat());
            address.setLng(geocoding.getResult().getLocation().getLng());
            address.setBusiness(geocoding.getResult().getBusiness());
            return address;
        } catch (URISyntaxException e1) {
            return null;
        }
    }

    /*
        （1）车辆只有“GPS信号离线”或“超出电子围栏”一个预警任务，这条唯一的预警恢复后，该车如果有线下核查任务，则自动完成。
        （2）车辆有“逾期未还车”风险预警任务，不论是否有其他预警，“逾期未还车”预警恢复后，该车如果有线下核查任务，则自动完成。
     */
    public void autoCompleteSingleVehiclePatrolTask(RiskAlarm riskAlarm) {
        //  查询车辆的预警任务
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskAlarm.getVin(), null, 2);
        if (!CollectionUtil.isEmpty(riskAlarmList)) {
            //  声明是否需要自动完成线下核查任务标识
            boolean autoCompleteFlag = false;
            int size = riskAlarmList.size();
            for (RiskAlarm alarm : riskAlarmList) {
                Integer alarmType = alarm.getAlarmType();
                if (alarmType == null) {
                    log.error("预警类型为空! vin:{}", riskAlarm.getVin());
                    continue;
                }
                //  判断是否是“GPS信号离线”或“超出电子围栏”
                if(Objects.equals(alarmType, AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode()) || Objects.equals(alarmType, AlarmTypeEnum.OUT_OF_ELECTRONIC_FENCE.getCode())){
                    if (size == 1) {
                        autoCompleteFlag = true;
                        break;
                    }
                }
                //  判断是否是“逾期未还车”
                if(Objects.equals(alarmType, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode())){
                    autoCompleteFlag = true;
                    break;
                }
            }

            if (autoCompleteFlag) {
                //  自动完成线下核查任务
                AutoCompleteSingleVehiclePatrolTaskReq.Builder builder = AutoCompleteSingleVehiclePatrolTaskReq.newBuilder();
                builder.setVinNo(riskAlarm.getVin());
                builder.setAlarmNo(riskAlarm.getAlarmNo());
                builder.setAlarmType(riskAlarm.getAlarmType());
                mdWorkService.autoCompleteSingleVehiclePatrolTask(builder.build());
            }
        }
    }
}
