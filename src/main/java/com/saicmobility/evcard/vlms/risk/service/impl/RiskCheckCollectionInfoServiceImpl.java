package com.saicmobility.evcard.vlms.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy;
import com.saicmobility.evcard.md.mddataproxy.api.OperateContractInfo;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsReq;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsRes;
import com.saicmobility.evcard.utils.FileUtils;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.database.TableAttachmentService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckCollectionInfoService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckService;
import com.saicmobility.evcard.vlms.risk.dto.check.QueryRiskCheckCollectionInfoResponse;
import com.saicmobility.evcard.vlms.risk.dto.check.RiskCheckCollectionInfoData;
import com.saicmobility.evcard.vlms.risk.enums.AttachmentTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.CollectCarTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.DealStatusEnum;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.excel.BatchAddRiskCheckCollectionDemo;
import com.saicmobility.evcard.vlms.risk.excel.ExportErrorInfo;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckCollectionInfo;
import com.saicmobility.evcard.vlms.risk.service.RiskCheckCollectionInfoService;
import com.saicmobility.evcard.vlms.risk.service.base.BaseService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.utils.ExcelUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskCheckCollectionInfoServiceImpl extends BaseService implements RiskCheckCollectionInfoService {

    @Resource
    private TableRiskCheckCollectionInfoService tableRiskCheckCollectionInfoService;

    @Resource
    private TableRiskCheckService tableRiskCheckService;

    @Resource
    private TableAttachmentService tableAttachmentService;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private SubAssetsService subAssetsService;

    @Override
    public QueryRiskCheckCollectionInfoRes queryRiskCheckCollectionInfo(QueryRiskCheckCollectionInfoReq req) {
        QueryRiskCheckCollectionInfoResponse response = new QueryRiskCheckCollectionInfoResponse();
        List<RiskCheckCollectionInfo> riskCheckCollectionInfos = tableRiskCheckCollectionInfoService.selectList(req.getRiskCheckId());
        if (CollectionUtils.isEmpty(riskCheckCollectionInfos)){
            return QueryRiskCheckCollectionInfoRes.ok();
        }
        List<RiskCheckCollectionInfoData> infoDataList = Lists.newArrayList();
        for (RiskCheckCollectionInfo riskCheckCollectionInfo : riskCheckCollectionInfos) {
            RiskCheckCollectionInfoData riskCheckCollectionInfoData = new RiskCheckCollectionInfoData();
            BeanUtils.copyProperties(riskCheckCollectionInfo, riskCheckCollectionInfoData);
            String checkId = String.valueOf(riskCheckCollectionInfo.getId());
            riskCheckCollectionInfoData.setLocationPicInfos(queryAttachmentByFileType(AttachmentTypeEnum.VEHICLE_LOCATION.getCode(), checkId));
            riskCheckCollectionInfoData.setWatermarkPicInfos(queryAttachmentByFileType(AttachmentTypeEnum.VEHICLE_WATERMARK.getCode(), checkId));
            riskCheckCollectionInfoData.setAttachmentInfos(queryAttachmentByFileType(AttachmentTypeEnum.VEHICLE_LOSS.getCode(), checkId));
            infoDataList.add(riskCheckCollectionInfoData);
        }
        response.setInfos(infoDataList);
        return PbConvertUtil.generateProtoBuffer(response, QueryRiskCheckCollectionInfoRes.class);
    }

    @Override
    public ExportRiskCheckCollectionInfoRes exportRiskCheckCollectionInfo(ExportRiskCheckCollectionInfoReq req) {
        if (req.getRiskCheckId() == 0){
           return ExportRiskCheckCollectionInfoRes.failed(-2557001, "参数错误");
        }
        List<RiskCheckCollectionInfo> riskCheckCollectionInfos = tableRiskCheckCollectionInfoService.selectList(req.getRiskCheckId());
        if (CollectionUtils.isEmpty(riskCheckCollectionInfos)){
            return ExportRiskCheckCollectionInfoRes.failed(-2557001, "没有可下载的数据");
        }
        //文件根路径
        String mfsRootPath = Global.instance.getMfsRootPath();
        //文件下载地址
        String projectDownloadUrl = Global.instance.getProjectDownloadUrl();
        //开始导出
        String exportFilePath = FileUtils.getFilePathSuffix("ExportInvoiceInfo.xlsx",2);
        String downLoadFilePath = projectDownloadUrl + exportFilePath;
        String fileName = "收车日志_"+ LocalDateUtil.format(new Date(), LocalDateUtil.UNSIGNED_DATETIME_PATTERN) +".xlsx";

        String fullExportFilePath = mfsRootPath + exportFilePath;
        //如果目录不存在，则创建目录
        File file = new File(fullExportFilePath);
        if(!file.getParentFile().exists()){
            file.getParentFile().mkdirs();
        }

        //导出模板信息
        InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("template/收车日志导出.xlsx");
        try (
                FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
                ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();
        ){

            WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
            List<RiskCheckCollectionInfoData> checkCollectionInfoDataList = new ArrayList<>();
            for (RiskCheckCollectionInfo collectionInfo : riskCheckCollectionInfos){
                RiskCheckCollectionInfoData collectionInfoData = new RiskCheckCollectionInfoData();
                BeanUtils.copyProperties(collectionInfo, collectionInfoData);
                collectionInfoData.setCollectCarTypeDesc(CollectCarTypeEnum.getValueByCode(collectionInfo.getCollectCarType()));

                String checkId = String.valueOf(collectionInfo.getId());
                collectionInfoData.setHasLocationPic(CollectionUtils.isEmpty(queryAttachmentByFileType(AttachmentTypeEnum.VEHICLE_LOCATION.getCode(), checkId)) ? "否" : "是");
                collectionInfoData.setHasWatermarkPic(CollectionUtils.isEmpty(queryAttachmentByFileType(AttachmentTypeEnum.VEHICLE_WATERMARK.getCode(), checkId)) ? "否" : "是");
                collectionInfoData.setHasAttachmentInfo(CollectionUtils.isEmpty(queryAttachmentByFileType(AttachmentTypeEnum.VEHICLE_LOSS.getCode(), checkId)) ? "否" : "是");
                collectionInfoData.setCreateTimeStr(DateUtil.format(collectionInfo.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                checkCollectionInfoDataList.add(collectionInfoData);
            }

            excelWriter.fill(checkCollectionInfoDataList, writeSheet);
            fileOutputStream.flush();
            excelWriter.finish();

        }catch (Exception e){
            log.error(e.getLocalizedMessage(),e);
            return ExportRiskCheckCollectionInfoRes.failed(-2557001, e.getMessage());
        }
        return ExportRiskCheckCollectionInfoRes.newBuilder().setFilePath(downLoadFilePath).build();
    }

    @Override
    @Transactional
    public AddRiskCheckCollectionInfoRes addRiskCheckCollectionInfo(AddRiskCheckCollectionInfoReq req) {
        RiskCheck riskCheck = tableRiskCheckService.selectById(req.getRiskCheckId());
        if (riskCheck == null){
            return AddRiskCheckCollectionInfoRes.failed(-2557001, "风控收车任务不存在");
        }
        // 如果是第一次新增 则推送长租
        List<RiskCheckCollectionInfo> riskCheckCollectionInfos = tableRiskCheckCollectionInfoService.selectList(req.getRiskCheckId());
        Long saveId = tableRiskCheckCollectionInfoService.save(req.getRiskCheckId(), req.getCollectCarType(), req.getCollectCarPeople(), req.getMiscDesc(), req.getCurrentUser());
        if (saveId != null){
            // 批量插入定位截图
            if (CollectionUtils.isNotEmpty(req.getLocationPicInfosList())){
                tableAttachmentService.batchInsert(transferAttachment(req.getLocationPicInfosList(), String.valueOf(saveId), AttachmentTypeEnum.VEHICLE_LOCATION.getCode()));
            }
            // 批量插入水印照片
            if (CollectionUtils.isNotEmpty(req.getWatermarkPicInfosList())){
                tableAttachmentService.batchInsert(transferAttachment(req.getWatermarkPicInfosList(), String.valueOf(saveId), AttachmentTypeEnum.VEHICLE_WATERMARK.getCode()));
            }
            // 批量插入附件
            if (CollectionUtils.isNotEmpty(req.getAttachmentInfosList())){
                tableAttachmentService.batchInsert(transferAttachment(req.getAttachmentInfosList(), String.valueOf(saveId), AttachmentTypeEnum.VEHICLE_LOSS.getCode()));
            }
        }
        if (CollectionUtils.isEmpty(riskCheckCollectionInfos)){
            String vin = riskCheck.getVin();
            SearchVehicleFileList assetsVehicle = subAssetsService.searchAssetsVehicle(vin);
            if (assetsVehicle.getProductLine() == 2){
                // 如果产品线是长租，且有进行中的长租订单 则推送长租
                QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(vin).build());
                if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                    log.error("查询长租订单合同信息失败! vin:{}", vin);
                    return AddRiskCheckCollectionInfoRes.ok();
                }
                List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
                if (CollectionUtil.isEmpty(operateContractInfoList)){
                    log.error("查询长租订单合同信息为空! vin:{}", vin);
                    return AddRiskCheckCollectionInfoRes.ok();
                }
                // 获取订单号
                OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                String orderNo = operateContractInfo.getOrderNo();
                RiskUtils.notifyFirstReturnCarToLongRent(orderNo);
            }
        }
        return AddRiskCheckCollectionInfoRes.ok();
    }

    @Override
    public batchAddRiskCheckCollectionInfoRes batchAddRiskCheckCollectionInfo(batchAddRiskCheckCollectionInfoReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        // 解析excel
        List<BatchAddRiskCheckCollectionDemo> vehicleDemoList = ExcelUtil.read(req.getFilePath(), 0, BatchAddRiskCheckCollectionDemo.class);
        if (CollectionUtil.isEmpty(vehicleDemoList)) {
            throw new ServiceException("请上传车辆清单");
        }
        if (vehicleDemoList.size() > 200){
            throw new ServiceException("单次上传限制200条");
        }
        // 错误信息
        List<ExportErrorInfo> errorInfoList = new ArrayList<>();

        int row = 0;
        for (BatchAddRiskCheckCollectionDemo importVehicleDemo : vehicleDemoList) {
            row ++;
            // 车架号
            String vin = importVehicleDemo.getVin();
            if (StringUtils.isBlank(vin)) {
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行车架号为空", row)));
                continue;
            }
            Boolean doFlag = true;
            // 收车方式判断
            String collectCarType = importVehicleDemo.getCollectCarType();
            if (StringUtils.isBlank(collectCarType)) {
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行收车方式为空", row)));
            } else {
                if (CollectCarTypeEnum.getCodeByValue(collectCarType) == null) {
                    doFlag = false;
                    errorInfoList.add(createErrorInfo(vin, StrUtil.format("收车方式必须是【内部/委外】，当前收车方式【{}】", collectCarType)));
                }
            }

            // 收车人判断
            String collectCarPeople = importVehicleDemo.getCollectCarPeople();
            if (StringUtils.isBlank(collectCarPeople)) {
                errorInfoList.add(createErrorInfo(vin, StrUtil.format("第{}行收车人为空", row)));
            }else if (collectCarPeople.length() > 50) {
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "收车人为长度不能超过50"));
            }
            String miscDesc = importVehicleDemo.getMiscDesc();
            if (StringUtils.isNotBlank(miscDesc) && miscDesc.length() > 500) {
                doFlag = false;
                errorInfoList.add(createErrorInfo(vin, "备注长度不能超过500"));
            }
            // 如果有字段为空或不符合规则
           /* if (!doFlag) {
                continue;
            }*/

            // 查询车辆是否有进行中的风控收车任务
            List<RiskCheck> riskChecks = tableRiskCheckService.selectByVin(vin, DealStatusEnum.DEAL_STATUS.getCode());
            if (CollectionUtil.isEmpty(riskChecks)){
                errorInfoList.add(createErrorInfo(vin, "车辆不存在执行中的风控收车任务"));
            }else {
                RiskCheck riskCheck = riskChecks.get(0);
                importVehicleDemo.setCheckId(riskCheck.getId());
            }
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)){
            String fileName = CharSequenceUtil.format("批量添加收车日志上传错误_{}.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            asynchronousUpload(fileName,126, errorInfoList, currentUser);
            throw new ServiceException("导入错误：请去文件导出页面，下载导入错误信息");
        }

        List<Long> checkIds = vehicleDemoList.stream().map(v -> v.getCheckId()).collect(Collectors.toList());
        // 如果是第一次新增 则推送长租
        List<RiskCheckCollectionInfo> riskCheckCollectionInfos = tableRiskCheckCollectionInfoService.selectListByIds(checkIds);
        //
        Map<Long, RiskCheckCollectionInfo> exitCollectionMap = riskCheckCollectionInfos.stream().collect(Collectors.toMap(v -> v.getId(), v -> v));

        // 请求长租参数
        List<Map<String, Object>> paramMapList = new ArrayList<>();

        for (BatchAddRiskCheckCollectionDemo demo : vehicleDemoList) {
            Long saveId = tableRiskCheckCollectionInfoService.save(demo.getCheckId(), CollectCarTypeEnum.getCodeByValue(demo.getCollectCarType()), demo.getCollectCarPeople(), demo.getMiscDesc(), req.getCurrentUser());
            if (saveId != null){
                // 批量插入定位截图
                if (CollectionUtils.isNotEmpty(req.getLocationPicInfosList())){
                    tableAttachmentService.batchInsert(transferAttachment(req.getLocationPicInfosList(), String.valueOf(saveId), AttachmentTypeEnum.VEHICLE_LOCATION.getCode()));
                }
                // 批量插入水印照片
                if (CollectionUtils.isNotEmpty(req.getWatermarkPicInfosList())){
                    tableAttachmentService.batchInsert(transferAttachment(req.getWatermarkPicInfosList(), String.valueOf(saveId), AttachmentTypeEnum.VEHICLE_WATERMARK.getCode()));
                }
                // 批量插入附件
                if (CollectionUtils.isNotEmpty(req.getAttachmentInfosList())){
                    tableAttachmentService.batchInsert(transferAttachment(req.getAttachmentInfosList(), String.valueOf(saveId), AttachmentTypeEnum.VEHICLE_LOSS.getCode()));
                }
            }

            if (exitCollectionMap.get(demo.getCheckId()) == null){
                String vin = demo.getVin();
                SearchVehicleFileList assetsVehicle = subAssetsService.searchAssetsVehicle(vin);
                if (assetsVehicle.getProductLine() == 2){
                    // 如果产品线是长租，且有进行中的长租订单 则推送长租
                    QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(vin).build());
                    if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                        log.error("查询长租订单合同信息失败! vin:{}", vin);
                        continue;
                    }
                    List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
                    if (CollectionUtil.isEmpty(operateContractInfoList)){
                        log.error("查询长租订单合同信息为空! vin:{}", vin);
                        continue;
                    }
                    // 获取订单号
                    OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                    String orderNo = operateContractInfo.getOrderNo();
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("orderNo", orderNo);
                    //RiskUtils.notifyFirstReturnCarToLongRent(orderNo);
                    paramMapList.add(paramMap);
                }
            }
        }

        RiskUtils.batchNotifyFirstReturnCarToLongRent(paramMapList);
        return batchAddRiskCheckCollectionInfoRes.ok();
    }


    // 创建错误信息
    public ExportErrorInfo createErrorInfo(String vin, String errorMsg) {
        ExportErrorInfo errorInfo = new ExportErrorInfo();
        errorInfo.setVin(vin);
        errorInfo.setError(errorMsg);
        return errorInfo;
    }

    public List<Attachment> queryAttachmentByFileType(int fileType, String relationId){
    	return tableAttachmentService.queryAttachmentByFileType(fileType, relationId);
    }

    public List<Attachment> transferAttachment(List<com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo> attachmentList, String relationId, Integer fileType){
        /**
         *   c.map(fileType).toProperty("fileType")
         *                         .map(relationId).toProperty("relationId")
         *                         .map(fileName).toProperty("fileName")
         *                         .map(filePath).toProperty("filePath")
         *                         .map(contentType).toProperty("contentType")
         *                         .map(createOperAccount).toProperty("createOperAccount")
         *                         .map(createOperName).toProperty("createOperName")
         */
        if (CollectionUtils.isEmpty(attachmentList)){
            return Lists.newArrayList();
        }
    	List<Attachment> attachmentListNew = Lists.newArrayList();
        for (AttachmentInfo attachmentInfo : attachmentList) {
            Attachment attachmentNew = new Attachment();
            BeanUtils.copyProperties(attachmentInfo, attachmentNew);
            attachmentNew.setFileType(fileType);
            attachmentNew.setRelationId(relationId);
            attachmentNew.setCreateOperAccount("System");
            attachmentNew.setCreateOperName("System");
            attachmentListNew.add(attachmentNew);
        }
    	return attachmentListNew;
    }
}
