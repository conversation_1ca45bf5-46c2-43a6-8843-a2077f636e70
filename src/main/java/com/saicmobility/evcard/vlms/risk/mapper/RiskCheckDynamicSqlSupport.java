package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskCheckDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    public static final RiskCheck riskCheck = new RiskCheck();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.id")
    public static final SqlColumn<Long> id = riskCheck.id;

    /**
     * Database Column Remarks:
     *   风控收车编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.check_no")
    public static final SqlColumn<String> checkNo = riskCheck.checkNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vin")
    public static final SqlColumn<String> vin = riskCheck.vin;

    /**
     * Database Column Remarks:
     *   处理状态（1-执行中 2-已执行 3-审批中）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.deal_status")
    public static final SqlColumn<Integer> dealStatus = riskCheck.dealStatus;

    /**
     * Database Column Remarks:
     *   报警系统（1-梧桐 2-长租）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.alarm_system")
    public static final SqlColumn<Integer> alarmSystem = riskCheck.alarmSystem;

    /**
     * Database Column Remarks:
     *   最后一次定位地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location")
    public static final SqlColumn<String> lastLocation = riskCheck.lastLocation;

    /**
     * Database Column Remarks:
     *   最后一次定位时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.last_location_time")
    public static final SqlColumn<Date> lastLocationTime = riskCheck.lastLocationTime;

    /**
     * Database Column Remarks:
     *   失控类型（1-失联车辆、2-灭失车辆、3-纠纷被扣、4-行政被扣）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_type")
    public static final SqlColumn<Integer> outControlType = riskCheck.outControlType;

    /**
     * Database Column Remarks:
     *   升级失控时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_date")
    public static final SqlColumn<Date> outControlDate = riskCheck.outControlDate;

    /**
     * Database Column Remarks:
     *   失控附件关联ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_annex_id")
    public static final SqlColumn<Long> outControlAnnexId = riskCheck.outControlAnnexId;

    /**
     * Database Column Remarks:
     *   失控说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.out_control_desc")
    public static final SqlColumn<String> outControlDesc = riskCheck.outControlDesc;

    /**
     * Database Column Remarks:
     *   车辆照片/附件关联ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_pic_id")
    public static final SqlColumn<Long> vehiclePicId = riskCheck.vehiclePicId;

    /**
     * Database Column Remarks:
     *   车辆收回说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.vehicle_check_desc")
    public static final SqlColumn<String> vehicleCheckDesc = riskCheck.vehicleCheckDesc;

    /**
     * Database Column Remarks:
     *   恢复时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.recovery_time")
    public static final SqlColumn<Date> recoveryTime = riskCheck.recoveryTime;

    /**
     * Database Column Remarks:
     *   完成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.task_end_time")
    public static final SqlColumn<Date> taskEndTime = riskCheck.taskEndTime;

    /**
     * Database Column Remarks:
     *   任务完成处理人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_name")
    public static final SqlColumn<String> endOperName = riskCheck.endOperName;

    /**
     * Database Column Remarks:
     *   任务完成处理人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.end_oper_account")
    public static final SqlColumn<String> endOperAccount = riskCheck.endOperAccount;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_deleted")
    public static final SqlColumn<Integer> isDeleted = riskCheck.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.misc_desc")
    public static final SqlColumn<String> miscDesc = riskCheck.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_time")
    public static final SqlColumn<Date> createTime = riskCheck.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_account")
    public static final SqlColumn<String> createOperAccount = riskCheck.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.create_oper_name")
    public static final SqlColumn<String> createOperName = riskCheck.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_time")
    public static final SqlColumn<Date> updateTime = riskCheck.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = riskCheck.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.update_oper_name")
    public static final SqlColumn<String> updateOperName = riskCheck.updateOperName;

    /**
     * Database Column Remarks:
     *   是否因加入白名单而恢复 0 否，1：是 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_white_list_recover")
    public static final SqlColumn<Integer> isWhiteListRecover = riskCheck.isWhiteListRecover;

    /**
     * Database Column Remarks:
     *   自动完成长租收车 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check.is_auto_complete")
    public static final SqlColumn<Integer> isAutoComplete = riskCheck.isAutoComplete;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check")
    public static final class RiskCheck extends AliasableSqlTable<RiskCheck> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> checkNo = column("check_no", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Integer> dealStatus = column("deal_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> alarmSystem = column("alarm_system", JDBCType.INTEGER);

        public final SqlColumn<String> lastLocation = column("last_location", JDBCType.VARCHAR);

        public final SqlColumn<Date> lastLocationTime = column("last_location_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> outControlType = column("out_control_type", JDBCType.INTEGER);

        public final SqlColumn<Date> outControlDate = column("out_control_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> outControlAnnexId = column("out_control_annex_id", JDBCType.BIGINT);

        public final SqlColumn<String> outControlDesc = column("out_control_desc", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehiclePicId = column("vehicle_pic_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleCheckDesc = column("vehicle_check_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> recoveryTime = column("recovery_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> taskEndTime = column("task_end_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> endOperName = column("end_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<String> endOperAccount = column("end_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isWhiteListRecover = column("is_white_list_recover", JDBCType.INTEGER);

        public final SqlColumn<Integer> isAutoComplete = column("is_auto_complete", JDBCType.INTEGER);

        public RiskCheck() {
            super("t_risk_check", RiskCheck::new);
        }
    }
}