package com.saicmobility.evcard.vlms.risk.dto.check;

import com.saicmobility.evcard.pb.annotation.PbFormat;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import lombok.Data;

import javax.annotation.Generated;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-19
 */
@Data
public class RiskCheckData {

    private Long id;

    /**
     *   风控收车编号
     */
    private String checkNo;

    /**
     *   车牌号
     */
    private String plateNo;

    /**
     *   车架号
     */
    private String vin;

    /**
     *   所属公司编码
     */
    private String orgId;

    /**
     *   所属公司名称
     */
    private String orgName;

    /**
     *   运营公司编码
     */
    private String operateOrgId;

    /**
     *   运营公司名称
     */
    private String operateOrgName;

    /**
     *   处理状态（1-执行中 2-已执行）
     */
    private Integer dealStatus;

    private String dealStatusDesc;

    /**
     *   车辆风险状态（1-无风险 2-风险车辆 3-风控车辆 4-失控车辆）
     */
    private Integer vehicleRiskStatus;

    private String vehicleRiskStatusDesc;

    /**
     *   最后一次定位地址
     */
    private String lastLocation;

    /**
     *   最后一次定位时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLocationTime;

    private String lastLocationTimeStr;

    /**
     *   失控类型（1-车辆失联 2-车辆灭失）
     */
    private Integer outControlType;

    private String outControlTypeDesc;

    /**
     *   升级失控时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outControlTime;

    private String outControlDateStr;

    /**
     *   失控附件关联ID
     */
    private Long outControlAnnexId;

    /**
     *   失控说明
     */
    private String outControlDesc;

    /**
     *   车辆照片/附件关联ID
     */
    private Long vehiclePicId;

    /**
     *   车辆收回说明
     */
    private String vehicleCheckDesc;

    /**
     *   恢复时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recoveryTime;

    /**
     *   完成时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskEndTime;

    private String taskEndTimeStr;

    /**
     *   任务完成处理人
     */
    private String endOperName;

    /**
     *   任务完成处理人域账号
     */
    private String endOperAccount;

    /**
     *   删除状态 0-否,1-是
     */
    private Integer isDeleted;

    /**
     *   备注
     */
    private String miscDesc;

    /**
     *   创建时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String createTimeStr;
    /**
     *   创建人域账号
     */
    private String createOperAccount;

    /**
     *    创建人姓名
     */
    private String createOperName;

    /**
     *   更新时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *   修改人域账号
     */
    private String updateOperAccount;

    /**
     *   更新人姓名
     */
    private String updateOperName;

    /**
     * 当前报警中风险数量
     */
    private Integer alarmNum;

    /**
     * 失控附件
     */
    private Attachment outControlAttachment;

    /**
     * 车辆照片/附件
     */
    private Attachment vehicleAttachment;

    private Integer productLine;

    private String productLineDesc;

    private Integer subProductLine;

    private String subProductLineDesc;

    private String storeName;

    private Integer propertyStatus;

    private String propertyStatusDesc;

    private String currentAlarmType;

    /**
     *  收车时间：年月日
     */
    private String collectCarDatetime;
    /**
     *  收车地点 省+市+具体地点，省市选择，地点文本输入
     */
    private String collectCarPlace ;

    /**
     * 收车人/供应商：文本输入
     */
    private String collectCarPeople;
    /**
     *  收车方式：1:内部 2:委外
     */
    private Integer collectCarType;

    /**
     *  风控类型: 1:风控车辆 2:失控车辆
     */
    private Integer collectCarRiskType;

    /**
     *【验车单】：附件（1个）
     */
    private Attachment checkVehicleFormInfo;

    private List<Attachment> checkVehicleFormInfos;

    /**
     *【车辆清收音视频资料】：附件（1个）
     */
    private Attachment checkVehicleVideoInfo;
    private List<Attachment> checkVehicleVideoInfos;


    /**
     *车内物品交接单】：附件（1个）
     */
    private Attachment vehicleItemInfo;
    private List<Attachment> vehicleItemInfos;

    /**
     * 其他
     */
    private List<Attachment> attachmentPaths;

    /**
     *收车日志数量
     */
    private Integer collectInfoCount;

    /**
     * 当前最新的白杨审批单号
     */
    private String latestApplicationId;

    /**
     * 当前最新的提交给白杨的请求编号
     */
    private String latestRequestNo;

    /**
     * 长租任务编号
     */
    private String rentTaskNo;

    private String province;

    private String city;

    private String address;
}
