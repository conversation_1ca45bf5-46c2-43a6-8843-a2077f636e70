package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableVehicleMovementDayStatisticsService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.VehicleMovementDayStatistics;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileListReq;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileListRes;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 车辆异动预警及恢复
 * <AUTHOR>
 * @date 2024-01-18
 */
@Slf4j
@JobHandler(value = "vehicleMovementWarningHandler")
@Component
public class VehicleMovementWarningHandler extends IJobHandler {

    @Resource
    public OutDescIdUtil outDescIdUtil;

    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private TableVehicleMovementDayStatisticsService tableVehicleMovementDayStatisticsService;

    private final static int pageSize = 1000;

    private static final BigDecimal MOVING_DISTANCE_10 = new BigDecimal(10); // 10公里异动
    private static final int DAYS_WARNING_2 = 2; // 2天恢复告警
    private static final int DAYS_WARNING_7 = 7; // 7天升级告警

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("vehicleMovementWarningHandler:开始执行任务!");
        Integer pageNum = 1;
        List<RiskAlarmConfigData> queryRiskAlarmConfigRes;
        do {
            queryRiskAlarmConfigRes = tableRiskAlarmConfigService.selectList(QueryRiskAlarmConfigReq.newBuilder().setAlarmType(AlarmTypeEnum.VEHICLE_MOVEMENT.getCode())
                    .setPageNum(pageNum).setPageSize(pageSize).build());
            if (CollectionUtil.isEmpty(queryRiskAlarmConfigRes)) {
                log.info("vehicleMovementWarningHandler:未查询到风控配置异动车辆列表!");
                return ReturnT.SUCCESS;
            }
            for(RiskAlarmConfigData vehicle : queryRiskAlarmConfigRes){
                try{
                    if(riskCommonService.isWhiteList(vehicle.getVin(), AlarmTypeEnum.VEHICLE_MOVEMENT.getCode())){
                        continue;
                    }
                    SearchVehicleFileListRes searchAssetsVehicleRes = vlmsAssetsService.searchVehicleFileListForRisk(SearchVehicleFileListReq.newBuilder()
                            .setPageNum(1).setPageSize(1).setVin(vehicle.getVin()).build());
                    if(null == searchAssetsVehicleRes || searchAssetsVehicleRes.getInfoList().isEmpty()){
                        continue;
                    }
                    // 资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
                    SearchVehicleFileList assetsVehicle = searchAssetsVehicleRes.getInfoList().get(0);
                    int propertyStatus = assetsVehicle.getPropertyStatus();
                    if (RiskUtils.isIgnoreAlarm(propertyStatus, assetsVehicle.getDeliveryStatus())){
                        continue;
                    }
                    //查询gps信息
                    VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(vehicle.getVin());
                    if(null == vehicleGPSData.getGpsDateTime() || vehicle.getAlarmStartTime().after(new Date(vehicleGPSData.getGpsDateTime()))){
                        vehicleGPSData.setGpsDateTime(vehicle.getAlarmStartTime().getTime());
                    }
                    if(null != vehicle.getPauseDeadlineTime()){
                        vehicleGPSData.setPauseDeadlineTime(vehicle.getPauseDeadlineTime());
                    }
                    //查询是否存在进行中的异动告警
                    RiskAlarm riskAlarm = riskAlarmService.queryDataByVinAndAlarmType(vehicle.getVin(), AlarmTypeEnum.VEHICLE_MOVEMENT.getCode());
                    if(null == riskAlarm && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().after(new Date())){
                        continue;
                    }
                    vehicleMovementWarning(searchAssetsVehicleRes.getInfoList().get(0),vehicleGPSData,riskAlarm);
                }catch (Exception e){
                    log.error("vehicleMovementWarningHandler:车辆异动预警及恢复异常! vin:{}", vehicle.getVin(), e);
                }
            }
            pageNum++;
           /* queryRiskAlarmConfigRes = tableRiskAlarmConfigService.selectList(QueryRiskAlarmConfigReq.newBuilder().setAlarmType(AlarmTypeEnum.VEHICLE_MOVEMENT.getCode())
                    .setPageNum(pageNum).setPageSize(pageSize).build());*/
        }while (CollectionUtil.isNotEmpty(queryRiskAlarmConfigRes));
        log.info("vehicleMovementWarningHandler:结束执行任务!");
        return ReturnT.SUCCESS;
    }

    /**
     * 异动逻辑处理
     * @param vehicle
     * @param riskAlarm
     * @param vehicleGPSData
     */
    private void vehicleMovementWarning(SearchVehicleFileList vehicle,VehicleGPSData vehicleGPSData,RiskAlarm riskAlarm ) {
        String yesterdayStr = DateUtil.getYesterdayDateStr(1);
        //查询车辆今天是否存在异动
        VehicleMovementDayStatistics vehicleMovementDayStatistics = tableVehicleMovementDayStatisticsService.queryDataByVinAndStatDate(vehicle.getVin(),yesterdayStr);
        if(null != vehicleMovementDayStatistics && vehicleMovementDayStatistics.getMileageDay().compareTo(MOVING_DISTANCE_10) >= 0){ //车辆今天已经存在异动
            //新建告警
            if(null == riskAlarm){
                //判断是否连续7天告警
                if(isContinuousWarning(vehicle.getVin())){
                    riskCommonService.addRiskAlarm(vehicle,vehicleGPSData,AlarmLevenEnum.TWO.getCode(),AlarmTypeEnum.VEHICLE_MOVEMENT.getCode(),VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode());
                    return;
                }
                riskCommonService.addRiskAlarm(vehicle,vehicleGPSData,AlarmLevenEnum.ONE.getCode() ,AlarmTypeEnum.VEHICLE_MOVEMENT.getCode(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
                return;
            }else {
                //暂停告警
                if(riskAlarm.getAlarmStatus() != AlarmStatusEnum.ALARM_STATUS.getCode() && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().before(new Date())){
                    isStopAlarm(riskAlarm,vehicle,vehicleGPSData);
                    return;
                }
                // 如果报警是暂停，且告警状态是恢复，则不处理
                if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
                    return;
                }
                //判断是否连续7天告警
                if(isContinuousWarning(vehicle.getVin())){
                    riskCommonService.updateRiskAlarm(true,riskAlarm,vehicleGPSData,vehicle.getProductLine());
                    return;
                }
            }
            return;
        }else { //今天未存在异动
            if(null == riskAlarm){
                return;
            }
            //暂停告警逻辑
            if(riskAlarm.getAlarmStatus() != AlarmStatusEnum.ALARM_STATUS.getCode() && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().before(new Date())){
                isStopAlarm(riskAlarm,vehicle,vehicleGPSData);
                return;
            }
            // 如果报警不是暂停，且告警状态是恢复，则不处理
            if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
                return;
            }
            //判断是否连续7天告警
            if(isContinuousWarning(vehicle.getVin())){
                riskCommonService.updateRiskAlarm(true,riskAlarm,vehicleGPSData,vehicle.getProductLine());
                return;
            }
            //如果上次告警时间距当前日期超过两天则恢复告警
            //车管所的车，不自动恢复
            if(isContinuousNOWarning(vehicle.getVin())&&vehicle.getProductLine()!=ParserForTypeEnum.VEHICLE_MANAGEMENT_CENTER.getCode()){
                //恢复告警
                riskCommonService.updateRiskAlarm(false,riskAlarm,vehicleGPSData,vehicle.getProductLine());
            }
        }
    }

    /**
     * 判断是否连续2天无告警
     * @param vin
     * @return
     */
    private boolean isContinuousNOWarning(String vin) {
        int num = 0;
        for(int i = 0; i < DAYS_WARNING_2; i++){
            VehicleMovementDayStatistics vehicleMovementDayStatistics = tableVehicleMovementDayStatisticsService.queryDataByVinAndStatDate(vin,DateUtil.getYesterdayDateStr(i+1));
            if(null == vehicleMovementDayStatistics || vehicleMovementDayStatistics.getMileageDay().compareTo(MOVING_DISTANCE_10) < 0){
                num ++;
            }
        }
        if(num == DAYS_WARNING_2){
            return true;
        }
        return false;
    }

    /**
     * 判断是否连续7天告警
     * @param vin
     */
    private boolean isContinuousWarning(String vin) {
        int num = 0;
        for(int i = 0; i < DAYS_WARNING_7; i++){
            VehicleMovementDayStatistics vehicleMovementDayStatistics = tableVehicleMovementDayStatisticsService.queryDataByVinAndStatDate(vin,DateUtil.getYesterdayDateStr(i+1));
            if(null != vehicleMovementDayStatistics && vehicleMovementDayStatistics.getMileageDay().compareTo(MOVING_DISTANCE_10) >= 0){
                num ++;
            }
        }
        if(num == DAYS_WARNING_7){
            return true;
        }
        return false;
    }

    /**
     * 暂停告警逻辑处理
     * @param riskAlarm
     * @param vehicle
     * @param vehicleGPSData
     */
    private void isStopAlarm(RiskAlarm riskAlarm, SearchVehicleFileList vehicle, VehicleGPSData vehicleGPSData) {
        RiskAlarm updateRiskAlarm = new RiskAlarm();
        updateRiskAlarm.setId(riskAlarm.getId());
        updateRiskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        tableRiskAlarmService.update(updateRiskAlarm);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("暂停后恢复风控报警【{}】", AlarmTypeEnum.getValueByCode(AlarmTypeEnum.VEHICLE_MOVEMENT.getCode())));
        Date now = new Date();
        //查询当前车辆存在的进行中的最高级别告警
        Integer vehicleRiskStatusNew = 0;
        if(isContinuousNOWarning(vehicle.getVin())){
            subAssetsService.updateVehicleRiskStatusByVin(vehicle.getVin(),riskCommonService.queryMaxVehicleRiskStatus(vehicle.getVin(), riskAlarm.getAlarmNo()));
            return;
        }
        //创建新告警记录
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
        if(isContinuousWarning(vehicle.getVin())){
            //升级告警
            riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
            vehicleRiskStatusNew = VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
        }
        riskAlarm.setId(null);
        riskAlarm.setRecoveryDate(null);
        riskAlarm.setRecoverMethod(null);
        riskAlarm.setStopAlarmDay(null);
        riskAlarm.setPauseDeadlineTime(null);
        riskAlarm.setAlarmTime(now);
        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ"));
        riskAlarm.setCreateTime(now);
        riskAlarm.setCreateOperName("定时任务");
        riskAlarm.setCreateOperAccount("System");
        riskAlarm.setRecoverChannel(0);
        tableRiskAlarmService.save(riskAlarm);
        Integer vehicleRiskStatus = riskCommonService.queryMaxVehicleRiskStatus(vehicle.getVin(), null);
        subAssetsService.updateVehicleRiskStatusByVin(vehicle.getVin(),vehicleRiskStatusNew > vehicleRiskStatus?vehicleRiskStatusNew:vehicleRiskStatus);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("添加风控类型【{}】", AlarmTypeEnum.getValueByCode(AlarmTypeEnum.VEHICLE_MOVEMENT.getCode())));
    }

    /**
     * 记录日志
     * @param relationKey
     * @param operateContent
     */
    private void saveLog(String relationKey, String operateContent) {
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(OperateTypeEnum.OPERATE_TYPE.getCode());
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }
}
