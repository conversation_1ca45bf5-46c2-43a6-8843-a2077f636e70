package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AttachmentDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    public static final Attachment attachment = new Attachment();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.id")
    public static final SqlColumn<Long> id = attachment.id;

    /**
     * Database Column Remarks:
     *   文件类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.file_type")
    public static final SqlColumn<Integer> fileType = attachment.fileType;

    /**
     * Database Column Remarks:
     *   关联id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.relation_id")
    public static final SqlColumn<String> relationId = attachment.relationId;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.file_name")
    public static final SqlColumn<String> fileName = attachment.fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.file_path")
    public static final SqlColumn<String> filePath = attachment.filePath;

    /**
     * Database Column Remarks:
     *   文件大小 字节
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.file_size")
    public static final SqlColumn<Long> fileSize = attachment.fileSize;

    /**
     * Database Column Remarks:
     *   文件类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.content_type")
    public static final SqlColumn<String> contentType = attachment.contentType;

    /**
     * Database Column Remarks:
     *   文件描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.file_desc")
    public static final SqlColumn<String> fileDesc = attachment.fileDesc;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.is_deleted")
    public static final SqlColumn<Integer> isDeleted = attachment.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.create_time")
    public static final SqlColumn<Date> createTime = attachment.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.create_oper_account")
    public static final SqlColumn<String> createOperAccount = attachment.createOperAccount;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.create_oper_name")
    public static final SqlColumn<String> createOperName = attachment.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.update_time")
    public static final SqlColumn<Date> updateTime = attachment.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = attachment.updateOperAccount;

    /**
     * Database Column Remarks:
     *   修改人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_attachment.update_oper_name")
    public static final SqlColumn<String> updateOperName = attachment.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_attachment")
    public static final class Attachment extends AliasableSqlTable<Attachment> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> fileType = column("file_type", JDBCType.INTEGER);

        public final SqlColumn<String> relationId = column("relation_id", JDBCType.VARCHAR);

        public final SqlColumn<String> fileName = column("file_name", JDBCType.VARCHAR);

        public final SqlColumn<String> filePath = column("file_path", JDBCType.VARCHAR);

        public final SqlColumn<Long> fileSize = column("file_size", JDBCType.BIGINT);

        public final SqlColumn<String> contentType = column("content_type", JDBCType.VARCHAR);

        public final SqlColumn<String> fileDesc = column("file_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public Attachment() {
            super("t_attachment", Attachment::new);
        }
    }
}