package com.saicmobility.evcard.vlms.risk.service.impl;

import com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckCompleteApprove;
import com.saicmobility.evcard.vlms.risk.service.RiskCheckCompleteApproveService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveDynamicSqlSupport.riskCheckCompleteApprove;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveLinkDynamicSqlSupport.riskCheckCompleteApproveLink;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
@Slf4j
public class RiskCheckCompleteApproveServiceImpl implements RiskCheckCompleteApproveService {

    @Resource
    private RiskCheckCompleteApproveMapper riskCheckCompleteApproveMapper;

    @Override
    public void insertSelective(RiskCheckCompleteApprove newApprove) {
        riskCheckCompleteApproveMapper.insertSelective(newApprove);
    }

    /**
     * 根据applicationId查询
     *
     * @param applicationId
     * @return
     */
    @Override
    public RiskCheckCompleteApprove selectByApplicationId(String applicationId) {
        SelectStatementProvider render = select(
                riskCheckCompleteApprove.allColumns())
                .from(riskCheckCompleteApprove)
                .where()
                .and(riskCheckCompleteApprove.applicationId, isEqualToWhenPresent(applicationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckCompleteApproveMapper.selectOne(render).orElse(null);
    }

    /**
     * 更新审批状态
     *
     * @param applicationId
     * @param status
     */
    @Override
    public void updateApproveStatus(String applicationId, int status) {
        UpdateStatementProvider updateProvider = SqlBuilder.update(riskCheckCompleteApprove)
                .set(riskCheckCompleteApprove.approveStatus).equalTo(status)
                .set(riskCheckCompleteApprove.updateTime).equalTo(new Date())
                .set(riskCheckCompleteApprove.updateOperAccount).equalTo("白杨审批回调")
                .set(riskCheckCompleteApprove.updateOperName).equalTo("白杨审批回调")
                .where(riskCheckCompleteApprove.applicationId, isEqualTo(applicationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        riskCheckCompleteApproveMapper.update(updateProvider);
    }

    /**
     * 根据checkNo查询最新一条记录
     * @param checkNo
     * @return
     */
    @Override
    public RiskCheckCompleteApprove selectLatestRequestNoByCheckNo(String checkNo) {
        SelectStatementProvider render = select(
                riskCheckCompleteApprove.allColumns())
                .from(riskCheckCompleteApprove)
                .leftJoin(riskCheckCompleteApproveLink).on(riskCheckCompleteApprove.applicationId, equalTo(riskCheckCompleteApproveLink.applicationId))
                .where()
                .and(riskCheckCompleteApproveLink.checkNo, isEqualToWhenPresent(checkNo))
                .orderBy(riskCheckCompleteApprove.createTime.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckCompleteApproveMapper.selectOne(render).orElse(null);
    }
}
