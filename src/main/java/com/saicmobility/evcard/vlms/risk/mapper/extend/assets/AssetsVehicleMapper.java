package com.saicmobility.evcard.vlms.risk.mapper.extend.assets;

import com.saicmobility.evcard.vlms.risk.model.extend.AssetsVehicle;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

import javax.annotation.Generated;

import java.util.List;
import java.util.Optional;

import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface AssetsVehicleMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper  , CommonInsertMapper<AssetsVehicle> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    BasicColumn[] selectList = BasicColumn.columnList(id,
            vin,
            engineNo,
            color,
            propertyStatus,
            propertyOrgId,
            propertyOrgName,
            operationOrgId,
            operationOrgName,
            deliveryStatus,
            vehicleModelId,
            brandModelId,
            vehicleChannel,
            vehicleSource,
            putInStatus,
            sysTermId,
            commercialInsuranceExpire,
            compulsoryInsuranceExpire,
            vehicleTransferTime,
            profitCenter,
            isDeleted,
            createTime,
            createOperAccount,
            createOperName,
            updateTime,
            updateOperAccount,
            updateOperName,
            systemUpdateTime,
            systemSyncCode,
            vehicleRiskStatus);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<AssetsVehicle> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AssetsVehicleResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="color", property="color", jdbcType=JdbcType.VARCHAR),
        @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="property_org_id", property="propertyOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="property_org_name", property="propertyOrgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="operation_org_id", property="operationOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="operation_org_name", property="operationOrgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="delivery_status", property="deliveryStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="brand_model_id", property="brandModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_channel", property="vehicleChannel", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_source", property="vehicleSource", jdbcType=JdbcType.INTEGER),
        @Result(column="put_in_status", property="putInStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="sys_term_id", property="sysTermId", jdbcType=JdbcType.BIGINT),
        @Result(column="commercial_insurance_expire", property="commercialInsuranceExpire", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="compulsory_insurance_expire", property="compulsoryInsuranceExpire", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="vehicle_transfer_time", property="vehicleTransferTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="profit_center", property="profitCenter", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="system_update_time", property="systemUpdateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="system_sync_code", property="systemSyncCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_risk_status", property="vehicleRiskStatus", jdbcType=JdbcType.INTEGER)
    })
    List<AssetsVehicle> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AssetsVehicleResult")
    Optional<AssetsVehicle> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, assetsVehicle, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, assetsVehicle, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int insert(AssetsVehicle row) {
        return MyBatis3Utils.insert(this::insert, row, assetsVehicle, c ->
            c.map(vin).toProperty("vin")
            .map(engineNo).toProperty("engineNo")
            .map(color).toProperty("color")
            .map(propertyStatus).toProperty("propertyStatus")
            .map(propertyOrgId).toProperty("propertyOrgId")
            .map(propertyOrgName).toProperty("propertyOrgName")
            .map(operationOrgId).toProperty("operationOrgId")
            .map(operationOrgName).toProperty("operationOrgName")
            .map(deliveryStatus).toProperty("deliveryStatus")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(brandModelId).toProperty("brandModelId")
            .map(vehicleChannel).toProperty("vehicleChannel")
            .map(vehicleSource).toProperty("vehicleSource")
            .map(putInStatus).toProperty("putInStatus")
            .map(sysTermId).toProperty("sysTermId")
            .map(commercialInsuranceExpire).toProperty("commercialInsuranceExpire")
            .map(compulsoryInsuranceExpire).toProperty("compulsoryInsuranceExpire")
            .map(vehicleTransferTime).toProperty("vehicleTransferTime")
            .map(profitCenter).toProperty("profitCenter")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
            .map(systemUpdateTime).toProperty("systemUpdateTime")
            .map(systemSyncCode).toProperty("systemSyncCode")
            .map(vehicleRiskStatus).toProperty("vehicleRiskStatus")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int insertSelective(AssetsVehicle row) {
        return MyBatis3Utils.insert(this::insert, row, assetsVehicle, c ->
            c.map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(engineNo).toPropertyWhenPresent("engineNo", row::getEngineNo)
            .map(color).toPropertyWhenPresent("color", row::getColor)
            .map(propertyStatus).toPropertyWhenPresent("propertyStatus", row::getPropertyStatus)
            .map(propertyOrgId).toPropertyWhenPresent("propertyOrgId", row::getPropertyOrgId)
            .map(propertyOrgName).toPropertyWhenPresent("propertyOrgName", row::getPropertyOrgName)
            .map(operationOrgId).toPropertyWhenPresent("operationOrgId", row::getOperationOrgId)
            .map(operationOrgName).toPropertyWhenPresent("operationOrgName", row::getOperationOrgName)
            .map(deliveryStatus).toPropertyWhenPresent("deliveryStatus", row::getDeliveryStatus)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", row::getVehicleModelId)
            .map(brandModelId).toPropertyWhenPresent("brandModelId", row::getBrandModelId)
            .map(vehicleChannel).toPropertyWhenPresent("vehicleChannel", row::getVehicleChannel)
            .map(vehicleSource).toPropertyWhenPresent("vehicleSource", row::getVehicleSource)
            .map(putInStatus).toPropertyWhenPresent("putInStatus", row::getPutInStatus)
            .map(sysTermId).toPropertyWhenPresent("sysTermId", row::getSysTermId)
            .map(commercialInsuranceExpire).toPropertyWhenPresent("commercialInsuranceExpire", row::getCommercialInsuranceExpire)
            .map(compulsoryInsuranceExpire).toPropertyWhenPresent("compulsoryInsuranceExpire", row::getCompulsoryInsuranceExpire)
            .map(vehicleTransferTime).toPropertyWhenPresent("vehicleTransferTime", row::getVehicleTransferTime)
            .map(profitCenter).toPropertyWhenPresent("profitCenter", row::getProfitCenter)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
            .map(systemUpdateTime).toPropertyWhenPresent("systemUpdateTime", row::getSystemUpdateTime)
            .map(systemSyncCode).toPropertyWhenPresent("systemSyncCode", row::getSystemSyncCode)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default Optional<AssetsVehicle> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, assetsVehicle, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default List<AssetsVehicle> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, assetsVehicle, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default List<AssetsVehicle> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, assetsVehicle, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default Optional<AssetsVehicle> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, assetsVehicle, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    static UpdateDSL<UpdateModel> updateAllColumns(AssetsVehicle row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(row::getVin)
                .set(engineNo).equalTo(row::getEngineNo)
                .set(color).equalTo(row::getColor)
                .set(propertyStatus).equalTo(row::getPropertyStatus)
                .set(propertyOrgId).equalTo(row::getPropertyOrgId)
                .set(propertyOrgName).equalTo(row::getPropertyOrgName)
                .set(operationOrgId).equalTo(row::getOperationOrgId)
                .set(operationOrgName).equalTo(row::getOperationOrgName)
                .set(deliveryStatus).equalTo(row::getDeliveryStatus)
                .set(vehicleModelId).equalTo(row::getVehicleModelId)
                .set(brandModelId).equalTo(row::getBrandModelId)
                .set(vehicleChannel).equalTo(row::getVehicleChannel)
                .set(vehicleSource).equalTo(row::getVehicleSource)
                .set(putInStatus).equalTo(row::getPutInStatus)
                .set(sysTermId).equalTo(row::getSysTermId)
                .set(commercialInsuranceExpire).equalTo(row::getCommercialInsuranceExpire)
                .set(compulsoryInsuranceExpire).equalTo(row::getCompulsoryInsuranceExpire)
                .set(vehicleTransferTime).equalTo(row::getVehicleTransferTime)
                .set(profitCenter).equalTo(row::getProfitCenter)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName)
                .set(systemUpdateTime).equalTo(row::getSystemUpdateTime)
                .set(systemSyncCode).equalTo(row::getSystemSyncCode);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AssetsVehicle row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(row::getVin)
                .set(engineNo).equalToWhenPresent(row::getEngineNo)
                .set(color).equalToWhenPresent(row::getColor)
                .set(propertyStatus).equalToWhenPresent(row::getPropertyStatus)
                .set(propertyOrgId).equalToWhenPresent(row::getPropertyOrgId)
                .set(propertyOrgName).equalToWhenPresent(row::getPropertyOrgName)
                .set(operationOrgId).equalToWhenPresent(row::getOperationOrgId)
                .set(operationOrgName).equalToWhenPresent(row::getOperationOrgName)
                .set(deliveryStatus).equalToWhenPresent(row::getDeliveryStatus)
                .set(vehicleModelId).equalToWhenPresent(row::getVehicleModelId)
                .set(brandModelId).equalToWhenPresent(row::getBrandModelId)
                .set(vehicleChannel).equalToWhenPresent(row::getVehicleChannel)
                .set(vehicleSource).equalToWhenPresent(row::getVehicleSource)
                .set(putInStatus).equalToWhenPresent(row::getPutInStatus)
                .set(sysTermId).equalToWhenPresent(row::getSysTermId)
                .set(commercialInsuranceExpire).equalToWhenPresent(row::getCommercialInsuranceExpire)
                .set(compulsoryInsuranceExpire).equalToWhenPresent(row::getCompulsoryInsuranceExpire)
                .set(vehicleTransferTime).equalToWhenPresent(row::getVehicleTransferTime)
                .set(profitCenter).equalToWhenPresent(row::getProfitCenter)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
                .set(systemUpdateTime).equalToWhenPresent(row::getSystemUpdateTime)
                .set(systemSyncCode).equalToWhenPresent(row::getSystemSyncCode);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int updateByPrimaryKey(AssetsVehicle row) {
        return update(c ->
            c.set(vin).equalTo(row::getVin)
            .set(engineNo).equalTo(row::getEngineNo)
            .set(color).equalTo(row::getColor)
            .set(propertyStatus).equalTo(row::getPropertyStatus)
            .set(propertyOrgId).equalTo(row::getPropertyOrgId)
            .set(propertyOrgName).equalTo(row::getPropertyOrgName)
            .set(operationOrgId).equalTo(row::getOperationOrgId)
            .set(operationOrgName).equalTo(row::getOperationOrgName)
            .set(deliveryStatus).equalTo(row::getDeliveryStatus)
            .set(vehicleModelId).equalTo(row::getVehicleModelId)
            .set(brandModelId).equalTo(row::getBrandModelId)
            .set(vehicleChannel).equalTo(row::getVehicleChannel)
            .set(vehicleSource).equalTo(row::getVehicleSource)
            .set(putInStatus).equalTo(row::getPutInStatus)
            .set(sysTermId).equalTo(row::getSysTermId)
            .set(commercialInsuranceExpire).equalTo(row::getCommercialInsuranceExpire)
            .set(compulsoryInsuranceExpire).equalTo(row::getCompulsoryInsuranceExpire)
            .set(vehicleTransferTime).equalTo(row::getVehicleTransferTime)
            .set(profitCenter).equalTo(row::getProfitCenter)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .set(systemUpdateTime).equalTo(row::getSystemUpdateTime)
            .set(systemSyncCode).equalTo(row::getSystemSyncCode)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    default int updateByPrimaryKeySelective(AssetsVehicle row) {
        return update(c ->
            c.set(vin).equalToWhenPresent(row::getVin)
            .set(engineNo).equalToWhenPresent(row::getEngineNo)
            .set(color).equalToWhenPresent(row::getColor)
            .set(propertyStatus).equalToWhenPresent(row::getPropertyStatus)
            .set(propertyOrgId).equalToWhenPresent(row::getPropertyOrgId)
            .set(propertyOrgName).equalToWhenPresent(row::getPropertyOrgName)
            .set(operationOrgId).equalToWhenPresent(row::getOperationOrgId)
            .set(operationOrgName).equalToWhenPresent(row::getOperationOrgName)
            .set(deliveryStatus).equalToWhenPresent(row::getDeliveryStatus)
            .set(vehicleModelId).equalToWhenPresent(row::getVehicleModelId)
            .set(brandModelId).equalToWhenPresent(row::getBrandModelId)
            .set(vehicleChannel).equalToWhenPresent(row::getVehicleChannel)
            .set(vehicleSource).equalToWhenPresent(row::getVehicleSource)
            .set(putInStatus).equalToWhenPresent(row::getPutInStatus)
            .set(sysTermId).equalToWhenPresent(row::getSysTermId)
            .set(commercialInsuranceExpire).equalToWhenPresent(row::getCommercialInsuranceExpire)
            .set(compulsoryInsuranceExpire).equalToWhenPresent(row::getCompulsoryInsuranceExpire)
            .set(vehicleTransferTime).equalToWhenPresent(row::getVehicleTransferTime)
            .set(profitCenter).equalToWhenPresent(row::getProfitCenter)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .set(systemUpdateTime).equalToWhenPresent(row::getSystemUpdateTime)
            .set(systemSyncCode).equalToWhenPresent(row::getSystemSyncCode)
            .where(id, isEqualTo(row::getId))
        );
    }
}