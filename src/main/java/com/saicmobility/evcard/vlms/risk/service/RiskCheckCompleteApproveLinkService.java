package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckCompleteApproveLink;

import java.util.List;

public interface RiskCheckCompleteApproveLinkService {

    void insertSelective(RiskCheckCompleteApproveLink riskCheckCompleteApproveLink);

    /**
     * 根据applicationId查询
     * @param applicationId
     * @return
     */
    List<RiskCheckCompleteApproveLink> selectByApplicationId(String applicationId);
}
