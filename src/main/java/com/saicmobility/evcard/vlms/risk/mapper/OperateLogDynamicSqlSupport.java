package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class OperateLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    public static final OperateLog operateLog = new OperateLog();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.id")
    public static final SqlColumn<Long> id = operateLog.id;

    /**
     * Database Column Remarks:
     *   外键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.foreign_id")
    public static final SqlColumn<String> foreignId = operateLog.foreignId;

    /**
     * Database Column Remarks:
     *   操作类型（1-风控报警 2-风控收车 3-风控报警配置 4-暂停时长配置 5-白名单配置）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.operate_type")
    public static final SqlColumn<Integer> operateType = operateLog.operateType;

    /**
     * Database Column Remarks:
     *   操作内容
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.operate_content")
    public static final SqlColumn<String> operateContent = operateLog.operateContent;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.is_deleted")
    public static final SqlColumn<Integer> isDeleted = operateLog.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.misc_desc")
    public static final SqlColumn<String> miscDesc = operateLog.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.create_time")
    public static final SqlColumn<Date> createTime = operateLog.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.create_oper_account")
    public static final SqlColumn<String> createOperAccount = operateLog.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.create_oper_name")
    public static final SqlColumn<String> createOperName = operateLog.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.update_time")
    public static final SqlColumn<Date> updateTime = operateLog.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = operateLog.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_operate_log.update_oper_name")
    public static final SqlColumn<String> updateOperName = operateLog.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_operate_log")
    public static final class OperateLog extends AliasableSqlTable<OperateLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> foreignId = column("foreign_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> operateType = column("operate_type", JDBCType.INTEGER);

        public final SqlColumn<String> operateContent = column("operate_content", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public OperateLog() {
            super("t_operate_log", OperateLog::new);
        }
    }
}