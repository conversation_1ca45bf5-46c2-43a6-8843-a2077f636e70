package com.saicmobility.evcard.vlms.risk;

import com.saicmobility.evcard.vlms.risk.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * @date 2022/2/23 10:28
 */
@Component
public class Global {
    
    public static Global instance;


    @Resource
    public RedisUtil redisUtil;

    @Value("${mfs.root.path}")
    public String mfsRootPath;


    @Value("${project.outer.download.url}")
    public String projectOuterDownloadUrl;

    @Value("${project.download.url}")
    public String projectDownloadUrl;

    @Value("${bdp.inner.url}")
    public String bdpInnerUrl;

    @Value("${long.rent.url}")
    public String longRentUrl;

    @Value("${baiYangRiskCheckCompleteKey}")
    public String baiYangRiskCheckCompleteKey;

    @Autowired
    public ConfigLoader configLoader;

    @PostConstruct
    public void init() {
        instance = this;
    }

    public String getMfsRootPath() {
        return mfsRootPath;
    }

    public String getProjectDownloadUrl() {
        return projectDownloadUrl;
    }

    public String getBdpInnerUrl() {
        return bdpInnerUrl;
    }

    public String getLongRentUrl() {
        return longRentUrl;
    }

    public String getBaiYangRiskCheckCompleteKey() {
        return baiYangRiskCheckCompleteKey;
    }

    public void setBaiYangRiskCheckCompleteKey(String baiYangRiskCheckCompleteKey) {
        this.baiYangRiskCheckCompleteKey = baiYangRiskCheckCompleteKey;
    }
}
