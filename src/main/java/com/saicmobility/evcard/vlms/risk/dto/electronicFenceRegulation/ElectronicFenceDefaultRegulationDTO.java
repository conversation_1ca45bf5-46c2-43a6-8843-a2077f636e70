package com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation;

import lombok.Data;

@Data
public class ElectronicFenceDefaultRegulationDTO {

    private Long id;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆业务状态（0-分时租赁、1-长租、2-专车、3-短租、4-公务用车、5-市场用车、6-其他、7-课题测试）
     */
    private Integer rentType;

    /**
     * 车辆业务状态（0-分时租赁、1-长租、2-专车、3-短租、4-公务用车、5-市场用车、6-其他、7-课题测试）
     */
    private String rentTypeToString;

    /**
     * 所属公司id
     */
    private String orgId;

    /**
     * 所属公司名称
     */
    private String orgName;

    /**
     * 运营公司id
     */
    private String operationOrgId;

    /**
     * 运营公司名称
     */
    private String operationOrgName;

    /**
     * 围栏城市id范围
     */
    private String penCityIdScope;

    /**
     * 围栏城市范围
     */
    private String penCityScope;

    /**
     * 规则类型 1、默认规则 2、单车规则
     */
    private Integer regulationType;

    /**
     * 产品线 1:车管中心 2:长租 3:短租 4:公务用车
     */
    private Integer productLine;
}
