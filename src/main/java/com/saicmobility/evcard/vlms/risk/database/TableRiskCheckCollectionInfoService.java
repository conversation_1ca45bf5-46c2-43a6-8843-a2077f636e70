package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckCollectionInfo;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;

import java.util.List;

public interface TableRiskCheckCollectionInfoService {

    /**
     * 查询风控收车列表
     * @param alarmCheckId
     * @return
     */
    List<RiskCheckCollectionInfo> selectList(Long alarmCheckId);


    /**
     * 查询风控收车列表
     * @param checkIds
     * @return
     */
    List<RiskCheckCollectionInfo> selectListByIds(List<Long> checkIds);

    /**
     * 新增风控收车记录
     * @param collectCarType
     * @param collectCarPeople
     * @param currentUser
     */
    Long save(Long riskCheckId, Integer collectCarType, String collectCarPeople, String miscDesc, CurrentUser currentUser);
}
