package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.enums.AlarmStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.RecoverMethodEnum;
import com.saicmobility.evcard.vlms.risk.enums.RiskOperateEnum;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.*;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 定时恢复全部风控报警
 */

@Slf4j
@JobHandler(value = "autoRecoveryAllRisk")
@Component
public class AutoRecoveryAllRiskHandler extends IJobHandler {

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private RiskCommonService baseService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private SubRiskAlaramService subRiskAlaramService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.queryAllDataByType();
        if (CollectionUtil.isEmpty(riskAlarmList)){
            return ReturnT.SUCCESS;
        }
        for (RiskAlarm riskAlarm : riskAlarmList) {
            if (StringUtils.isBlank(riskAlarm.getVin())){
                continue;
            }
            try {
                AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskAlarm.getVin());
                if (vehicleInfoByVin != null){
                    // 资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
                    int propertyStatus = vehicleInfoByVin.getPropertyStatus();
                    // 交付状态 0 未交付 1 已交付
                    int deliveryStatus = vehicleInfoByVin.getDeliveryStatus();
                    if (propertyStatus == 3 || propertyStatus == 5 || (deliveryStatus == 1 && propertyStatus == 8)){
                        removeRiskAlarm(riskAlarm, "车辆【车辆资产状态】：已处置、报废，或已处置（未过户）已交付自动解除风险");
                    }
                }

            }catch (Exception e){
                log.error("恢复异常----车架号：{}", riskAlarm.getVin());
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 解除风险
     * @param riskAlarm
     * @param opereateContent
     */
    public void removeRiskAlarm(RiskAlarm riskAlarm, String opereateContent){
        int maxVehicleStatus = baseService.queryMaxVehicleRiskStatus(riskAlarm.getVin(), riskAlarm.getAlarmNo());
        riskAlarm.setRecoverMethod(RecoverMethodEnum.AUTO_RECOVER.getCode());
        riskAlarm.setRecoveryDate(new Date());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
        //  自动完成车辆巡查任务
        baseService.autoCompleteSingleVehiclePatrolTask(riskAlarm);
        // 处理恢复风控逻辑
        riskAlarmService.handelRiskAlarm(riskAlarm, RiskOperateEnum.OPERATE_REMOVE.getCode(), CurrentUser.newBuilder().setNickName("定时任务").setUserAccount("System").build());
        saveOperateLog(opereateContent, riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode());

        // 车辆日志
        saveVehicleLog(riskAlarm.getVin(), opereateContent);
        // 同步更新车辆风控状态
        subAssetsService.updateVehicleRiskStatusByVin(riskAlarm.getVin(), maxVehicleStatus);

        // 通知长租
        subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
    }

    public void saveOperateLog(String operateContent, String relationKey, Integer operateType){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }


    /**
     * 根据车架号查询车辆信息
     * @param vin
     * @return
     */
    public AssetsVehicle getVehicleInfoByVin(String vin){
        GetAssetsVehicleByVinRes res = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vin).build());
        List<AssetsVehicle> vehicles = res.getInfoList();
        if (CollectionUtil.isEmpty(vehicles)) {
            return null;
        }
        return vehicles.get(0);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vin
     * @param operateContent
     */
    public void saveVehicleLog(String vin, String operateContent){
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
        logList.add(log);
        // 新增日志
        insertVehicleOperateLog(logList);
    }

    /**
     * 新增操作
     * @param logList
     */
    public void insertVehicleOperateLog(List<SaveVehicleOperateLog> logList){
        com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser user = com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser
                .newBuilder()
                .setNickName("定时任务")
                .setUserNo("")
                .setUserAccount("System").build();
        // 保存车辆预风控记录
        SaveVehicleOperateLogRes res = vlmsAssetsService.saveVehicleOperateLog(SaveVehicleOperateLogReq.newBuilder().addAllLogs(logList).setBusinessType(3).setCurrentUser(user).build());
        if (res.getRetCode() != 0) {
            throw new ServiceException( "保存车辆风控操作日志失败");
        }
    }
}
