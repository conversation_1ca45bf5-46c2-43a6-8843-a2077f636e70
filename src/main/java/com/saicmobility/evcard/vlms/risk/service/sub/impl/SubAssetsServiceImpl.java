package com.saicmobility.evcard.vlms.risk.service.sub.impl;

import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-30
 */
@Service
@Slf4j
public class SubAssetsServiceImpl implements SubAssetsService {

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Override
    public SearchVehicleFileList searchAssetsVehicle(String vin) {
        List<SearchVehicleFileList> searchVehicleFileLists = this.searchVehicleFileList(null, null, 0, vin);
        if (CollectionUtils.isNotEmpty(searchVehicleFileLists)){
            return searchVehicleFileLists.get(0);
        }else {
            throw new ServiceException("车辆信息不存在");
        }
    }

    @Override
    public List<SearchVehicleFileList> searchVehicleFileList(Integer pageNum, Integer pageSize, Integer vehiclePhase, String vin) {
        SearchVehicleFileListRes searchVehicleFileListRes = vlmsAssetsService.searchVehicleFileListForRisk(SearchVehicleFileListReq.newBuilder()
                .setPageNum(pageNum == null ? 1 : pageNum).setPageSize(pageSize == null ? 99 : pageSize).setVin(vin).setVehiclePhase(vehiclePhase).build());
        if (searchVehicleFileListRes != null) {
            return searchVehicleFileListRes.getInfoList();
        }
        return null;
    }

    @Override
    public void updateVehicleRiskStatusByVin(String vin, Integer vehicleRiskStatus) {
        vlmsAssetsService.updateVehicleRiskStatusByVin(UpdateVehicleRiskStatusByVinReq.newBuilder().setVin(vin).setVehicleRiskStatus(vehicleRiskStatus).build());
    }
}
