package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskCheckExtendInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    public static final RiskCheckExtendInfo riskCheckExtendInfo = new RiskCheckExtendInfo();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.id")
    public static final SqlColumn<Long> id = riskCheckExtendInfo.id;

    /**
     * Database Column Remarks:
     *   风控收车id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.risk_check_id")
    public static final SqlColumn<Long> riskCheckId = riskCheckExtendInfo.riskCheckId;

    /**
     * Database Column Remarks:
     *   收车时间：年月日
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_datetime")
    public static final SqlColumn<String> collectCarDatetime = riskCheckExtendInfo.collectCarDatetime;

    /**
     * Database Column Remarks:
     *   收车地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_place")
    public static final SqlColumn<String> collectCarPlace = riskCheckExtendInfo.collectCarPlace;

    /**
     * Database Column Remarks:
     *   收车人/供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_people")
    public static final SqlColumn<String> collectCarPeople = riskCheckExtendInfo.collectCarPeople;

    /**
     * Database Column Remarks:
     *   收车方式：1:内部 2:委外
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_type")
    public static final SqlColumn<Integer> collectCarType = riskCheckExtendInfo.collectCarType;

    /**
     * Database Column Remarks:
     *   风控类型: 1:风控车辆 2:失控车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.collect_car_risk_type")
    public static final SqlColumn<Integer> collectCarRiskType = riskCheckExtendInfo.collectCarRiskType;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = riskCheckExtendInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.misc_desc")
    public static final SqlColumn<String> miscDesc = riskCheckExtendInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_time")
    public static final SqlColumn<Date> createTime = riskCheckExtendInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_account")
    public static final SqlColumn<String> createOperAccount = riskCheckExtendInfo.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.create_oper_name")
    public static final SqlColumn<String> createOperName = riskCheckExtendInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_time")
    public static final SqlColumn<Date> updateTime = riskCheckExtendInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = riskCheckExtendInfo.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = riskCheckExtendInfo.updateOperName;

    /**
     * Database Column Remarks:
     *   省
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.province")
    public static final SqlColumn<String> province = riskCheckExtendInfo.province;

    /**
     * Database Column Remarks:
     *   市
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.city")
    public static final SqlColumn<String> city = riskCheckExtendInfo.city;

    /**
     * Database Column Remarks:
     *   地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_extend_info.address")
    public static final SqlColumn<String> address = riskCheckExtendInfo.address;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_extend_info")
    public static final class RiskCheckExtendInfo extends AliasableSqlTable<RiskCheckExtendInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> riskCheckId = column("risk_check_id", JDBCType.BIGINT);

        public final SqlColumn<String> collectCarDatetime = column("collect_car_datetime", JDBCType.VARCHAR);

        public final SqlColumn<String> collectCarPlace = column("collect_car_place", JDBCType.VARCHAR);

        public final SqlColumn<String> collectCarPeople = column("collect_car_people", JDBCType.VARCHAR);

        public final SqlColumn<Integer> collectCarType = column("collect_car_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> collectCarRiskType = column("collect_car_risk_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<String> province = column("province", JDBCType.VARCHAR);

        public final SqlColumn<String> city = column("city", JDBCType.VARCHAR);

        public final SqlColumn<String> address = column("address", JDBCType.VARCHAR);

        public RiskCheckExtendInfo() {
            super("t_risk_check_extend_info", RiskCheckExtendInfo::new);
        }
    }
}