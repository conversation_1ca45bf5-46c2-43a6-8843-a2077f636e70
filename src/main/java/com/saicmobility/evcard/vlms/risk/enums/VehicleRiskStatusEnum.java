package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车辆风险状态 0-无风险 1-风险车辆 2-风控车辆 3-失控车辆
 * <AUTHOR>
 * @date 2024-01-16
 */
@AllArgsConstructor
@Getter
public enum VehicleRiskStatusEnum {
    NO_RISK(1, "无风险"),
    RISK_VEHICLE(2, "风险车辆"),
    RISK_CONTROL_VEHICLE(3, "风控车辆"),
    OVER_CONTROL_VEHICLE(4, "失控车辆");

    private Integer code;
    private String value;
    public static String getValueByCode(Integer code) {
        for (VehicleRiskStatusEnum vehicleRiskStatusEnum : VehicleRiskStatusEnum.values()) {
            if (vehicleRiskStatusEnum.getCode() == code) {
                return vehicleRiskStatusEnum.getValue();
            }
        }
        return null;
    }

}
