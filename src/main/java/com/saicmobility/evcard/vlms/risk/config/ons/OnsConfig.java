package com.saicmobility.evcard.vlms.risk.config.ons;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.aliyun.openservices.ons.api.order.OrderConsumer;
import com.saicmobility.evcard.vlms.risk.listener.CompleteOrderlistener;
import com.saicmobility.evcard.vlms.risk.listener.MdReturnVehicleListener;
import com.saicmobility.evcard.vlms.risk.listener.VehicleInfoInsertListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/** 
* @Desc ONS相关配置  Rocket MQ项目配置都放在此类里面
* <AUTHOR>
* @Date 2022年1月13日 上午9:20:38
*/
@Configuration
@Slf4j
public class OnsConfig {
	
	@Value("${ali.ons.name_srv_address}")
	private String ONSAddr;
	
	@Value("${ali.ons.group_id}")
	private String onsLocationGid;

	@Value("${ali.ons.access_key}")
	private String onsAccessKey;
	
	@Value("${ali.ons.secret_key}")
	private String onsSecretKey;
	
	@Value("${ali.complete.task.order.AccessKey}")
	private String aliCompleteTaskOrderAccessKey;
	
	@Value("${ali.complete.task.order.SecretKey}")
	private String aliCompleteTaskOrderSecretKey;
	
/*	@Value("${ons.complete.task.order.topic}")
	private String onsCompleteTaskOrderTopic;*/
	
	@Value("${ons.consume.data.change.topic}")
	private String onsConsumeDataChangeTopic;

	/*@Value("${ali.mdReturnVehicle.topic}")
	private String mdReturnVehicleTopic;*/
	
	/*@Autowired
    private CompleteOrderlistener completeOrderlistener;*/
    @Autowired
    private VehicleInfoInsertListener vehicleInfoInsertListener;

   /* @Autowired
    private MdReturnVehicleListener mdReturnVehicleListener;*/
    
    /** orderConsumer */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean initConsumerBean() {
    	ConsumerBean consumerBean = new ConsumerBean();
    	//设置ONS信息
    	Properties properties = new Properties();
    	properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, ONSAddr);
    	properties.setProperty(PropertyKeyConst.GROUP_ID, onsLocationGid);
    	properties.setProperty(PropertyKeyConst.AccessKey, aliCompleteTaskOrderAccessKey);
    	properties.setProperty(PropertyKeyConst.SecretKey, aliCompleteTaskOrderSecretKey);
    	properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "5");
    	
		//增加listener
		Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
		//车控事件信息监听 -- 改成定时任务
		/*Subscription completeHandlingTaskOrderSubscription = new Subscription();
		completeHandlingTaskOrderSubscription.setTopic(onsCompleteTaskOrderTopic);
		completeHandlingTaskOrderSubscription.setExpression("RETURN_VEHICLE||ORDER_RENEWAL");
		subscriptionTable.put(completeHandlingTaskOrderSubscription, completeOrderlistener);*/
		
		//数据库表操作事件监听
		Subscription dealDataBaseChangeSubscription = new Subscription();
		dealDataBaseChangeSubscription.setTopic(onsConsumeDataChangeTopic);
		dealDataBaseChangeSubscription.setExpression("vehicle_info");
		subscriptionTable.put(dealDataBaseChangeSubscription, vehicleInfoInsertListener);

		// 门店订单还车监听-- 改成定时任务
		/*Subscription mdVehicleReturnSubscription = new Subscription();
		mdVehicleReturnSubscription.setTopic(mdReturnVehicleTopic);
		mdVehicleReturnSubscription.setExpression("RETURN_VEHICLE||ORDER_RENEWAL");
		subscriptionTable.put(mdVehicleReturnSubscription, mdReturnVehicleListener);*/
		
		consumerBean.setSubscriptionTable(subscriptionTable);
    	consumerBean.setProperties(properties);
        return consumerBean;
    }
    
    @Bean(name = "producer" ,initMethod = "start" ,destroyMethod = "shutdown")
    public ProducerBean initProducer() {
    	ProducerBean producer = new ProducerBean();
		producer.setProperties(getProperties());
    	return producer;
    }
    
    @Bean(name = "producerForLocation" ,initMethod = "start" ,destroyMethod = "shutdown")
    public ProducerBean initProducerForLocation() {
    	ProducerBean producerForLocation = new ProducerBean();
    	producerForLocation.setProperties(getProperties());
    	return producerForLocation;
    }
    
    private Properties getProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, ONSAddr);
    	properties.setProperty(PropertyKeyConst.GROUP_ID, onsLocationGid);
    	properties.setProperty(PropertyKeyConst.AccessKey, onsAccessKey);
    	properties.setProperty(PropertyKeyConst.SecretKey, onsSecretKey);
        return properties;
    }
}

