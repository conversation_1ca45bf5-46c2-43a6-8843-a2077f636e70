package com.saicmobility.evcard.vlms.risk.dto.terminal;

import com.saicmobility.evcard.md.mddataproxy.api.VehicleTerminalData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class QueryTerminalInfoByVinDTORes {

    private List<VehicleTerminalDataVO> data;

    public static QueryTerminalInfoByVinDTORes toRes(List<VehicleTerminalData> dataList) {

        QueryTerminalInfoByVinDTORes queryTerminalInfoByVinDTORes = new QueryTerminalInfoByVinDTORes();
        List<VehicleTerminalDataVO> vehicleTerminalDataVO = new ArrayList<>();
        for(VehicleTerminalData data:dataList){
            VehicleTerminalDataVO vo = new VehicleTerminalDataVO();
            vo.setVin(data.getVin());
            vo.setDataSource(data.getDataSource());
            vehicleTerminalDataVO.add(vo);
        }
        queryTerminalInfoByVinDTORes.setData(vehicleTerminalDataVO);
        return queryTerminalInfoByVinDTORes;
    }
}
