package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCollectionInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskCheckCollectionInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskCheckCollectionInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, riskCheckId, collectCarPeople, collectCarType, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskCheckCollectionInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskCheckCollectionInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="risk_check_id", property="riskCheckId", jdbcType=JdbcType.BIGINT),
        @Result(column="collect_car_people", property="collectCarPeople", jdbcType=JdbcType.VARCHAR),
        @Result(column="collect_car_type", property="collectCarType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskCheckCollectionInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskCheckCollectionInfoResult")
    Optional<RiskCheckCollectionInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskCheckCollectionInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskCheckCollectionInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int insert(RiskCheckCollectionInfo row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckCollectionInfo, c ->
            c.map(riskCheckId).toProperty("riskCheckId")
            .map(collectCarPeople).toProperty("collectCarPeople")
            .map(collectCarType).toProperty("collectCarType")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int insertSelective(RiskCheckCollectionInfo row) {
        return MyBatis3Utils.insert(this::insert, row, riskCheckCollectionInfo, c ->
            c.map(riskCheckId).toPropertyWhenPresent("riskCheckId", row::getRiskCheckId)
            .map(collectCarPeople).toPropertyWhenPresent("collectCarPeople", row::getCollectCarPeople)
            .map(collectCarType).toPropertyWhenPresent("collectCarType", row::getCollectCarType)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default Optional<RiskCheckCollectionInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskCheckCollectionInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default List<RiskCheckCollectionInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskCheckCollectionInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default List<RiskCheckCollectionInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskCheckCollectionInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default Optional<RiskCheckCollectionInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskCheckCollectionInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskCheckCollectionInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(riskCheckId).equalTo(row::getRiskCheckId)
                .set(collectCarPeople).equalTo(row::getCollectCarPeople)
                .set(collectCarType).equalTo(row::getCollectCarType)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskCheckCollectionInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(riskCheckId).equalToWhenPresent(row::getRiskCheckId)
                .set(collectCarPeople).equalToWhenPresent(row::getCollectCarPeople)
                .set(collectCarType).equalToWhenPresent(row::getCollectCarType)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int updateByPrimaryKey(RiskCheckCollectionInfo row) {
        return update(c ->
            c.set(riskCheckId).equalTo(row::getRiskCheckId)
            .set(collectCarPeople).equalTo(row::getCollectCarPeople)
            .set(collectCarType).equalTo(row::getCollectCarType)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    default int updateByPrimaryKeySelective(RiskCheckCollectionInfo row) {
        return update(c ->
            c.set(riskCheckId).equalToWhenPresent(row::getRiskCheckId)
            .set(collectCarPeople).equalToWhenPresent(row::getCollectCarPeople)
            .set(collectCarType).equalToWhenPresent(row::getCollectCarType)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}