package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 恢复方式 1、人工 2、自动
 * <AUTHOR>
 * @date 2024-01-31
 */
@AllArgsConstructor
@Getter
public enum ParserForTypeEnum {
    LONG_TERM(2,"长租"),
    SHORT_TERM(3,"短租"),
    VEHICLE_MANAGEMENT_CENTER(1,"车管中心");


    Integer code;
    String value;
    public static String getValueByCode(Integer code){
        for (ParserForTypeEnum item : ParserForTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
