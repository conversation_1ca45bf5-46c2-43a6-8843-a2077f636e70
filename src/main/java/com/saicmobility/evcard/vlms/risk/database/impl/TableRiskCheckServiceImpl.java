package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckService;
import com.saicmobility.evcard.vlms.risk.dto.check.RiskCheckData;
import com.saicmobility.evcard.vlms.risk.enums.DealStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.RecoverMethodEnum;
import com.saicmobility.evcard.vlms.risk.mapper.RiskCheckExtendInfoMapper;
import com.saicmobility.evcard.vlms.risk.mapper.RiskCheckMapper;
import com.saicmobility.evcard.vlms.risk.mapper.extend.RiskCheckExtendMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckExtendInfo;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.ObjectValidUtil;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskCheckReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.QueryExpressionDSL;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.OperationVehicleDynamicSqlSupport.isDeleted;
import static com.saicmobility.evcard.vlms.risk.mapper.OperationVehicleDynamicSqlSupport.operationVehicle;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmDynamicSqlSupport.riskAlarm;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckDynamicSqlSupport.riskCheck;
import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckExtendInfoDynamicSqlSupport.riskCheckExtendInfo;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.AssetsVehicleDynamicSqlSupport.assetsVehicle;
import static com.saicmobility.evcard.vlms.risk.mapper.extend.assets.VehicleLicenseInfoDynamicSqlSupport.vehicleLicenseInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-01-18
 */
@Service
@Slf4j
public class TableRiskCheckServiceImpl implements TableRiskCheckService {

    @Resource
    private RiskCheckMapper riskCheckMapper;

    @Resource
    private RiskCheckExtendMapper riskCheckExtendMapper;

    @Resource
    private RiskCheckExtendInfoMapper riskCheckExtendInfoMapper;

    @Override
    public List<RiskCheckData> selectList(QueryRiskCheckReq req) {
        String userOrgId = req.getCurrentUser().getOrgId();
        int pageNum = req.getPageNum() == 0 ? 1 : req.getPageNum();
        int pageSize = req.getPageSize() == 0 ? 10 : req.getPageSize();
        int limitNum = (pageNum -1) * pageSize;

        Date createTimeStart = DateUtil.parse(req.getCreateTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date createTimeEnd = DateUtil.parse(req.getCreateTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        Date taskEndTimeStart = DateUtil.parse(req.getTaskEndTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date taskEndTimeEnd = DateUtil.parse(req.getTaskEndTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        Date recoveryStartTime = DateUtil.parse(req.getRecoveryTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date recoveryEndTime = DateUtil.parse(req.getRecoveryTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");

        Integer subProductLine = req.getSubProductLine();

        int recoverMethod = req.getRecoverMethod();

        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder sqlBuilder = select(
                riskCheck.id,
                riskCheck.vin,
                riskCheck.checkNo,
                riskCheck.dealStatus,
                riskCheck.createTime,
                riskCheck.createOperAccount,
                riskCheck.createOperName,
                riskCheck.taskEndTime,
                riskCheck.endOperName,
                riskCheck.endOperAccount,
                riskCheck.outControlType,
                assetsVehicle.propertyOrgId,
                assetsVehicle.propertyOrgName,
                assetsVehicle.operationOrgId,
                assetsVehicle.operationOrgName,
                assetsVehicle.vehicleRiskStatus,
                vehicleLicenseInfo.plateNo,
                operationVehicle.subProductLine
        )
                .from(riskCheck)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskCheck.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskCheck.vin))
                .leftJoin(operationVehicle).on(operationVehicle.vin, equalTo(riskCheck.vin))
                .where()
                .and(riskCheck.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(riskCheck.checkNo, isEqualToWhenPresent(req.getCheckNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.operationOrgId, isEqualToWhenPresent(req.getOperateOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(req.getOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)),
                        or(assetsVehicle.operationOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId))))
                .and(riskCheck.dealStatus, isEqualToWhenPresent(req.getDealStatus()).filter(v -> v != 0))
                .and(riskCheck.createTime, isGreaterThanOrEqualTo(createTimeStart).filter(ObjectValidUtil::isValid))
                .and(riskCheck.createTime, isLessThanOrEqualTo(createTimeEnd).filter(ObjectValidUtil::isValid))
                .and(riskCheck.taskEndTime, isGreaterThanOrEqualTo(taskEndTimeStart).filter(ObjectValidUtil::isValid))
                .and(riskCheck.taskEndTime, isLessThanOrEqualTo(taskEndTimeEnd).filter(ObjectValidUtil::isValid))
                .and(riskCheck.recoveryTime, isGreaterThanOrEqualTo(recoveryStartTime).filter(ObjectValidUtil::isValid))
                .and(riskCheck.recoveryTime, isLessThanOrEqualTo(recoveryEndTime).filter(ObjectValidUtil::isValid))
                .and(riskCheck.isDeleted, isEqualTo(0))
                .and(operationVehicle.subProductLine, isInWhenPresent(req.getSubProductLinesList()))
                .and(operationVehicle.subProductLine, isEqualToWhenPresent(subProductLine).filter(v -> v != 0));

        switch (recoverMethod){
            case 1 :
                sqlBuilder.and(riskCheck.endOperName,isIn("定时任务自动","extracme"));
                break;
            case 2 :
                sqlBuilder.and(riskCheck.endOperName,isNotIn("定时任务自动","extracme"));
                break;
            default:
                break;
        }
        SelectStatementProvider render = sqlBuilder.orderBy(riskCheck.id.descending())
                .limit(pageSize)
                .offset(limitNum)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckExtendMapper.selectRiskCheckList(render);
    }

    @Override
    public long selectTotal(QueryRiskCheckReq req) {
        String userOrgId = req.getCurrentUser().getOrgId();

        Date createTimeStart = DateUtil.parse(req.getCreateTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date createTimeEnd = DateUtil.parse(req.getCreateTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        Date taskEndTimeStart = DateUtil.parse(req.getTaskEndTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date taskEndTimeEnd = DateUtil.parse(req.getTaskEndTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        Date recoveryStartTime = DateUtil.parse(req.getRecoveryTimeStart()+" 00:00:00","yyyy-MM-dd HH:mm:ss");
        Date recoveryEndTime = DateUtil.parse(req.getRecoveryTimeEnd()+" 23:59:59","yyyy-MM-dd HH:mm:ss");
        Integer subProductLine = req.getSubProductLine();

        int recoverMethod = req.getRecoverMethod();

        QueryExpressionDSL<org.mybatis.dynamic.sql.select.SelectModel>.QueryExpressionWhereBuilder sqlBuilder = select(
                count(riskCheck.id)
        )
                .from(riskCheck)
                .leftJoin(assetsVehicle).on(assetsVehicle.vin, equalTo(riskCheck.vin))
                .leftJoin(vehicleLicenseInfo).on(vehicleLicenseInfo.vin, equalTo(riskCheck.vin))
                .leftJoin(operationVehicle).on(operationVehicle.vin, equalTo(riskCheck.vin))
                .where()
                .and(riskCheck.vin, isEqualToWhenPresent(req.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleLicenseInfo.plateNo, isEqualToWhenPresent(req.getPlateNo()).filter(StringUtils::isNotBlank))
                .and(riskCheck.checkNo, isEqualToWhenPresent(req.getCheckNo()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.operationOrgId, isEqualToWhenPresent(req.getOperateOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualToWhenPresent(req.getOrgId()).filter(StringUtils::isNotBlank))
                .and(assetsVehicle.propertyOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId)),
                        or(assetsVehicle.operationOrgId, isEqualTo(userOrgId).filter(orgId -> !"00".equals(orgId) && StringUtils.isNotBlank(orgId))))
                .and(riskCheck.dealStatus, isEqualToWhenPresent(req.getDealStatus()).filter(v -> v != 0))
                .and(riskCheck.createTime, isGreaterThanOrEqualTo(createTimeStart).filter(ObjectValidUtil::isValid))
                .and(riskCheck.createTime, isLessThanOrEqualTo(createTimeEnd).filter(ObjectValidUtil::isValid))
                .and(riskCheck.taskEndTime, isGreaterThanOrEqualTo(taskEndTimeStart).filter(ObjectValidUtil::isValid))
                .and(riskCheck.taskEndTime, isLessThanOrEqualTo(taskEndTimeEnd).filter(ObjectValidUtil::isValid))
                .and(riskCheck.recoveryTime, isGreaterThanOrEqualTo(recoveryStartTime).filter(ObjectValidUtil::isValid))
                .and(riskCheck.recoveryTime, isLessThanOrEqualTo(recoveryEndTime).filter(ObjectValidUtil::isValid))
                .and(operationVehicle.subProductLine, isEqualToWhenPresent(subProductLine).filter(v -> v != 0))
                .and(operationVehicle.subProductLine, isInWhenPresent(req.getSubProductLinesList()))
                .and(riskCheck.isDeleted, isEqualTo(0));

        switch (recoverMethod){
            case 1:
                sqlBuilder.and(riskCheck.endOperName,isIn("定时任务自动","extracme"));
                break;
            case 2:
                sqlBuilder.and(riskCheck.endOperName,isNotIn("定时任务自动","extracme"));
                break;
            default:
                break;
        }

        SelectStatementProvider render = sqlBuilder.build().render(RenderingStrategies.MYBATIS3);
        return riskCheckMapper.count(render);
    }

    @Override
    public RiskCheck selectById(long id) {
        return riskCheckMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public void update(RiskCheck riskCheck) {
        riskCheckMapper.updateByPrimaryKeySelective(riskCheck);
    }

    @Override
    public List<RiskCheck> selectBy(RiskCheck req) {
        // 条件查询
        SelectStatementProvider render = select(
                riskCheck.allColumns())
                .from(riskCheck)
                .where()
                .and(riskCheck.vin, isEqualToWhenPresent(req.getVin()))
                .and(riskCheck.checkNo, isEqualToWhenPresent(req.getCheckNo()))
                .and(riskCheck.dealStatus, isEqualToWhenPresent(req.getDealStatus()))
                .and(riskCheck.isDeleted, isEqualTo(0))
                .orderBy(riskCheck.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckMapper.selectMany(render);
    }

    @Override
    public List<RiskCheck> selectByVin(String vin, Integer dealStatus) {
        if (StringUtils.isBlank(vin)){
            return new ArrayList<>();
        }
        RiskCheck riskCheck = new RiskCheck();
        riskCheck.setVin(vin);
        riskCheck.setDealStatus(DealStatusEnum.DEAL_STATUS.getCode());
        return this.selectBy(riskCheck);
    }

    @Override
    public void insert(RiskCheck riskCheck) {
        riskCheckMapper.insertSelective(riskCheck);
    }

    @Override
    public void insertRiskCheckExtend(RiskCheckExtendInfo riskCheckExtend) {
        riskCheckExtendInfoMapper.insertSelective(riskCheckExtend);
    }

    @Override
    public RiskCheckExtendInfo selectRiskCheckExtendById(long id) {
        // 条件查询
        SelectStatementProvider render = select(
                riskCheckExtendInfo.allColumns())
                .from(riskCheckExtendInfo)
                .where()
                .and(riskCheckExtendInfo.riskCheckId, isEqualTo(id))
                .and(riskCheck.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckExtendInfoMapper.selectOne(render).orElse(null);
    }

    /**
     * 更新
     * @param riskCheckExtend
     */
    @Override
    public void updateRiskCheckExtend(RiskCheckExtendInfo riskCheckExtend) {
        riskCheckExtendInfoMapper.updateByPrimaryKeySelective(riskCheckExtend);
    }
}
