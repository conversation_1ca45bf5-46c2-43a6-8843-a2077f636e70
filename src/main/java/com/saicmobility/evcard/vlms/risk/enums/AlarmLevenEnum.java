package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车辆预警等级 1、2、3
 * <AUTHOR>
 * @date 2024-02-19
 */
@AllArgsConstructor
@Getter
public enum AlarmLevenEnum {
    ONE(1, "1级"),
    TWO(2, "2级"),
    THREE(3, "3级");

    private Integer code;
    private String value;
    public static String getValueByCode(Integer code) {
        for (AlarmLevenEnum vehicleRiskStatusEnum : AlarmLevenEnum.values()) {
            if (vehicleRiskStatusEnum.getCode() == code) {
                return vehicleRiskStatusEnum.getValue();
            }
        }
        return null;
    }

}
