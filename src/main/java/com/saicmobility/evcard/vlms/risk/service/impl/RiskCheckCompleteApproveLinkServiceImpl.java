package com.saicmobility.evcard.vlms.risk.service.impl;

import com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveLinkMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckCompleteApproveLink;
import com.saicmobility.evcard.vlms.risk.service.RiskCheckCompleteApproveLinkService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskCheckCompleteApproveLinkDynamicSqlSupport.riskCheckCompleteApproveLink;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
@Slf4j
public class RiskCheckCompleteApproveLinkServiceImpl implements RiskCheckCompleteApproveLinkService {

    @Resource
    private RiskCheckCompleteApproveLinkMapper riskCheckCompleteApproveLinkMapper;

    @Override
    public void insertSelective(RiskCheckCompleteApproveLink riskCheckCompleteApproveLink) {
        riskCheckCompleteApproveLinkMapper.insertSelective(riskCheckCompleteApproveLink);
    }

    /**
     * 根据applicationId查询
     * @param applicationId
     * @return
     */
    @Override
    public List<RiskCheckCompleteApproveLink> selectByApplicationId(String applicationId) {
        SelectStatementProvider render = select(
                riskCheckCompleteApproveLink.allColumns())
                .from(riskCheckCompleteApproveLink)
                .where()
                .and(riskCheckCompleteApproveLink.applicationId, isEqualToWhenPresent(applicationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return riskCheckCompleteApproveLinkMapper.selectMany(render);
    }
}
