package com.saicmobility.evcard.vlms.risk.utils;

import cn.hutool.core.util.IdUtil;
import com.saicmobility.evcard.vlms.risk.dto.CurrentUser;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-01-30
 */
public class BaseUtils {

    private static Pattern pattern = Pattern.compile("^[1-9]\\d*$");


    /**
     * 是否是正整数，前面不以0开头
     * @param str
     * @return
     */
    public static boolean isNumeric(String str){
        if(str == null || "".equals(str.trim())){
            return false;
        }
        str = str.trim();
        return pattern.matcher(str).matches();
    }

    public static String getFilePathSuffix(String filename, int fileType) {
        if (StringUtils.isBlank(filename)) {
            return null;
        } else {
            String fileCategoryPath = fileType == 1 ? "tmpFile" : "bizFile";
            String dayStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now());
            String midPath = dayStr.substring(0, 6) +
                    File.separator + dayStr.substring(6) + File.separator + IdUtil.objectId();
            int lastIndex = filename.lastIndexOf(".");
            String fileExt = "";
            if (lastIndex > 0) {
                fileExt = filename.substring(lastIndex);
            }
            return File.separator + fileCategoryPath + File.separator + midPath + fileExt;
        }
    }

    public static CurrentUser getCurrentUser(com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser currentUser) {
        com.saicmobility.evcard.vlms.risk.dto.CurrentUser userInfo = new com.saicmobility.evcard.vlms.risk.dto.CurrentUser();
        userInfo.setUserId(-1L);
        userInfo.setUserName(currentUser.getNickName());
        userInfo.setOrgCode(currentUser.getOrgId());
        return userInfo;
    }
}
