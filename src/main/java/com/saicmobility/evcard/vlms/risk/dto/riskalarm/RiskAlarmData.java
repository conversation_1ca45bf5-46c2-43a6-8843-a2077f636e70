package com.saicmobility.evcard.vlms.risk.dto.riskalarm;

import com.saicmobility.evcard.pb.annotation.PbFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
public class RiskAlarmData {

    private Long id;

    /**
     *   风控报警编号
     */
    private String alarmNo;

    /**
     *   车牌号
     */
    private String plateNo;

    /**
     *   车架号
     */
    private String vin;

    /**
     *   所属公司编码
     */
    private String orgId;

    /**
     *   所属公司名称
     */
    private String orgName;

    /**
     *   运营公司编码
     */
    private String operateOrgId;

    /**
     *   运营公司名称
     */
    private String operateOrgName;

    /**
     *   报警类型（1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失）
     */
    private Integer alarmType;

    private String alarmTypeDesc;

    /**
     *   报警等级
     */
    private String alarmLevel;

    private Integer alarmLevelNum;

    /**
     *   报警状态（1-报警中 2-已恢复）
     */
    private Integer alarmStatus;

    private String alarmStatusDesc;

    /**
     * 当前报警中风险数量
     */
    private Integer alarmNum;

    /**
     *   恢复方式（1-人工 2-自动）
     */
    private Integer recoverMethod;

    private String recoverMethodDesc;

    /**
     *   车辆风险状态（1-无风险 2-风险车辆 3-风控车辆 4-失控车辆）
     */
    private Integer vehicleRiskStatus;

    private String vehicleRiskStatusDesc;

    /**
     *   报警时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

    private String alarmTimeStr;

    /**
     *   最后一次定位地址
     */
    private String lastLocation;

    /**
     *   最后一次定位时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLocationTime;

    private String lastLocationTimeStr;

    /**
     *   是否暂停报警（1-是 0-否）
     */
    private Integer isStopAlarm;

    private String isStopAlarmDesc;

    /**
     *   暂停报警升级时长（天）
     */
    private Integer stopAlarmDay;

    /**
     *   报警升级暂停截止时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pauseDeadlineTime;

    private String pauseDeadlineTimeStr;

    /**
     *   恢复时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recoveryDate;

    private String recoveryDateStr;

    /**
     *   报警说明
     */
    private String alarmDesc;

    /**
     *   升级说明
     */
    private String upgradeDesc;

    /**
     *   出围栏时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exitFenceDate;

    private String exitFenceDateDesc;

    /**
     *   GPS最后一次信号时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastGpsDate;

    private String lastGpsDateDesc;

    /**
     *   删除状态 0-否,1-是
     */
    private Integer isDeleted;

    /**
     *   备注
     */
    private String miscDesc;

    /**
     *   创建时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String createTimeDesc;

    /**
     *   创建人域账号
     */
    private String createOperAccount;

    /**
     *    创建人姓名
     */
    private String createOperName;

    /**
     *   更新时间
     */
    @PbFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *   修改人域账号
     */
    private String updateOperAccount;

    /**
     * 资产状态
     */
    private Integer propertyStatus;

    private String propertyStatusDesc;

    /**
     *
     */
    private Integer productLine;

    private String productLineDesc;

    /**
     * * 子产品线
     */
    private Integer subProductLine;

    private String subProductLineDesc;

    /**
     * * 门店名称
     */
    private String storeName;

    /**
     * 恢复渠道 1-梧桐系统 2-长租系统
     */
    private Integer recoverChannel;
    private String recoverChannelDesc;
    /**
     *  报警系统 1-梧桐系统 2-长租系统
     */
    private Integer alarmSystem;
    private String alarmSystemDesc;
}
