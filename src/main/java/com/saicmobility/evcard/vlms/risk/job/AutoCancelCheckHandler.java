package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckService;
import com.saicmobility.evcard.vlms.risk.database.TableVehicleMovementDayStatisticsService;
import com.saicmobility.evcard.vlms.risk.enums.AlarmStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.DealStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.model.VehicleMovementDayStatistics;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.GetAssetsVehicleByVinReq;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.GetAssetsVehicleByVinRes;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 自动取消风控收车任务
 * <AUTHOR>
 * @date 2024-01-24
 */
@Slf4j
@JobHandler(value = "autoCancelCheck")
@Component
public class AutoCancelCheckHandler extends IJobHandler {

    @Resource
    private TableRiskCheckService tableRiskCheckService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private TableVehicleMovementDayStatisticsService tableVehicleMovementDayStatisticsService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Override
    public ReturnT<String> execute(String s) {
        log.info("自动取消风控收车任务");
        RiskCheck riskCheckQuery = new RiskCheck();
        riskCheckQuery.setDealStatus(DealStatusEnum.DEAL_STATUS.getCode());
        List<RiskCheck> riskChecks = tableRiskCheckService.selectBy(riskCheckQuery);
        if (CollectionUtil.isEmpty(riskChecks)){
            return ReturnT.SUCCESS;
        }
        for (RiskCheck riskCheck : riskChecks) {
            // 判断车辆是否已处置货已经报废
            AssetsVehicle vehicleInfoByVin = getVehicleInfoByVin(riskCheck.getVin());
            if (vehicleInfoByVin != null){
                // 资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
                int propertyStatus = vehicleInfoByVin.getPropertyStatus();
                if (propertyStatus == 3 || propertyStatus == 5){
                    // 收车任务自动取消
                    riskCheck.setDealStatus(DealStatusEnum.DEAL_RECOVER.getCode());
                    riskCheck.setTaskEndTime(new Date());
                    riskCheck.setEndOperName("定时任务自动");
                    tableRiskCheckService.update(riskCheck);
                    // 报警日志
                    saveOperateLog("车辆已处置或已报废，取消风控收车任务", riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode());
                    continue;
                }
            }
            // 全部报警都恢复
            List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(riskCheck.getVin(), AlarmStatusEnum.ALARM_STATUS.getCode(), 2);
            // 有报警中则 不自动取消
            if (CollectionUtil.isNotEmpty(riskAlarmList)) {
                continue;
            }
            // 车辆连续7天未产生异动
            if (isContinuousWarning(riskCheck.getVin())){
                // 收车任务自动取消
                riskCheck.setDealStatus(DealStatusEnum.DEAL_RECOVER.getCode());
                riskCheck.setTaskEndTime(new Date());
                riskCheck.setEndOperName("定时任务自动");
                tableRiskCheckService.update(riskCheck);

                CurrentUser currentUserr= CurrentUser.newBuilder().setUserAccount("System").setNickName("定时任务").build();
                // 报警日志
                saveOperateLog("取消风控收车任务", riskCheck.getCheckNo(), OperateTypeEnum.OPERATE_RECEIVE_VEHICLE.getCode());

                /*for (AlarmTypeEnum alarmTypeEnum : AlarmTypeEnum.values()) {
                    Long configId = riskAlarmConfigService.updateRiskAlarmConfig(riskCheck.getVin(), alarmTypeEnum.getCode(), new Date(), null, null, currentUserr);
                    saveOperateLog("自动取消风控收车任务,更新报警开始时间", configId+"", OperateTypeEnum.OPERATE_CONFIG.getCode());
                }*/
            }
        }
        return ReturnT.SUCCESS;
    }

    public void saveOperateLog(String operateContent, String relationKey, Integer operateType){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);

        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }

    /**
     * 判断是否连续7天未产生告警
     * @param vin
     */
    private boolean isContinuousWarning(String vin) {
        int num = 0;
        for(int i = 0; i < 7; i++){
            VehicleMovementDayStatistics vehicleMovementDayStatistics = tableVehicleMovementDayStatisticsService.queryDataByVinAndStatDate(vin, DateUtil.getYesterdayDateStr(i+1));
            if(null == vehicleMovementDayStatistics){
                num ++;
            }
        }
        if(num == 7){
            return true;
        }
        return false;
    }

    /**
     * 根据车架号查询车辆信息
     * @param vin
     * @return
     */
    public AssetsVehicle getVehicleInfoByVin(String vin){
        GetAssetsVehicleByVinRes res = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vin).build());
        List<AssetsVehicle> vehicles = res.getInfoList();
        if (CollectionUtil.isEmpty(vehicles)) {
            return null;
        }
        return vehicles.get(0);
    }
}
