buildscript {
    ext {
        springBootVersion = '2.2.1.RELEASE' // 迁移到此版本
        gradleVersion = '6.9.4' // 指定Gradle版本
    }
    
    // 设置Gradle兼容性
    System.setProperty('org.gradle.internal.publish.checksums.insecure', 'true')
    repositories {
        mavenLocal()    // 使用此配置可以调试 存库中不存在而本地有的jar包，如本地mv install的公共组件，本地打包的proto jar包
        maven {
            url 'http://artifactory.evcard.vip:8081/artifactory/maven-virtual/'
            credentials { username 'admin'; password 'Extracme123' }
            allowInsecureProtocol = true
        }
//        maven {
//            url 'http://artifactory.saicm.local/artifactory/maven-virtual/'
//        }
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        mavenCentral()
    }

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.6.2") // 代码检查， 生成单测覆盖率
//        classpath("gradle.plugin.com.gorylenko.gradle-git-properties:gradle-git-properties:2.0.0") // 打包时自动加入git信息
        classpath "gradle.plugin.com.thinkimi.gradle:mybatis-generator-plugin:2.4" //mybatis代码生成工具
    }
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
//apply plugin: 'com.gorylenko.gradle-git-properties'
apply plugin: "com.thinkimi.gradle.MybatisGenerator"

group 'com.saicmobility.evcard'
version '1.0.0'

repositories {
    mavenLocal()    // 使用此配置可以调试 存库中不存在而本地有的jar包，如本地mv install的公共组件，本地打包的proto jar包
    maven {
        url 'http://artifactory.evcard.vip:8081/artifactory/maven-virtual/'
        credentials { username 'admin'; password 'Extracme123' }
        allowInsecureProtocol = true
    }
    maven {
        url 'http://artifactory.saicm.local/artifactory/maven-virtual/'
        allowInsecureProtocol = true
    }
    mavenCentral()
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

sourceCompatibility = 1.8

configurations {
    mybatisGenerator
}

dependencies {
    implementation('org.springframework.boot:spring-boot-starter')
    implementation('org.springframework.boot:spring-boot-starter-data-redis')
    implementation('org.springframework.boot:spring-boot-starter-validation')
    implementation('org.springframework.boot:spring-boot-starter-aop')
    implementation('org.springframework.boot:spring-boot-starter-web')
    implementation('org.springframework.boot:spring-boot-starter-jdbc')
    implementation('com.ctrip.framework.apollo:apollo-client:1.0.0')
    implementation('org.apache.commons:commons-pool2:2.5.0')
    implementation('commons-io:commons-io:1.4')
    implementation('com.alibaba:druid-spring-boot-starter:1.1.20')
    implementation('mysql:mysql-connector-java:8.0.25')
    implementation('cn.hutool:hutool-all:5.8.0.M4')
    implementation ('com.siacmobility.evcard:bfc-common:1.0.11')

    //mybatis
    implementation('org.mybatis.spring.boot:mybatis-spring-boot-starter:2.2.2')
    implementation('org.mybatis.dynamic-sql:mybatis-dynamic-sql:1.4.0')
    implementation('com.github.pagehelper:pagehelper-spring-boot-starter:1.3.0')

    //mybatis generator
    mybatisGenerator('org.mybatis.generator:mybatis-generator-core:1.4.1')
    mybatisGenerator('com.siacmobility.evcard:bfc-common:1.0.2')

    implementation('com.saicmobility.evcard.vlms:vlmsriskservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmsmodelcenterservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmsdisposeservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmsassetsservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmsoperationservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmsadmingtw_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmsthirddependentservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.vlms:vlmspurchaseservice_protos:1.0.+')
    implementation('com.saicmobility.evcard.bfc:bfcinsurance_protos:1.0.+')
    implementation('com.saicmobility.evcard.md:mddataproxy_protos:1.0.327')
    implementation('com.saicmobility.evcard.md:mdordercenter_protos:1.0.394')
    implementation('com.saicmobility.team:hqoaauth_protos:1.0.+')
    compile('com.saicmobility.team:hqoaform_protos:1.0.+')
    implementation('krpc:spring-boot-starter:1.0.45')
    implementation('com.saicmobility.common:envconfig:1.7.49')
    implementation('com.saicmobility.common:scheduler:1.0.9')
    implementation('com.saicmobility.evcard.md:mdstoreservice_protos:1.0.112')
    implementation('com.saicmobility.evcard.md:mdworkservice_protos:1.0.110')

    compile('com.extracme.imap4server:evcard-imap4server:1.0')

//    implementation('org.springframework.boot:spring-boot-starter-webflux')
    compile 'org.springframework:spring-web'
    compile 'org.apache.httpcomponents:httpclient:4.5.2'
    compile('axis:axis:1.4')
    //compile('com.saicmobility.common:scheduler:1.0.6')

    compile('org.redisson:redisson:3.14.0')

    implementation('com.alibaba:easyexcel:3.1.1')
    implementation('io.github.biezhi:TinyPinyin:2.0.3.RELEASE')

    //aliyun OSS
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.5.0'
    implementation 'com.aliyun:aliyun-java-sdk-sts:2.1.6'

    //ons
    implementation 'org.apache.avro:avro:1.8.2'
    implementation 'com.extracme.evcard:evcard-dts-avro:1.0.0'
    implementation 'com.extracme.evcard:evcard-redis:2.7.0'
    implementation 'com.aliyun.openservices:ons-client:1.8.8.1.Final'

    compile("org.projectlombok:lombok:1.18.8")
    compileOnly('org.projectlombok:lombok:1.18.8')
    annotationProcessor('org.projectlombok:lombok:1.18.8')
    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.83'

    testImplementation('junit:junit:4.12')
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    testImplementation('kmock:kmock:1.0.36')

    compile('com.extracme.evcard:evcard-mq-bean:********')
    compile('com.extracme:extracme-framework-core:1.0.0')
}

apply from: "jacoco.gradle"

mybatisGenerator {
    verbose = true
    configFile = 'src/main/resources/mybatis/generatorConfig.xml'
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}