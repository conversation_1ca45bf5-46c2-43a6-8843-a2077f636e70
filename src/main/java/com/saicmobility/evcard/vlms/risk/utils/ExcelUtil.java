package com.saicmobility.evcard.vlms.risk.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.saicmobility.evcard.vlms.risk.excel.listener.DataListener;
import com.saicmobility.evcard.vlms.risk.excel.listener.ImportListener;
import com.saicmobility.evcard.vlms.risk.excel.support.ExcelException;
import com.saicmobility.evcard.vlms.risk.excel.support.ExcelImporter;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.util.List;

/**
 * Excel工具类
 *
 * <AUTHOR>
 * @apiNote https://www.yuque.com/easyexcel/doc/easyexcel
 */
@Slf4j
public class ExcelUtil {

    /**
     * 读取excel的所有sheet数据
     *
     * @param filePath excel文件
     * @return List<Object>
     */
    public static <T> List<T> read(String filePath, Class<T> clazz) {
        DataListener<T> dataListener = new DataListener<>();
        ExcelReaderBuilder builder = getReaderBuilder(filePath, dataListener, clazz);
        if (builder == null) {
            return null;
        }
        builder.doReadAll();
        return dataListener.getDataList();
    }

    /**
     * 读取excel的指定sheet数据
     *
     * @param filePath excel文件
     * @param sheetNo  sheet序号(从0开始)
     * @return List<Object>
     */
    public static <T> List<T> read(String filePath, int sheetNo, Class<T> clazz) {
        return read(filePath, sheetNo, 1, clazz);
    }

    /**
     * 读取excel的指定sheet数据
     *
     * @param filePath      excel文件
     * @param sheetNo       sheet序号(从0开始)
     * @param headRowNumber 表头行数
     * @return List<Object>
     */
    public static <T> List<T> read(String filePath, int sheetNo, int headRowNumber, Class<T> clazz) {
        DataListener<T> dataListener = new DataListener<>();
        ExcelReaderBuilder builder = getReaderBuilder(filePath, dataListener, clazz);
        if (builder == null) {
            return null;
        }
        builder.sheet(sheetNo).headRowNumber(headRowNumber).doRead();
        return dataListener.getDataList();
    }

    /**
     * 读取并导入数据
     *
     * @param filePath excel文件
     * @param importer 导入逻辑类
     * @param <T>      泛型
     */
    public static <T> void save(String filePath, ExcelImporter<T> importer, Class<T> clazz) {
        ImportListener<T> importListener = new ImportListener<>(importer);
        ExcelReaderBuilder builder = getReaderBuilder(filePath, importListener, clazz);
        if (builder != null) {
            builder.doReadAll();
        }
    }

    /**
     * 获取构建类
     *
     * @param filePath     excel文件
     * @param readListener excel监听类
     * @return ExcelReaderBuilder
     */
    public static <T> ExcelReaderBuilder getReaderBuilder(String filePath, ReadListener<T> readListener, Class<T> clazz) {
        if (StringUtils.isEmpty(filePath)) {
            throw new ExcelException("请上传文件!");
        }
        if ((!StringUtils.endsWithIgnoreCase(filePath, ".xls") && !StringUtils.endsWithIgnoreCase(filePath, ".xlsx"))) {
            throw new ExcelException("请上传正确的excel文件!");
        }
        InputStream inputStream;
        try {
            inputStream = FileUtil.getInputStream(filePath);
            return EasyExcel.read(inputStream, clazz, readListener);
        } catch (Throwable e) {
            log.error(StrUtil.format("tId^{} 未知异常", Trace.currentTraceId()), e);
        }
        return null;
    }

}
