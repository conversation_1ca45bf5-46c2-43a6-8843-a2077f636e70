package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskCheckCollectionInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    public static final RiskCheckCollectionInfo riskCheckCollectionInfo = new RiskCheckCollectionInfo();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.id")
    public static final SqlColumn<Long> id = riskCheckCollectionInfo.id;

    /**
     * Database Column Remarks:
     *   风控收车id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.risk_check_id")
    public static final SqlColumn<Long> riskCheckId = riskCheckCollectionInfo.riskCheckId;

    /**
     * Database Column Remarks:
     *   收车人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_people")
    public static final SqlColumn<String> collectCarPeople = riskCheckCollectionInfo.collectCarPeople;

    /**
     * Database Column Remarks:
     *   收车方式：1:内部 2:委外
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.collect_car_type")
    public static final SqlColumn<Integer> collectCarType = riskCheckCollectionInfo.collectCarType;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = riskCheckCollectionInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.misc_desc")
    public static final SqlColumn<String> miscDesc = riskCheckCollectionInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_time")
    public static final SqlColumn<Date> createTime = riskCheckCollectionInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_account")
    public static final SqlColumn<String> createOperAccount = riskCheckCollectionInfo.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.create_oper_name")
    public static final SqlColumn<String> createOperName = riskCheckCollectionInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_time")
    public static final SqlColumn<Date> updateTime = riskCheckCollectionInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = riskCheckCollectionInfo.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_collection_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = riskCheckCollectionInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_collection_info")
    public static final class RiskCheckCollectionInfo extends AliasableSqlTable<RiskCheckCollectionInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> riskCheckId = column("risk_check_id", JDBCType.BIGINT);

        public final SqlColumn<String> collectCarPeople = column("collect_car_people", JDBCType.VARCHAR);

        public final SqlColumn<Integer> collectCarType = column("collect_car_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public RiskCheckCollectionInfo() {
            super("t_risk_check_collection_info", RiskCheckCollectionInfo::new);
        }
    }
}