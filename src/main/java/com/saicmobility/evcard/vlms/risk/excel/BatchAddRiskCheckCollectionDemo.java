package com.saicmobility.evcard.vlms.risk.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BatchAddRiskCheckCollectionDemo {

    @ExcelProperty(index = 0)
    private String vin;

    @ExcelProperty(index = 1)
    private String collectCarType;

    @ExcelProperty(index = 2)
    private String collectCarPeople;

    @ExcelProperty(index = 3)
    private String miscDesc;

    private Long checkId;
}
