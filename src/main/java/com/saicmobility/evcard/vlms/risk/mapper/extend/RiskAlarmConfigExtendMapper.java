package com.saicmobility.evcard.vlms.risk.mapper.extend;

import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmConfigMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface RiskAlarmConfigExtendMapper extends RiskAlarmConfigMapper {

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SelectAlarmConfigListResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="plate_no", property="plateNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
           /* @Result(column="property_org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="operation_org_id", property="operateOrgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="operation_org_name", property="operateOrgName", jdbcType=JdbcType.VARCHAR),*/
            @Result(column="pause_day", property="pauseDay", jdbcType=JdbcType.INTEGER),
            @Result(column="alarm_type", property="alarmType", jdbcType=JdbcType.INTEGER),
            @Result(column="alarm_start_time", property="alarmStartTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="pause_deadline_time", property="pauseDeadlineTime", jdbcType=JdbcType.TIMESTAMP),
           /* @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR)*/
    })
    List<RiskAlarmConfigData> selectAlarmConfigList(SelectStatementProvider selectStatementProvider);
}
