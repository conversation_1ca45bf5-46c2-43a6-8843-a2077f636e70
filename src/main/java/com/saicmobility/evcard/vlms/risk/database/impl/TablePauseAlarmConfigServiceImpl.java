package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TablePauseAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.mapper.PauseAlarmConfigMapper;
import com.saicmobility.evcard.vlms.risk.model.PauseAlarmConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryPauseAlarmDurationConfigReq;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.PauseAlarmConfigDynamicSqlSupport.pauseAlarmConfig;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2024-01-17
 */
@Service
@Slf4j
public class TablePauseAlarmConfigServiceImpl implements TablePauseAlarmConfigService {

    @Resource
    public PauseAlarmConfigMapper pauseAlarmConfigMapper;

    @Override
    public PauseAlarmConfig selectById(Long id) {
        return pauseAlarmConfigMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public PauseAlarmConfig selectByAlarmType(Integer alarmType) {
        SelectStatementProvider provider = select(pauseAlarmConfig.allColumns())
                .from(pauseAlarmConfig)
                .where()
                .and(pauseAlarmConfig.alarmType, isEqualTo(alarmType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return pauseAlarmConfigMapper.selectOne(provider).orElse(null);
    }

    @Override
    public List<PauseAlarmConfig> selectList(QueryPauseAlarmDurationConfigReq req) {
        SelectStatementProvider render = select(
                pauseAlarmConfig.allColumns())
                .from(pauseAlarmConfig)
                .where()
                .orderBy(pauseAlarmConfig.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return pauseAlarmConfigMapper.selectMany(render);
    }


    @Override
    public void updateById(PauseAlarmConfig pauseAlarmConfig) {
        pauseAlarmConfigMapper.updateByPrimaryKeySelective(pauseAlarmConfig);
    }
}
