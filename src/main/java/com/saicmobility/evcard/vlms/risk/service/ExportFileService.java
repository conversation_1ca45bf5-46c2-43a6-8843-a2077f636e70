package com.saicmobility.evcard.vlms.risk.service;

/**
 * <AUTHOR>
 * @date 2024-02-25
 */
public interface ExportFileService {

    /**
     * 开始导出
     * @param fileSource 文件来源
     * @param fileName 文件名
     * @param filePath 文件路径
     * @param operator 操作人
     * @param userAccount 域账号
     * @param orgId 登录公司
     * @param orgName 登录公司名称
     * @return
     */
    Integer startExport(Integer fileSource, String fileName, String filePath, String operator,String userAccount,
                        String orgId, String orgName);

    /**
     * 导出成功
     * @param fileId 导出文件ID
     * @param operator 操作人
     */
    void exportSuccess(Integer fileId, String operator);

    /**
     * 导出失败
     * @param fileId 导出文件ID
     * @param reason 导出失败原因
     * @param operator 操作人
     */
    void exportFail(Integer fileId, String reason, String operator);
}
