package com.saicmobility.evcard.vlms.risk.utils;

import cn.hutool.core.io.FileUtil;
import org.springframework.util.StringUtils;

import java.net.FileNameMap;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;

public class MimeTypeUtils {

    private static final Map<String, String> fileExtensionMap;

    static {
        fileExtensionMap = new HashMap<String, String>();
        // MS Office
        fileExtensionMap.put("doc", "application/msword");
        fileExtensionMap.put("dot", "application/msword");
        fileExtensionMap.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        fileExtensionMap.put("dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template");
        fileExtensionMap.put("docm", "application/vnd.ms-word.document.macroEnabled.12");
        fileExtensionMap.put("dotm", "application/vnd.ms-word.template.macroEnabled.12");
        fileExtensionMap.put("xls", "application/vnd.ms-excel");
        fileExtensionMap.put("xlt", "application/vnd.ms-excel");
        fileExtensionMap.put("xla", "application/vnd.ms-excel");
        fileExtensionMap.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        fileExtensionMap.put("xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
        fileExtensionMap.put("xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12");
        fileExtensionMap.put("xltm", "application/vnd.ms-excel.template.macroEnabled.12");
        fileExtensionMap.put("xlam", "application/vnd.ms-excel.addin.macroEnabled.12");
        fileExtensionMap.put("xlsb", "application/vnd.ms-excel.sheet.binary.macroEnabled.12");
        fileExtensionMap.put("ppt", "application/vnd.ms-powerpoint");
        fileExtensionMap.put("pot", "application/vnd.ms-powerpoint");
        fileExtensionMap.put("pps", "application/vnd.ms-powerpoint");
        fileExtensionMap.put("ppa", "application/vnd.ms-powerpoint");
        fileExtensionMap.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        fileExtensionMap.put("potx", "application/vnd.openxmlformats-officedocument.presentationml.template");
        fileExtensionMap.put("ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow");
        fileExtensionMap.put("ppam", "application/vnd.ms-powerpoint.addin.macroEnabled.12");
        fileExtensionMap.put("pptm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12");
        fileExtensionMap.put("potm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12");
        fileExtensionMap.put("ppsm", "application/vnd.ms-powerpoint.slideshow.macroEnabled.12");
        // Open Office
        fileExtensionMap.put("odt", "application/vnd.oasis.opendocument.text");
        fileExtensionMap.put("ott", "application/vnd.oasis.opendocument.text-template");
        fileExtensionMap.put("oth", "application/vnd.oasis.opendocument.text-web");
        fileExtensionMap.put("odm", "application/vnd.oasis.opendocument.text-master");
        fileExtensionMap.put("odg", "application/vnd.oasis.opendocument.graphics");
        fileExtensionMap.put("otg", "application/vnd.oasis.opendocument.graphics-template");
        fileExtensionMap.put("odp", "application/vnd.oasis.opendocument.presentation");
        fileExtensionMap.put("otp", "application/vnd.oasis.opendocument.presentation-template");
        fileExtensionMap.put("ods", "application/vnd.oasis.opendocument.spreadsheet");
        fileExtensionMap.put("ots", "application/vnd.oasis.opendocument.spreadsheet-template");
        fileExtensionMap.put("odc", "application/vnd.oasis.opendocument.chart");
        fileExtensionMap.put("odf", "application/vnd.oasis.opendocument.formula");
        fileExtensionMap.put("odb", "application/vnd.oasis.opendocument.database");
        fileExtensionMap.put("odi", "application/vnd.oasis.opendocument.image");
        fileExtensionMap.put("oxt", "application/vnd.openofficeorg.extension");

        // 图片类文件
        fileExtensionMap.put("jpg", "image/jpeg");
        fileExtensionMap.put("jpeg", "image/jpeg");
        fileExtensionMap.put("png", "image/png");
        fileExtensionMap.put("gif", "image/gif");
        fileExtensionMap.put("bmp", "image/bmp");
        fileExtensionMap.put("webp", "image/webp");
        fileExtensionMap.put("tiff", "image/tiff");
        fileExtensionMap.put("tif", "image/tiff");
        fileExtensionMap.put("svg", "image/svg+xml");
        fileExtensionMap.put("ico", "image/x-icon");
        fileExtensionMap.put("psd", "image/vnd.adobe.photoshop");
        fileExtensionMap.put("ai", "application/postscript");
        fileExtensionMap.put("eps", "application/postscript");
        fileExtensionMap.put("raw", "image/x-dcraw"); // RAW 图像文件

        // 视频类文件
        fileExtensionMap.put("mp4", "video/mp4");
        fileExtensionMap.put("mpeg", "video/mpeg");
        fileExtensionMap.put("mpg", "video/mpeg");
        fileExtensionMap.put("avi", "video/x-msvideo");
        fileExtensionMap.put("mov", "video/quicktime");
        fileExtensionMap.put("wmv", "video/x-ms-wmv");
        fileExtensionMap.put("flv", "video/x-flv");
        fileExtensionMap.put("mkv", "video/x-matroska");
        fileExtensionMap.put("webm", "video/webm");
        fileExtensionMap.put("3gp", "video/3gpp");
        fileExtensionMap.put("3g2", "video/3gpp2");
        fileExtensionMap.put("m4v", "video/x-m4v");
        fileExtensionMap.put("ogv", "video/ogg");
        fileExtensionMap.put("vob", "video/x-ms-vob");
        fileExtensionMap.put("rm", "application/vnd.rn-realmedia");
        fileExtensionMap.put("rmvb", "application/vnd.rn-realmedia-vbr");
        fileExtensionMap.put("asf", "video/x-ms-asf");
        fileExtensionMap.put("divx", "video/divx");
        fileExtensionMap.put("m2ts", "video/mp2t"); // Blu-ray 视频文件
        fileExtensionMap.put("ts", "video/mp2t"); // MPEG 传输流
        fileExtensionMap.put("mxf", "application/mxf"); // 专业视频格式

        // 压缩包文件类型
        fileExtensionMap.put("zip", "application/zip");
        fileExtensionMap.put("rar", "application/x-rar-compressed");
        fileExtensionMap.put("7z", "application/x-7z-compressed");
        fileExtensionMap.put("tar", "application/x-tar");
        fileExtensionMap.put("gz", "application/gzip");
        fileExtensionMap.put("bz2", "application/x-bzip2");
        fileExtensionMap.put("xz", "application/x-xz");
        fileExtensionMap.put("iso", "application/x-iso9660-image"); // 光盘镜像文件
        fileExtensionMap.put("dmg", "application/x-apple-diskimage"); // macOS 磁盘镜像

        // 其他常见媒体文件
        fileExtensionMap.put("swf", "application/x-shockwave-flash"); // Flash 动画
        fileExtensionMap.put("f4v", "video/x-f4v"); // Adobe Flash 视频
        fileExtensionMap.put("mts", "video/mp2t"); // AVCHD 视频文件
        fileExtensionMap.put("hevc", "video/hevc"); // 高效视频编码 (H.265)
        fileExtensionMap.put("h264", "video/h264"); // H.264 编码视频
    }

    public static String getContentTypeByFileName(String fileName) {
        FileNameMap mimeTypes = URLConnection.getFileNameMap();
        String contentType = mimeTypes.getContentTypeFor(fileName);
        if (!StringUtils.hasText(contentType)) {
            String extension = FileUtil.extName(fileName);
            contentType = fileExtensionMap.get(extension);
        }
        return contentType;
    }
}
