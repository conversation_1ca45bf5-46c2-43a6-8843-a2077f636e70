package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.dto.riskalarm.RiskAlarmData;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmListReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface TableRiskAlarmService {

    /**
     * 查询风险告警列表
     * @param req
     * @return
     */
    List<RiskAlarmData> selectList(QueryRiskAlarmListReq req);

    /**
     * 查询总条数
     * @param req
     * @return
     */
    long selectTotal(QueryRiskAlarmListReq req);

    /**
     * 查询风险告警
     * @param riskAlarm
     * @return
     */
    RiskAlarm queryRiskAlarm(RiskAlarm riskAlarm);

    /**
     * 查询风险告警
     * @param vin
     * @param alarmType
     * @param alarmStatus
     * @return
     */
    RiskAlarm queryRiskAlarm(String vin, Integer alarmType, Integer alarmStatus);

    /**
     * 查询风险告警
     * @param alarmNo
     * @return
     */
    RiskAlarm queryRiskAlarmByAlarmNo(String alarmNo);

    /**
     * 根据id风险告警
     * @param id
     * @return
     */
    RiskAlarm selectById(Long id);

    /**
     * 新增风险告警
     * @param riskAlarm
     */
    void save(RiskAlarm riskAlarm);

    /**
     * 根据车辆查询报警中的风险类型
     * @param vin
     * @return
     */
    List<RiskAlarm> selectAlarmVin(String vin, Integer alarmStatus, Integer queryType);

    /**
     * 更新风险告警
     * @param riskAlarm
     */
    void update(RiskAlarm riskAlarm);

    /**
     * 根据车架号和报警类型查询风险告警
     * @param vin
     * @param alarmType
     * @return
     */
    RiskAlarm queryDataByVinAndType(String vin, Integer alarmType);


    /**
     * 根据车辆查询报警中的风险类型
     * @param alarmType
     * @return
     */
    List<RiskAlarm> queryDataByType(Integer alarmType);

    /**
     * 查询全部报警
     * @return
     */
    List<RiskAlarm> queryAllDataByType();

    /**
     * 更新风险告警
     * @param riskAlarm
     */
    void updateByAlarmNo(RiskAlarm riskAlarm);

}
