package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.md.mdordercenter.api.CarRentalContractInfo;
import com.saicmobility.evcard.md.mdordercenter.api.MdOrderCenter;
import com.saicmobility.evcard.md.mdordercenter.api.SearchCarRentalContractInfoListForWtReq;
import com.saicmobility.evcard.md.mdordercenter.api.SearchCarRentalContractInfoListForWtRes;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.service.RiskCheckService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.*;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 定时升级报警等级
 * <AUTHOR>
 * @date 2024-01-24
 */
@Slf4j
@JobHandler(value = "autoUpgradeRisk")
@Component
public class AutoUpgradeRiskHandler extends IJobHandler {

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    public OutDescIdUtil outDescIdUtil;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private MdOrderCenter mdOrderCenter;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private RiskCheckService riskCheckService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;

    private static final int LONG_TERM_PRODUCT_LINE_DAYS = 7;
    private static final int SHORT_TERM_PRODUCT_LINE_DAYS = 3;
    private static final int ONE_DAYS = 1;

    
    @Override
    public ReturnT<String> execute(String vin) throws Exception {
        log.info("定时升级报警等级");
        Date now = new Date();

        // 年检临期 和 保险临期
        doOperateVehicle(now, vin);
        // 非处置运营阶段车辆
        doNotOperateVehicle(now, vin);
        // 逾期未还车 长租或短租
        doCarNotReturn(now, vin);

        log.info("定时升级报警等级结束");
        return ReturnT.SUCCESS;
    }

    public void doNotOperateVehicle(Date now, String vin) {

      boolean doFlag = true;
      int pageNum = 1;
      int limit = 5000;
      // 固资运营阶段
      do {
          List<SearchVehicleFileList> searchVehicleFiles = subAssetsService.searchVehicleFileList(pageNum, limit, 0, vin);
          if (CollectionUtil.isEmpty(searchVehicleFiles)){
              doFlag = false;
          }
          for (SearchVehicleFileList assetsVehicle : searchVehicleFiles) {
              if (assetsVehicle.getVehiclePhase() == VehiclePhaseEnum.OPERATING.getCode()){
                  continue;
              }
              // 判断车辆是否是已处置、报废、已处置未过户（交付状态未交付）
              if (RiskUtils.isIgnoreAlarm(assetsVehicle.getPropertyStatus(), assetsVehicle.getDeliveryStatus())){
                  continue;
              }
              // 年检到期时间
              String inspectionExpireDateStr = assetsVehicle.getInspectionExpireDate();
              if (StringUtils.isBlank(inspectionExpireDateStr)){
                  continue;
              }

              try {
                  // 判断车辆是否在白名单中
                  boolean isWhiteList = riskCommonService.isWhiteList(assetsVehicle.getVin(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
                  if (isWhiteList) {
                      continue;
                  }
                  boolean hasAlarm = false;
                  // 判断是否有进行中的报警
                  RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(assetsVehicle.getVin(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
                  if (riskAlarm != null){
                      hasAlarm = true;
                  }
                  // 年检到期时间
                String expireDateStr = inspectionExpireDateStr.split(" ")[0];
                // 数据库存的是年月日，需要加一天
                LocalDate expireDate = LocalDate.parse(expireDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);

                // 开始时间
                RiskAlarmConfig riskAlarmConfig = getAlarmStartTime(assetsVehicle.getVin(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
                if (riskAlarmConfig == null){
                    continue;
                }
                LocalDate startTime = formatDateToLocalDate(riskAlarmConfig.getAlarmStartTime());
                if (startTime.isAfter(expireDate)){
                    expireDate = startTime;
                }
                boolean isOverPause = false;
                Date pauseDeadlineTime = riskAlarmConfig.getPauseDeadlineTime();
                if (pauseDeadlineTime != null){
                    if (pauseDeadlineTime.compareTo(now) >= 0){
                        continue;
                    }else {
                        isOverPause = true;
                    }
                }
                Period difference = Period.between(expireDate, LocalDate.now());
                // 分别获取年数、月数和天数
                int years = difference.getYears();
                int months = difference.getMonths();
                int days = difference.getDays();

                // 存在报警，判断是否可以升级
                if (hasAlarm){
                    // 报警开始时间
                    //LocalDate alarmTime = formatDateToLocalDate(riskAlarm.getAlarmTime());
                    Date alarmTime = riskAlarm.getAlarmTime();
                    long overDay = DateUtil.daysBetween(now, alarmTime);
                    if (isOverPause && riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                        // 暂停截止日期到后逻辑
                        //  超过2年零7天则立即升级二级报警
                        if (years >= 2 && ((months == 0 && days > 7) || months > 0)) {
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                        }else if (years == 2 && months == 0 && days <= 7){
                            // 否则升级一级报警
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.ONE.getCode(), "", 0);
                        }
                        // 恢复将暂停状态设为否
                        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                        tableRiskAlarmService.update(riskAlarm);

                    } else if (overDay >= LONG_TERM_PRODUCT_LINE_DAYS && riskAlarm.getAlarmLevel() == AlarmLevenEnum.ONE.getCode()) {
                        // 是否升级到二级
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                    }
                }else {
                    // 不存在报警， 超过2年零7天则立即升级二级报警
                    if (years >= 2 && ((months == 0 && days > 7) || months > 0)) {
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                    }else if (years == 2 && months == 0 && days <= 7){
                        // 否则升级一级报警
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.ONE.getCode(), "", 0);
                    }
                }
              }catch (Exception e){
                  log.error("车辆处理异常：{}", assetsVehicle.getVin());
                  continue;
              }
          }
          pageNum ++;
          if (searchVehicleFiles.size() < 5000){
             doFlag = false;
          }
      }while (doFlag);
    }

    /**
     * 逾期未还车逻辑处理
     */
    public void doCarNotReturn(Date now, String vin) {
        // C订单查询
        doCOrder(now, vin);

        // 同步门店订单\直连平台订单
        doMcOrder(now, vin);

        // 长租订单
        doLongRentAlarm(now, vin);

    }

    /**
     * 门店订单查询
     * @param now
     */
    private void doMcOrder(Date now, String vin) {

        //  3：已取车 4：还车中
        List<Integer> contractStatusList = Arrays.asList(3, 4);

        for (Integer contractStatus : contractStatusList) {

        SearchCarRentalContractInfoListForWtReq req = SearchCarRentalContractInfoListForWtReq
                .newBuilder()
                .setContractStatus(contractStatus)
                .setCurrentTime(DateUtil.dateToString(now, DateUtil.DATE_TYPE1))
                .setVin(vin)
                .build();
        SearchCarRentalContractInfoListForWtRes searchCarRentalContractInfoListForWtRes = mdOrderCenter.searchCarRentalContractInfoListForWt(req);
        if (searchCarRentalContractInfoListForWtRes != null && searchCarRentalContractInfoListForWtRes.getInfoList() != null) {
            List<CarRentalContractInfo> infoList = searchCarRentalContractInfoListForWtRes.getInfoList();
            for (CarRentalContractInfo carRentalContractInfo : infoList) {
                try {
                    // 开始时间
                    RiskAlarmConfig alarmConfig = getAlarmStartTime(carRentalContractInfo.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                    if (alarmConfig == null){
                        continue;
                    }
                    // 判断车辆是否在白名单中
                    boolean isWhiteList = riskCommonService.isWhiteList(carRentalContractInfo.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                    if (isWhiteList){
                        continue;
                    }
                    boolean hasAlarm = false;
                    // 查询当前报警中的风控报警
                    RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(carRentalContractInfo.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                    if (riskAlarm != null){
                        // 当前有报警
                        hasAlarm = true;
                    }

                    List<SearchVehicleFileList> searchVehicleFileLists = subAssetsService.searchVehicleFileList(1, 10, 0, carRentalContractInfo.getVin());
                    SearchVehicleFileList assetsVehicle = searchVehicleFileLists.get(0);
                    // 判断车辆是否是已处置、报废、已处置未过户（交付状态未交付）
                    if (RiskUtils.isIgnoreAlarm(assetsVehicle.getPropertyStatus(), assetsVehicle.getDeliveryStatus())){
                        continue;
                    }

                    // 最晚还车时间
                    //LocalDate expireDate = LocalDate.parse(carRentalContractInfo.getLastReturnDateTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    // 最晚还车时间
                    Date expireDate = DateUtil.parse(carRentalContractInfo.getLastReturnDateTime(), "yyyy-MM-dd HH:mm:ss");

                    Date alarmStartTime = alarmConfig.getAlarmStartTime();
                    //LocalDate startTime = formatDateToLocalDate(alarmConfig.getAlarmStartTime());
                   /* if (startTime.isAfter(expireDate)){
                        expireDate = startTime;
                    }*/
                    if (alarmStartTime.after(expireDate)){
                        expireDate = alarmStartTime;
                    }
                    boolean isOverPause = false;
                    Date pauseDeadlineTime = alarmConfig.getPauseDeadlineTime();
                    if (pauseDeadlineTime != null){
                        if (pauseDeadlineTime.compareTo(now) >= 0){
                            continue;
                        }else {
                            isOverPause = true;
                        }
                    }
                    Integer orderType;
                    if (carRentalContractInfo.getContractId().startsWith("MC")){
                        orderType = OrderTypeEnum.MD_ORDER.getCode();
                    }else {
                        orderType = OrderTypeEnum.PARTNER_ORDER.getCode();
                    }

                    //long diffDay = LocalDate.now().toEpochDay() - expireDate.toEpochDay();
                    long diffDay = DateUtil.daysBetween(now, expireDate);
                    if (hasAlarm){
                        // 判断是否需要升级
                        /*if (riskAlarm.getAlarmLevel() > AlarmLevenEnum.ONE.getCode()){
                            continue;
                        }*/
                        //LocalDate alarmStartDate = formatDateToLocalDate(riskAlarm.getAlarmTime());
                        Date alarmTime = riskAlarm.getAlarmTime();
                        //long overDay = LocalDate.now().toEpochDay() - alarmStartDate.toEpochDay();
                        long overDay = DateUtil.daysBetween(now, alarmTime);
                        log.info("车架号:【{}】, 报警类型：【{}】,当前时间与报警时间相差天数:【{}】", assetsVehicle.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getValue(), overDay);
                        if (isOverPause && riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                            log.info("车架号:【{}】, 报警类型：【{}】当前时间超过暂停截止日期,当前时间与计划还车时间相差天数:【{}】", assetsVehicle.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getValue(), diffDay);
                            // 当前无报警处理
                            if (diffDay >= ONE_DAYS && diffDay <= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                                autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.ONE.getCode(), carRentalContractInfo.getContractId(), orderType);
                            }else if (diffDay >= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                                autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), carRentalContractInfo.getContractId(), orderType);
                            }
                            // 暂停状态
                            riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                            tableRiskAlarmService.update(riskAlarm);

                        }else if (overDay >=  LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), carRentalContractInfo.getContractId(), orderType);
                        }

                    }else {
                        log.info("车架号:【{}】, 报警类型：【{}】,当前时间与计划还车时间相差天数:【{}】", assetsVehicle.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getValue(), diffDay);
                        // 当前无报警处理
                        if (diffDay >= ONE_DAYS && diffDay <= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.ONE.getCode(), carRentalContractInfo.getContractId(), orderType);
                        }else if (diffDay >= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), carRentalContractInfo.getContractId(), orderType);
                        }
                    }
                }catch (Exception e){
                    log.error("系统报错:车架号:{}:{}", carRentalContractInfo.getVin(), e);
                }
            }
        }
        }
    }

    /**
     * 不报警处理
     * 资产状态 是 已处置、报废、已处置未过户 不报警
     * @param assetsVehicle
     * @return
     */
 /*   private boolean isIgnoreAlarm(SearchVehicleFileList assetsVehicle) {
        boolean flag = false;
        int propertyStatus = assetsVehicle.getPropertyStatus();
        //int deliveryStatus = assetsVehicle.getDeliveryStatus();
        if (propertyStatus == PropertyStatusEnums.BF.getCode() || propertyStatus == PropertyStatusEnums.YCZ.getCode() || propertyStatus == PropertyStatusEnums.WGH.getCode()){
            flag =  true;
        }
        return flag;
    }*/

    /**
     * 长租订单逻辑处理
     */
    public void doLongRentAlarm(Date now, String vin) {

        QueryNotReturnLongRentContractReq req = QueryNotReturnLongRentContractReq.newBuilder().setCurrentDay(DateUtil.format(now, "yyyy-MM-dd")).setVin(vin).build();
        QueryNotReturnLongRentContractRes rentContractRes = mdDataProxy.queryNotReturnLongRentContract(req);
        List<NotReturnLongRentContract> infoList = rentContractRes.getInfoList();
        if (CollectionUtil.isEmpty(infoList)){
            return;
        }
        for (NotReturnLongRentContract longRentContract : infoList) {
            try {
                // 开始时间
                RiskAlarmConfig alarmConfig = getAlarmStartTime(longRentContract.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                if (alarmConfig == null) {
                    continue;
                }

                // 判断车辆是否在白名单中
                boolean isWhiteList = riskCommonService.isWhiteList(longRentContract.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                if (isWhiteList){
                    continue;
                }

                boolean hasAlarm = false;
                // 查询当前报警中的风控报警
                RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(longRentContract.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                if (riskAlarm != null){
                    // 当前有报警
                    hasAlarm = true;
                }
                List<SearchVehicleFileList> searchVehicleFileLists = subAssetsService.searchVehicleFileList(1, 10, 0, longRentContract.getVin());
                if (CollectionUtil.isEmpty(searchVehicleFileLists)){
                    continue;
                }
                SearchVehicleFileList assetsVehicle = searchVehicleFileLists.get(0);
                // 判断车辆是否是已处置、报废、已处置未过户（交付状态未交付）
                if (RiskUtils.isIgnoreAlarm(assetsVehicle.getPropertyStatus(), assetsVehicle.getDeliveryStatus())){
                    continue;
                }

                // 数据库存的是年月日，需要加一天
                LocalDate expireDate = LocalDate.parse(longRentContract.getContractEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);

                LocalDate startTime = formatDateToLocalDate(alarmConfig.getAlarmStartTime());
                if (startTime.isAfter(expireDate)){
                    expireDate = startTime;
                }
                // 暂停状态
                boolean isOverPause = false;
                Date pauseDeadlineTime = alarmConfig.getPauseDeadlineTime();
                if (pauseDeadlineTime != null){
                    if (pauseDeadlineTime.compareTo(now) >= 0){
                        continue;
                    }else {
                        isOverPause = true;
                    }
                }

                long diffDay = LocalDate.now().toEpochDay() - expireDate.toEpochDay();

                if (hasAlarm){
                    // 判断是否需要升级
                    /*if (riskAlarm.getAlarmLevel() > AlarmLevenEnum.ONE.getCode()){
                        log.info("等级大于1级无需升级");
                        continue;
                    }*/
                    // LocalDate alarmStartDate = formatDateToLocalDate(riskAlarm.getAlarmTime());
                    // long overDay =  LocalDate.now().toEpochDay() - alarmStartDate.toEpochDay();
                    long overDay =  DateUtil.daysBetween(now, riskAlarm.getAlarmTime());
                    log.info("车架号:【{}】, 报警类型：【{}】,当前时间与报警时间相差天数:【{}】", assetsVehicle.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getValue(), diffDay);
                    if (isOverPause && riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                        log.info("车架号:【{}】, 报警类型：【{}】当前时间大于暂停截止日期,当前时间与计划还车时间相差天数:【{}】", assetsVehicle.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getValue(), diffDay);
                        // 当前无报警处理
                        if (diffDay >= SHORT_TERM_PRODUCT_LINE_DAYS && diffDay <= SHORT_TERM_PRODUCT_LINE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.ONE.getCode(), longRentContract.getContractId(), OrderTypeEnum.LONG_RENT_ORDER.getCode());
                        }else if (diffDay >= SHORT_TERM_PRODUCT_LINE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), longRentContract.getContractId(), OrderTypeEnum.LONG_RENT_ORDER.getCode());
                        }
                        // 暂停状态
                        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                        tableRiskAlarmService.update(riskAlarm);
                    }else if (overDay >= LONG_TERM_PRODUCT_LINE_DAYS  && riskAlarm.getAlarmLevel() == AlarmLevenEnum.ONE.getCode()){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), longRentContract.getContractId(), OrderTypeEnum.LONG_RENT_ORDER.getCode());
                    }
                }else {
                    log.info("车架号:【{}】, 报警类型：【{}】当前时间与计划还车时间相差天数:【{}】", assetsVehicle.getVin(), AlarmTypeEnum.OVERDUE_NOT_RETURN.getValue(), diffDay);
                    // 当前无报警处理
                    if (diffDay >= SHORT_TERM_PRODUCT_LINE_DAYS && diffDay <= SHORT_TERM_PRODUCT_LINE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.ONE.getCode(), longRentContract.getContractId(), OrderTypeEnum.LONG_RENT_ORDER.getCode());
                    }else if (diffDay >= SHORT_TERM_PRODUCT_LINE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), longRentContract.getContractId(), OrderTypeEnum.LONG_RENT_ORDER.getCode());
                    }
                }
            }catch (Exception e){
                log.error("长租订单逻辑处理异常:{}", longRentContract.getContractId());
                continue;
            }
        }
    }

    /**
     * C 订单逻辑处理
     * @param now
     */
    private void doCOrder(Date now, String vinCode) {

        SearchRiskOrderInfoListReq build = SearchRiskOrderInfoListReq.newBuilder()
                .setContractStatus(3)
                .setCurrentTime(DateUtil.dateToString(now, DateUtil.DATE_TYPE4))
                .setVin(vinCode)
                .build();
        SearchRiskOrderInfoListRes searchRiskOrderInfoListRes = mdDataProxy.searchRiskOrderInfoList(build);

        List<SearchRiskOrderInfoListRes.SearchCarRentalContractDTO> infoList = searchRiskOrderInfoListRes.getInfoList();
        if (CollectionUtil.isEmpty(infoList)){
            return;
        }

        for (SearchRiskOrderInfoListRes.SearchCarRentalContractDTO searchCarRentalContractDTO : infoList) {

            try {
                String vin = searchCarRentalContractDTO.getVin();
                // 开始时间
                RiskAlarmConfig alarmConfig = getAlarmStartTime(vin, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                if (alarmConfig == null){
                    continue;
                }

                // 判断车辆是否在白名单中
                boolean isWhiteList = riskCommonService.isWhiteList(vin, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                if (isWhiteList){
                    continue;
                }
                boolean hasAlarm = false;
                // 查询当前报警中的风控报警
                RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode());
                if (riskAlarm != null){
                    // 当前有报警
                    hasAlarm = true;
                }

                List<SearchVehicleFileList> searchVehicleFileLists = subAssetsService.searchVehicleFileList(1, 10, 0, searchCarRentalContractDTO.getVin());
                SearchVehicleFileList assetsVehicle = searchVehicleFileLists.get(0);
                // 判断车辆是否是已处置、报废、已处置未过户（交付状态已交付）
                if (RiskUtils.isIgnoreAlarm(assetsVehicle.getPropertyStatus(), assetsVehicle.getDeliveryStatus())){
                    continue;
                }
                // 最晚还车时间
                //LocalDate expireDate = formatDateToLocalDate(searchCarRentalContractDTO.getLatestEndTime());
                Date expireDate = DateUtil.getDateFromStr(searchCarRentalContractDTO.getLatestEndTime(), "yyyyMMddHHmmss");
                // 报警开始时间
                //LocalDate startTime = formatDateToLocalDate(alarmConfig.getAlarmStartTime());
                Date alarmStartTime = alarmConfig.getAlarmStartTime();
                if (alarmStartTime.after(expireDate)){
                    expireDate = alarmStartTime;
                }
                // 暂停状态
                boolean isOverPause = false;
                Date pauseDeadlineTime = alarmConfig.getPauseDeadlineTime();
                if (pauseDeadlineTime != null){
                    if (pauseDeadlineTime.compareTo(now) >= 0){
                        continue;
                    }else {
                        isOverPause = true;
                    }
                }

                //long diffDay = LocalDate.now().toEpochDay() - expireDate.toEpochDay();
                long diffDay = DateUtil.daysBetween(now, expireDate);
                if (hasAlarm){
                    // 判断是否需要升级
                    /*if (riskAlarm.getAlarmLevel() > AlarmLevenEnum.ONE.getCode()){
                        continue;
                    }*/
                    //LocalDate alarmStartDate = formatDateToLocalDate(riskAlarm.getAlarmTime());
                    Date alarmTime = riskAlarm.getAlarmTime();
                    //long overDay = LocalDate.now().toEpochDay() - alarmStartDate.toEpochDay();
                    long overDay = DateUtil.daysBetween(now, alarmTime);

                    LocalDate localDate = formatDateToLocalDate(riskAlarm.getAlarmTime());
                   // diffDay = LocalDate.now().toEpochDay() - localDate.toEpochDay();
                    diffDay = DateUtil.daysBetween(now, alarmTime);
                    log.info("当前时间与报警时间相差天数:【{}】", overDay);
                    // 超过暂停时间
                    if (isOverPause && riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                        log.info("当前时间超过暂停截止日期， 当前时间与计划还车时间相差天数:【{}】", diffDay);
                        // 当前无报警处理
                        if (diffDay >= ONE_DAYS && diffDay <= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.ONE.getCode(), searchCarRentalContractDTO.getContractId(), OrderTypeEnum.SAIC_ORDER.getCode());
                        }else if (diffDay >= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                            autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), searchCarRentalContractDTO.getContractId(), OrderTypeEnum.SAIC_ORDER.getCode());
                        }
                        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                        tableRiskAlarmService.update(riskAlarm);
                    }else if (overDay >= LONG_TERM_PRODUCT_LINE_DAYS  && riskAlarm.getAlarmLevel() == AlarmLevenEnum.ONE.getCode()){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), searchCarRentalContractDTO.getContractId(), OrderTypeEnum.SAIC_ORDER.getCode());
                    }
                }else {
                    log.info("当前时间与最晚还车时间相差天数:【{}】", diffDay);
                    // 当前无报警处理
                    if (diffDay >= ONE_DAYS && diffDay <= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.ONE.getCode(), searchCarRentalContractDTO.getContractId(), OrderTypeEnum.SAIC_ORDER.getCode());
                    }else if (diffDay >= ONE_DAYS + LONG_TERM_PRODUCT_LINE_DAYS){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.OVERDUE_NOT_RETURN.getCode(), AlarmLevenEnum.TWO.getCode(), searchCarRentalContractDTO.getContractId(), OrderTypeEnum.SAIC_ORDER.getCode());
                    }
                }
            }catch (Exception e){
                log.info("数据处理异常,合同号:{}， 车架号:{}", searchCarRentalContractDTO.getContractId(), searchCarRentalContractDTO.getVin());
                continue;
            }
        }
    }

    /**
     * 固资运营阶段
     */
    public void doOperateVehicle(Date now, String vin) {
        boolean doFlag = true;
        int pageNum = 1;
        int limit = 5000;
        // 固资运营阶段
        do {
            List<SearchVehicleFileList> searchVehicleFiles = subAssetsService.searchVehicleFileList(pageNum, limit, VehiclePhaseEnum.OPERATING.getCode(), vin);
            if (CollectionUtil.isEmpty(searchVehicleFiles)){
                doFlag = false;
            }
            for (SearchVehicleFileList assetsVehicle : searchVehicleFiles) {
                // 判断车辆是否是已处置、报废、已处置未过户（交付状态已交付）
                if (RiskUtils.isIgnoreAlarm(assetsVehicle.getPropertyStatus(), assetsVehicle.getDeliveryStatus())){
                    continue;
                }
                try {
                    // 交强险到期时间，和报警开始时间比较，（交强险到期时间，报警开始时间，较大的时间开始计算）交强险效期不足7天，再比较报警升级暂停截止日期，如果大于暂停截止日期，则报警。
                    checkCompulsoryInsuranceAlarm(assetsVehicle, LONG_TERM_PRODUCT_LINE_DAYS, now);

                    int productLine = assetsVehicle.getProductLine();
                    if (productLine == ProductLineEnum.PRODUCT_LINE_ENUM_2.getCode()){
                        // 产品线长租
                        // 取年检到期时间，和报警开始时间比较，（取年检到期时间，报警开始时间，较大的时间开始计算）年检有效期不足7天，再比较报警升级暂停截止日期，如果大于暂停截止日期，则报警。
                        checkAnnualAlarm(assetsVehicle, LONG_TERM_PRODUCT_LINE_DAYS, now);
                    }else if (productLine == ProductLineEnum.PRODUCT_LINE_ENUM_3.getCode()){
                        // 产品线短租
                        // 取年检到期时间，和报警开始时间比较，（取年检到期时间，报警开始时间，较大的时间开始计算）年检有效期不足3天，再比较报警升级暂停截止日期，如果大于暂停截止日期，则报警
                        checkAnnualAlarm(assetsVehicle, SHORT_TERM_PRODUCT_LINE_DAYS, now);
                    }else {
                        continue;
                    }
                    if (searchVehicleFiles.size() < 5000){
                        doFlag = false;
                    }
                }catch (Exception e){
                    log.error("车辆处理异常:{}", assetsVehicle.getVin());
                }
            }
            pageNum ++;
        }while (doFlag);
    }

    /**
     * 保险临期
     * @param assetsVehicle
     * @param longTermProductLineDays
     * @param now
     */
    private void checkCompulsoryInsuranceAlarm(SearchVehicleFileList assetsVehicle, int longTermProductLineDays, Date now) {
        String compulsoryInsuranceExpire = assetsVehicle.getCompulsoryInsuranceExpire();
        String vin = assetsVehicle.getVin();
        // 产品线是否是长租
        boolean isLongRentVehicle = assetsVehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_2.getCode();
        log.info("开始处理保险临期:{}, 产品线是否是长租:{}", vin, isLongRentVehicle);
        // 开始时间
        // 开始时间
        RiskAlarmConfig riskAlarmConfig = getAlarmStartTime(assetsVehicle.getVin(), AlarmTypeEnum.INSURANCE_ADVENT.getCode());
        if (riskAlarmConfig == null){
            return;
        }

        // 判断车辆是否在白名单中
        boolean isWhiteList = riskCommonService.isWhiteList(assetsVehicle.getVin(), AlarmTypeEnum.INSURANCE_ADVENT.getCode());
        if (isWhiteList){
            return;
        }
        boolean hasAlarm = false;
        // 查询当前报警中的风控报警
        RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, AlarmTypeEnum.INSURANCE_ADVENT.getCode());
        if (riskAlarm != null){
            // 当前有报警
            hasAlarm = true;
        }
        // 时间为空立即报警
        if (StringUtils.isBlank(compulsoryInsuranceExpire)){
            return;
        }else {
            // 如果保险有效期 跟当前时间比较小于 14天则升级二级报警
            String expireDateStr = compulsoryInsuranceExpire.split(" ")[0];
            // 数据库存的是年月日，需要加一天
            LocalDate expireDate = LocalDate.parse(expireDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);

            LocalDate startTime = formatDateToLocalDate(riskAlarmConfig.getAlarmStartTime());
            if (startTime.isAfter(expireDate)){
                expireDate = startTime;
            }
            boolean isOverPause = false;
            // 暂停状态
            Date pauseDeadlineTime = riskAlarmConfig.getPauseDeadlineTime();
            if (pauseDeadlineTime != null){
                if (pauseDeadlineTime.compareTo(now) >= 0){
                    return;
                }else {
                    isOverPause = true;
                }
            }

            long diffDay = expireDate.toEpochDay() - LocalDate.now().toEpochDay();
            // 存在报警
            if (hasAlarm){
                // 判断是否需要升级
                /*if (riskAlarm.getAlarmLevel() > AlarmLevenEnum.ONE.getCode()){
                    return;
                }*/
                //LocalDate alarmStartDate = formatDateToLocalDate(riskAlarm.getAlarmTime());
                //long overDay = LocalDate.now().toEpochDay() - alarmStartDate.toEpochDay();
                long overDay = DateUtil.daysBetween(now, riskAlarm.getAlarmTime());
                log.info("车架号:{} 当前时间与报警时间相差天数:【{}】", vin, overDay);
                // 超过暂停时间
                if (isOverPause && riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                    log.info("车架号:{} 当前时间超过暂停截止时间，当前时间与保险到期日相差天数:【{}】", vin, diffDay);
                    if (diffDay > 0 && diffDay <= LONG_TERM_PRODUCT_LINE_DAYS){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmLevenEnum.ONE.getCode(), "", 0);
                    }else if (diffDay <= 0){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                    }
                    // 暂停状态
                    riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                    tableRiskAlarmService.update(riskAlarm);
                }else if (overDay >= LONG_TERM_PRODUCT_LINE_DAYS  && riskAlarm.getAlarmLevel() == AlarmLevenEnum.ONE.getCode()){
                    autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                }
            }else {
                log.info("车架号:{} 当前时间与保险到期日相差天数:【{}】", vin, diffDay);
                if (diffDay > 0 && diffDay <= LONG_TERM_PRODUCT_LINE_DAYS){
                    autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmLevenEnum.ONE.getCode(), "", 0);
                }else if (diffDay <= 0){
                    autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSURANCE_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                }
            }
        }
        log.info("结束处理保险临期:{}", vin);
    }

    private RiskAlarmConfig getAlarmStartTime(String vin, Integer alarmType){
        RiskAlarmConfig riskAlarmConfig = tableRiskAlarmConfigService.selectRiskAlarmConfig(vin, alarmType);
        if (riskAlarmConfig != null){
            return riskAlarmConfig;
        }
        return null;
    }

    /**
     * date 转localDate
     * @param date
     * @return
     */
    public LocalDate formatDateToLocalDate(Date date){
        // 将Date对象转换为Instant对象
        Instant instant = date.toInstant();

        // 使用Instant对象创建ZonedDateTime对象（默认时区）
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        // 从ZonedDateTime对象获取LocalDate对象
        LocalDate localDate = zonedDateTime.toLocalDate();
        return localDate;
    }

    /**
     * 判断年检临期报警逻辑
     * @param assetsVehicle
     * @param longTermProductLineDays
     */
    private void checkAnnualAlarm(SearchVehicleFileList assetsVehicle, int longTermProductLineDays, Date now) {
        String inspectionExpireDate = assetsVehicle.getInspectionExpireDate();
        String vin = assetsVehicle.getVin();
        log.info("开始处理年检临期:{}", vin);

        // 开始时间
        RiskAlarmConfig alarmConfig = getAlarmStartTime(assetsVehicle.getVin(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
        if (alarmConfig == null){
            return;
        }
        
        // 判断车辆是否在白名单中
        boolean isWhiteList = riskCommonService.isWhiteList(assetsVehicle.getVin(), AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
        if (isWhiteList){
            return;
        }
        // 是否有进行中的报警
        boolean hasAlarm = false;
        // 查询当前报警中的风控报警
        RiskAlarm riskAlarm = tableRiskAlarmService.queryDataByVinAndType(vin, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode());
        if (riskAlarm != null){
            hasAlarm = true;
        }
        if (StringUtils.isBlank(inspectionExpireDate)){
            return;
        }else {
            // 如果保险有效期 跟当前时间比较小于 14天则升级二级报警
            String expireDateStr = inspectionExpireDate.split(" ")[0];
            // 数据库存的是年月日，需要加一天
            LocalDate expireDate = LocalDate.parse(expireDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1);

            LocalDate startTime = formatDateToLocalDate(alarmConfig.getAlarmStartTime());
            if (startTime.isAfter(expireDate)){
                expireDate = startTime;
            }
            // 是否暂停
            boolean isOverPause = false;
            Date pauseDeadlineTime = alarmConfig.getPauseDeadlineTime();
            if (pauseDeadlineTime != null){
                if (pauseDeadlineTime.compareTo(now) >= 0){
                    return;
                }else {
                    isOverPause = true;
                }
            }

            // 相差天数
            long diffDay = expireDate.toEpochDay() - LocalDate.now().toEpochDay();
            if (hasAlarm){
                // 报警等级超过一级忽略
                /*if (riskAlarm.getAlarmLevel() > AlarmLevenEnum.ONE.getCode()){
                    return;
                }*/
                //LocalDate alarmStartDate = formatDateToLocalDate(riskAlarm.getAlarmTime());
                //long overDay = LocalDate.now().toEpochDay() - alarmStartDate.toEpochDay();
                long overDay = DateUtil.daysBetween(now, riskAlarm.getAlarmTime());

                log.info("车架号:{} 当前时间与报警时间相差天数:【{}】", vin, overDay);
                if (isOverPause && riskAlarm.getIsStopAlarm() == IsStopAlarmEnum.YES.getCode()){
                    log.info("车架号:{} 当前时间与年检到期日相差天数:【{}】", vin, diffDay);
                    if (diffDay > 0 && diffDay <= longTermProductLineDays){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.ONE.getCode(), "", 0);
                    }else if (diffDay <= 0){
                        autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                    }
                    // 更新是否暂停
                    riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
                    tableRiskAlarmService.update(riskAlarm);
                }else if (overDay >=  LONG_TERM_PRODUCT_LINE_DAYS  && riskAlarm.getAlarmLevel() == AlarmLevenEnum.ONE.getCode()){
                    autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                }
            }else {
                log.info("车架号:{} 当前时间与年检到期日相差天数:【{}】", vin, diffDay);
                if (diffDay > 0 && diffDay <= longTermProductLineDays){
                    autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.ONE.getCode(), "", 0);
                }else if (diffDay <= 0){
                    autoCreateRiskAlarm(assetsVehicle, AlarmTypeEnum.INSPECT_ANNUALLY_ADVENT.getCode(), AlarmLevenEnum.TWO.getCode(), "", 0);
                }
            }
        }
        log.info("结束处理年检临期:{}", vin);
    }

    /**
     * 创建风控报警
     * @param assetsVehicle
     * @param alarmType
     * @param alarmLevel
     */
    private void autoCreateRiskAlarm(SearchVehicleFileList assetsVehicle, Integer alarmType, Integer alarmLevel, String orderSeq, Integer orderType) {
        String vin = assetsVehicle.getVin();
        // 当前报警中的报警
        RiskAlarm riskAlarm = tableRiskAlarmService.queryRiskAlarm(vin, alarmType, AlarmStatusEnum.ALARM_STATUS.getCode());
        // 最大车辆风险等级
        Integer vehicleStatusMax = riskCommonService.queryMaxVehicleRiskStatus(assetsVehicle.getVin(), null);
        // 当前车辆风险登记
        Integer vehicleStatusNum = alarmLevel + 1 > vehicleStatusMax ? alarmLevel + 1 : vehicleStatusMax;;

        String alarmNo;
        if (riskAlarm != null){
            // 更新
            riskAlarm.setAlarmLevel(alarmLevel);
            // 获取GPS车辆信息
            setGPSInfo(riskAlarm, assetsVehicle.getVin());
            tableRiskAlarmService.update(riskAlarm);
            if (alarmLevel == 2){
                saveOperateLog(StrUtil.format("升级风险报警:【{}】", AlarmTypeEnum.getValueByCode(alarmType)),
                        riskAlarm.getAlarmNo(), OperateTypeEnum.OPERATE_TYPE.getCode());
                // 车辆日志
                saveVehicleLog(assetsVehicle.getVin(), StrUtil.format("升级风险报警:【{}】", AlarmTypeEnum.getValueByCode(alarmType)));
            }
            alarmNo = riskAlarm.getAlarmNo();
        }else {
            alarmNo = outDescIdUtil.nextId("FKBJ");
            riskAlarm = new RiskAlarm();
            Date now = new Date();
            riskAlarm.setAlarmNo(alarmNo);
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
            riskAlarm.setAlarmLevel(alarmLevel);
            riskAlarm.setAlarmType(alarmType);
            riskAlarm.setOrderSeq(orderSeq);
            riskAlarm.setOrderType(orderType);
            riskAlarm.setAlarmTime(now);
            riskAlarm.setVin(assetsVehicle.getVin());
            riskAlarm.setCreateOperName("定时任务");
            riskAlarm.setCreateOperAccount("System");
            riskAlarm.setCreateTime(now);

            // 获取GPS车辆信息
            setGPSInfo(riskAlarm, assetsVehicle.getVin());
            tableRiskAlarmService.save(riskAlarm);

            String logContent = StrUtil.format("新增风险报警:【{}】", AlarmTypeEnum.getValueByCode(alarmType));
            saveOperateLog(logContent, alarmNo, OperateTypeEnum.OPERATE_TYPE.getCode());
            // 车辆日志
            saveVehicleLog(assetsVehicle.getVin(), StrUtil.format("新增风险报警:【{}】", AlarmTypeEnum.getValueByCode(alarmType)));
        }

        if (alarmLevel.equals(AlarmLevenEnum.TWO.getCode())){
            if (RiskUtils.isNeedCreateCheck(assetsVehicle.getProductLine(), alarmType)){
                riskCheckService.autoCreateRiskCheck(riskAlarm, CurrentUser.newBuilder().setNickName("定时任务").setUserAccount("System").build());
            }else if (assetsVehicle.getProductLine() == 2){
                // 车辆风险等级
                vehicleStatusNum = vehicleStatusMax > VehicleRiskStatusEnum.RISK_VEHICLE.getCode() ? vehicleStatusMax : VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
                // 如果产品线是长租，且有进行中的长租订单 则推送长租
                QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(assetsVehicle.getVin()).build());
                if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                    log.error("查询长租订单合同信息失败! vin:{}", vin);
                    return;
                }
                List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
                if (CollectionUtil.isNotEmpty(operateContractInfoList)){
                    // 获取订单号
                    OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                    String orderNo = operateContractInfo.getOrderNo();
                    // 请求长租
                    RiskUtils.notifyRiskWorkOrderToLongRent(orderNo, assetsVehicle.getPlateNo(), alarmLevel, alarmNo, alarmType, 1);
                    // 记录推送标识，恢复报警需要判断是否已经推送过
                    tableRiskPushRecordService.insert(alarmNo);
                }
            }
        }
        // 更新车辆报警所有等级
        subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), vehicleStatusNum);

    }

    /**
     * 设置GPS信息
     * @param riskAlarm
     * @param vin
     */
    private void setGPSInfo(RiskAlarm riskAlarm, String vin){
        // 获取GPS车辆信息
        VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(vin);
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
    }

    /**
     * 新增操作日志
     * @param operateContent
     * @param relationKey
     */
    public void saveOperateLog(String operateContent, String relationKey, Integer operateType){
        com.saicmobility.evcard.vlms.risk.model.OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vinList
     * @param operateContent
     */
    public void saveVehicleLog(List<String> vinList, String operateContent){
        if (CollectionUtils.isEmpty(vinList)){
            return;
        }
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        for (String vin : vinList) {
            SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
            logList.add(log);
        }
        // 新增日志
        insertVehicleOperateLog(logList);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vin
     * @param operateContent
     */
    public void saveVehicleLog(String vin, String operateContent){
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
        logList.add(log);
        // 新增日志
        insertVehicleOperateLog(logList);
    }

    /**
     * 新增操作
     * @param logList
     */
    public void insertVehicleOperateLog(List<SaveVehicleOperateLog> logList){
        com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser user = com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser
                .newBuilder()
                .setNickName("定时任务")
                .setUserNo("")
                .setUserAccount("System").build();
        // 保存车辆预风控记录
        SaveVehicleOperateLogRes res = vlmsAssetsService.saveVehicleOperateLog(SaveVehicleOperateLogReq.newBuilder().addAllLogs(logList).setBusinessType(3).setCurrentUser(user).build());
        if (res.getRetCode() != 0) {
            throw new ServiceException( "保存车辆风控操作日志失败");
        }
    }

}
