package com.saicmobility.evcard.vlms.risk.dto.whitelist;

import com.saicmobility.evcard.vlms.risk.model.Attachment;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-25
 */
@Data
public class RiskWhitelistConfigData {

    private Long id;
    private String plateNo;

    private String vin;

    private String orgId;

    private String orgName;

    private String operateOrgId;

    private String operateOrgName;

    private Integer effectiveStatus;

    private String effectiveStatusDesc;

    private Integer alarmType;

    private String alarmTypeDesc;

    private Date expirationDate;

    private String expirationDateStr;

    private String createOperName;

    private String createTimeStr;

    private Date createTime;

    private String createOperAccount;

    private List<Attachment> attachmentPaths;

    private String miscDesc;
}
