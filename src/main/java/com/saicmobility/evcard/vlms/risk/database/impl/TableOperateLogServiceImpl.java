package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.mapper.OperateLogMapper;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryOperationLogReq;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.OperateLogDynamicSqlSupport.operateLog;
import static com.saicmobility.evcard.vlms.risk.mapper.OperateLogDynamicSqlSupport.operateType;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-01-30
 */
@Service
@Slf4j
public class TableOperateLogServiceImpl implements TableOperateLogService {

    @Resource
    private OperateLogMapper operateLogMapper;

    @Override
    public void save(OperateLog operateLog) {
        operateLogMapper.insertSelective(operateLog);
    }

    @Override
    public long selectTotal(QueryOperationLogReq req) {
        SelectStatementProvider render = select(count())
                .from(operateLog)
                .where()
                .and(operateLog.foreignId, isEqualTo(req.getRelationKey()))
                .and(operateLog.operateType, isEqualToWhenPresent(req.getOperateType()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return operateLogMapper.count(render);
    }

    @Override
    public List<OperateLog> selectList(QueryOperationLogReq req) {

        int pageNum = req.getPageNum() == 0 ? 1 : req.getPageNum();
        int pageSize = req.getPageSize() == 0 ? 10 : req.getPageSize();
        int limitNum = (pageNum -1) * pageSize;
        SelectStatementProvider render = select(operateLog.allColumns())
                .from(operateLog)
                .where()
                .and(operateLog.foreignId, isEqualTo(req.getRelationKey()))
                .and(operateLog.operateType, isEqualToWhenPresent(req.getOperateType()))
                .orderBy(operateLog.id.descending())
                .limit(pageSize)
                .offset(limitNum)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return operateLogMapper.selectMany(render);
    }
}
