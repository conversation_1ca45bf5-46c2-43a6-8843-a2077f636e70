package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskCheckCompleteApproveLinkDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    public static final RiskCheckCompleteApproveLink riskCheckCompleteApproveLink = new RiskCheckCompleteApproveLink();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.id")
    public static final SqlColumn<Long> id = riskCheckCompleteApproveLink.id;

    /**
     * Database Column Remarks:
     *   白杨审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.application_id")
    public static final SqlColumn<String> applicationId = riskCheckCompleteApproveLink.applicationId;

    /**
     * Database Column Remarks:
     *   风控收车任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve_link.check_no")
    public static final SqlColumn<String> checkNo = riskCheckCompleteApproveLink.checkNo;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve_link")
    public static final class RiskCheckCompleteApproveLink extends AliasableSqlTable<RiskCheckCompleteApproveLink> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> applicationId = column("application_id", JDBCType.VARCHAR);

        public final SqlColumn<String> checkNo = column("check_no", JDBCType.VARCHAR);

        public RiskCheckCompleteApproveLink() {
            super("t_risk_check_complete_approve_link", RiskCheckCompleteApproveLink::new);
        }
    }
}