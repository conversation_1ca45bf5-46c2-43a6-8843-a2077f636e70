package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy;
import com.saicmobility.evcard.md.mddataproxy.api.OperateContractInfo;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsReq;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsRes;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.enums.*;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskPushRecord;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmService;
import com.saicmobility.evcard.vlms.risk.service.RiskCommonService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubAssetsService;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.DateUtil;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileListReq;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileListRes;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 车辆gps下线预警及恢复
 * <AUTHOR>
 * @date 2024-01-18
 */
@Slf4j
@JobHandler(value = "vehicleGPSOfflineWarningHandler")
@Component
public class VehicleGPSOfflineWarningHandler extends IJobHandler {

    @Resource
    public OutDescIdUtil outDescIdUtil;

    @Resource
    private RiskAlarmService riskAlarmService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private RiskCommonService riskCommonService;

    @Resource
    private SubAssetsService subAssetsService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private TableRiskAlarmConfigService tableRiskAlarmConfigService;

    @Resource
    private SubRiskAlaramService subRiskAlaramService;

    @Resource
    private MdDataProxy mdDataProxy;

    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;




    private final static int pageSize = 1000;

    private static final Integer LONG_TERM_CODE = ParserForTypeEnum.LONG_TERM.getCode(); // 例如："LONG_TERM"
    private static final Integer SHORT_TERM_CODE = ParserForTypeEnum.SHORT_TERM.getCode(); // 例如："SHORT_TERM"
    private static final int DAYS_WARNING_7 = 7; // 7天告警
    private static final int DAYS_WARNING_2 = 2; // 2天告警


    @Override
    public ReturnT<String> execute(String queryVin) throws Exception {
        log.info("vehicleGPSEarlyWarningHandler:开始执行任务!");
        Integer pageNum = 1;
        List<RiskAlarmConfigData> queryRiskAlarmConfigRes;
        do {
             queryRiskAlarmConfigRes = tableRiskAlarmConfigService.selectList(QueryRiskAlarmConfigReq.newBuilder().setVin(queryVin).setAlarmType(AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode())
                    .setPageNum(pageNum).setPageSize(pageSize).build());
            if (CollectionUtil.isEmpty(queryRiskAlarmConfigRes)) {
                log.info("vehicleGPSEarlyWarningHandler:未查询到风控配置GPS下线车辆列表!");
                return ReturnT.SUCCESS;
            }

            for(RiskAlarmConfigData vehicle : queryRiskAlarmConfigRes){
                try{
                    if(riskCommonService.isWhiteList(vehicle.getVin(), AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode())){
                        continue;
                    }
                    // 处理GPS上传时间的逻辑
                    VehicleGPSData vehicleGPSData = riskCommonService.fetchGpsDate(vehicle.getVin());
                    SearchVehicleFileListRes searchAssetsVehicleRes = vlmsAssetsService.searchVehicleFileListForRisk(SearchVehicleFileListReq.newBuilder()
                            .setPageNum(1).setPageSize(1).setVehiclePhase(2).setVin(vehicle.getVin()).build());
                    if(null == searchAssetsVehicleRes || searchAssetsVehicleRes.getInfoList().isEmpty()){
                        continue;
                    }
                    if(null == vehicleGPSData.getGpsDateTime() || vehicle.getAlarmStartTime().after(new Date(vehicleGPSData.getGpsDateTime()))){
                        vehicleGPSData.setGpsDateTime(vehicle.getAlarmStartTime().getTime());
                    }
                    if(null != vehicle.getPauseDeadlineTime()){
                        vehicleGPSData.setPauseDeadlineTime(vehicle.getPauseDeadlineTime());
                    }
                    //查询是否存在进行中的GPS信号离线
                    RiskAlarm riskAlarm = riskAlarmService.queryDataByVinAndAlarmType(vehicle.getVin(), AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode());
                    if(null == riskAlarm && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().after(new Date())){
                        continue;
                    }
                    SearchVehicleFileList assetsVehicle = searchAssetsVehicleRes.getInfoList().get(0);
                    // 判断资产状态是否需要报警
                    if (RiskUtils.isIgnoreAlarm(assetsVehicle.getPropertyStatus(), assetsVehicle.getDeliveryStatus())){
                        continue;
                    }
                    if(null == vehicleGPSData.getGpsDateTime() || null == vehicleGPSData.getOldGpsDateTime()){
                        processLongOrShortTermGpsDataIsNull(assetsVehicle,vehicleGPSData,riskAlarm);
                    }else {
                        processLongOrShortTermGpsData(assetsVehicle,vehicleGPSData,riskAlarm);
                    }
                }catch (Exception e){
                    log.error("vehicleGPSEarlyWarningHandler:处理车辆GPS下线预警异常! vin:{}", vehicle.getVin(), e);
                }
            }
            pageNum++;
            /*queryRiskAlarmConfigRes = tableRiskAlarmConfigService.selectList(QueryRiskAlarmConfigReq.newBuilder().setAlarmType(AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode())
                    .setPageNum(pageNum).setPageSize(pageSize).build());*/
        }while (CollectionUtil.isNotEmpty(queryRiskAlarmConfigRes));
        log.info("vehicleGPSEarlyWarningHandler:结束执行任务!");
        return ReturnT.SUCCESS;
    }

    /**
     * gps上传时间为空，告警逻辑处理
     * @param assetsVehicle
     */
    private void processLongOrShortTermGpsDataIsNull(SearchVehicleFileList assetsVehicle,VehicleGPSData vehicleGPSData,RiskAlarm riskAlarm) {
        //查询当前车辆存在的进行中的最高级别告警
        Integer vehicleRiskStatus = riskCommonService.queryMaxVehicleRiskStatus(assetsVehicle.getVin(), null);
        if(null == riskAlarm && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().after(new Date())){
            return;
        }
        //新建告警

        if(null == riskAlarm){
            riskCommonService.addRiskAlarm(assetsVehicle,null,AlarmLevenEnum.ONE.getCode(),AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
            //gps离线新建告警，新车辆资产信息表，车辆风险状态为风险车辆
            if (vehicleRiskStatus<VehicleRiskStatusEnum.RISK_VEHICLE.getCode()){
                subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
            }

            return;
        }else if (assetsVehicle.getProductLine() == ProductLineEnum.PRODUCT_LINE_ENUM_1.getCode()){
            // 如果车辆产品线是车管中心则恢复报警
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
            riskCommonService.updateRiskAlarm(false,riskAlarm,vehicleGPSData,assetsVehicle.getProductLine());
            //通知长租
            if (assetsVehicle.getProductLine()==LONG_TERM_CODE){
                subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
            }
            return;
        }

        //暂停告警逻辑
        if(!riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_STATUS.getCode()) && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().before(new Date())){
            isStopAlarm(riskAlarm,assetsVehicle,vehicleRiskStatus,vehicleGPSData);
            return;
        }
        // 如果报警不是暂停，且告警状态是恢复，则不处理
        if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
            return;
        }
        // 计算天数 升级告警
        long dayNum = DateUtil.daysBetween(new Date(),new Date(vehicleGPSData.getGpsDateTime()));
        if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && riskAlarm != null && dayNum >= DAYS_WARNING_7) ||
                (assetsVehicle.getProductLine() == SHORT_TERM_CODE && riskAlarm != null && dayNum >= DAYS_WARNING_7)) { // 长租逻辑
            riskCommonService.updateRiskAlarm(true,riskAlarm,vehicleGPSData,assetsVehicle.getProductLine());
            // 查看有没有推过长租
            RiskPushRecord pushRecord = tableRiskPushRecordService.query(riskAlarm.getAlarmNo());
            if (pushRecord != null){
                return;
            }
            //升级告警，推送长租
            Integer alarmLevel = riskAlarm.getAlarmLevel();
            if (assetsVehicle.getProductLine() == LONG_TERM_CODE && alarmLevel.equals(AlarmLevenEnum.TWO.getCode())){
                // 如果产品线是长租，且有进行中的长租订单 则推送长租
                QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(assetsVehicle.getVin()).build());
                if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                    log.error("查询长租订单合同信息失败! vin:{}", assetsVehicle.getVin());
                    return;
                }
                List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
                if (CollectionUtil.isEmpty(operateContractInfoList)){
                    log.error("查询长租订单合同信息为空! vin:{}", assetsVehicle.getVin());
                    return;
                }
                // 获取订单号
                OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                String orderNo = operateContractInfo.getOrderNo();
                RiskUtils.notifyRiskWorkOrderToLongRent(orderNo, assetsVehicle.getPlateNo(), alarmLevel, riskAlarm.getAlarmNo(), riskAlarm.getAlarmType(), 1);
                // 记录推送标识，恢复报警需要判断是否已经推送过
                tableRiskPushRecordService.insert(riskAlarm.getAlarmNo());
                // 更新车辆报警所有等级
                subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), vehicleRiskStatus>alarmLevel+1?vehicleRiskStatus:alarmLevel+1);
            }
        }
    }

    /**
     * 停止告警逻辑
     * @param riskAlarm
     * @param assetsVehicle
     * @return
     */
    private void isStopAlarm(RiskAlarm riskAlarm,SearchVehicleFileList assetsVehicle,Integer vehicleRiskStatus,VehicleGPSData vehicleGPSData) {
        RiskAlarm updateRiskAlarm = new RiskAlarm();
        updateRiskAlarm.setId(riskAlarm.getId());
        updateRiskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        tableRiskAlarmService.update(updateRiskAlarm);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("暂停后恢复风控报警【{}】", AlarmTypeEnum.getValueByCode(AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode())));
        Date now = new Date();
        Integer vehicleRiskStatusNew = 0;
        //判断是否超时
        long dayNum = DateUtil.daysBetween(new Date(),new Date(vehicleGPSData.getGpsDateTime()));
        if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_7 && dayNum < DAYS_WARNING_7 + DAYS_WARNING_7)
                || (assetsVehicle.getProductLine() == SHORT_TERM_CODE && dayNum >= DAYS_WARNING_2 && dayNum < DAYS_WARNING_2 + DAYS_WARNING_7)) { //一级告警
            riskAlarm.setAlarmLevel(AlarmLevenEnum.ONE.getCode());
            if(vehicleRiskStatus < VehicleRiskStatusEnum.RISK_VEHICLE.getCode()){
                vehicleRiskStatusNew = VehicleRiskStatusEnum.RISK_VEHICLE.getCode();
            }
        }else if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_7 + DAYS_WARNING_7)
                || (assetsVehicle.getProductLine() == SHORT_TERM_CODE && dayNum >= DAYS_WARNING_2 + DAYS_WARNING_7)) { //二级告警
            riskAlarm.setAlarmLevel(AlarmLevenEnum.TWO.getCode());
            if(vehicleRiskStatus < VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode()){
                vehicleRiskStatusNew = VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode();
            }
        }else {
            return;
        }
        //创建新告警记录
        if(null != vehicleGPSData){
            if(null != vehicleGPSData.getOldGpsDateTime()){
                riskAlarm.setLastLocationTime(new Date(vehicleGPSData.getOldGpsDateTime()));
            }
            riskAlarm.setLastLocation(vehicleGPSData.getLastAddress());
        }
        riskAlarm.setId(null);
        riskAlarm.setRecoveryDate(null);
        riskAlarm.setRecoverMethod(null);
        riskAlarm.setRecoverChannel(0);
        riskAlarm.setStopAlarmDay(null);
        riskAlarm.setPauseDeadlineTime(null);
        riskAlarm.setAlarmTime(now);
        riskAlarm.setIsStopAlarm(IsStopAlarmEnum.NO.getCode());
        riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_STATUS.getCode());
        riskAlarm.setAlarmNo(outDescIdUtil.nextId("FKBJ"));
        riskAlarm.setCreateTime(now);
        riskAlarm.setCreateOperName("定时任务");
        riskAlarm.setCreateOperAccount(StringUtils.EMPTY);
        tableRiskAlarmService.save(riskAlarm);
        subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), vehicleRiskStatusNew > vehicleRiskStatus?vehicleRiskStatusNew:vehicleRiskStatus);
        saveLog(riskAlarm.getAlarmNo(), StrUtil.format("添加风控类型【{}】", AlarmTypeEnum.getValueByCode(AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode())));

    }

    /**
     * 告警逻辑
     * @param assetsVehicle
     * @param vehicleGPSData
     */
    private void processLongOrShortTermGpsData(SearchVehicleFileList assetsVehicle, VehicleGPSData vehicleGPSData,RiskAlarm riskAlarm) {
        // 计算天数
        long dayNum = DateUtil.daysBetween(new Date(),new Date(vehicleGPSData.getGpsDateTime()));
        //查询当前车辆存在的进行中的最高级别告警
        Integer vehicleRiskStatus = riskCommonService.queryMaxVehicleRiskStatus(assetsVehicle.getVin(), null);
        //新建告警逻辑
        if(null == riskAlarm){
            if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_7 && dayNum < DAYS_WARNING_7 + DAYS_WARNING_7)
                    || (assetsVehicle.getProductLine() == SHORT_TERM_CODE && dayNum >= DAYS_WARNING_2 && dayNum < DAYS_WARNING_2 + DAYS_WARNING_7)) {//一级告警
                riskCommonService.addRiskAlarm(assetsVehicle,vehicleGPSData,AlarmLevenEnum.ONE.getCode(),AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
            }else if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && dayNum >= DAYS_WARNING_7 + DAYS_WARNING_7)
                    || (assetsVehicle.getProductLine() == SHORT_TERM_CODE && dayNum >= DAYS_WARNING_2 + DAYS_WARNING_7)) {//二级告警
                riskCommonService.addRiskAlarm(assetsVehicle,vehicleGPSData,AlarmLevenEnum.TWO.getCode(),AlarmTypeEnum.GPS_SIGNAL_OFFLINE.getCode(),VehicleRiskStatusEnum.RISK_CONTROL_VEHICLE.getCode());
                return;
            }else {
                return;
            }
            //gps离线新建告警，新车辆资产信息表，如果已经是风险车辆则不更新
            if (vehicleRiskStatus<VehicleRiskStatusEnum.RISK_VEHICLE.getCode()){
                subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(),VehicleRiskStatusEnum.RISK_VEHICLE.getCode());
            }
        }

        //暂停告警逻辑
        if(!riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_STATUS.getCode()) && null != vehicleGPSData.getPauseDeadlineTime() && vehicleGPSData.getPauseDeadlineTime().before(new Date())){
            isStopAlarm(riskAlarm,assetsVehicle,vehicleRiskStatus,vehicleGPSData);
            return;
        }
        // 如果报警不是暂停，且告警状态是恢复，则不处理
        if (riskAlarm.getAlarmStatus().equals(AlarmStatusEnum.ALARM_RECOVER.getCode())){
            return;
        }
        //恢复告警逻辑
        long recoverDayNum = DateUtil.daysBetween(new Date(),new Date(vehicleGPSData.getOldGpsDateTime()));
        if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && riskAlarm != null && recoverDayNum < DAYS_WARNING_7) ||
                (assetsVehicle.getProductLine() == SHORT_TERM_CODE && riskAlarm != null && recoverDayNum < DAYS_WARNING_2) ||
                assetsVehicle.getProductLine() == 1) {
            riskAlarm.setAlarmStatus(AlarmStatusEnum.ALARM_RECOVER.getCode());
            riskAlarm.setRecoverChannel(AlarmSystemEnum.WTXT.getCode());
            riskCommonService.updateRiskAlarm(false,riskAlarm,vehicleGPSData,assetsVehicle.getProductLine());
            //通知长租
            if (assetsVehicle.getProductLine()==LONG_TERM_CODE){
                subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
            }
            return;
        }

        if (assetsVehicle.getProductLine()==ParserForTypeEnum.VEHICLE_MANAGEMENT_CENTER.getCode()){
            riskCommonService.updateRiskAlarm(false,riskAlarm,vehicleGPSData,assetsVehicle.getProductLine());
            subRiskAlaramService.alarmRecoryNotify(riskAlarm.getAlarmNo());
        }


        //升级告警逻辑
        long upgradeDayNum = DateUtil.daysBetween(new Date(),riskAlarm.getAlarmTime());
        if ((assetsVehicle.getProductLine() == LONG_TERM_CODE && riskAlarm != null && upgradeDayNum >= DAYS_WARNING_7) ||
                (assetsVehicle.getProductLine() == SHORT_TERM_CODE && riskAlarm != null && upgradeDayNum >= DAYS_WARNING_7)) {
            riskCommonService.updateRiskAlarm(true,riskAlarm,vehicleGPSData,assetsVehicle.getProductLine());
            // 查看有没有推过长租
            RiskPushRecord pushRecord = tableRiskPushRecordService.query(riskAlarm.getAlarmNo());
            if (pushRecord != null){
                return;
            }
            Integer alarmLevel = riskAlarm.getAlarmLevel();
            if (assetsVehicle.getProductLine() == LONG_TERM_CODE && alarmLevel.equals(AlarmLevenEnum.TWO.getCode())){
                // 如果产品线是长租，且有进行中的长租订单 则推送长租
                QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(assetsVehicle.getVin()).build());
                if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
                    log.error("查询长租订单合同信息失败! vin:{}", assetsVehicle.getVin());
                    return;
                }
                List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
                if (CollectionUtil.isEmpty(operateContractInfoList)){
                    log.error("查询长租订单合同信息为空! vin:{}", assetsVehicle.getVin());
                    return;
                }
                // 获取订单号
                OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
                String orderNo = operateContractInfo.getOrderNo();
                RiskUtils.notifyRiskWorkOrderToLongRent(orderNo, assetsVehicle.getPlateNo(), alarmLevel, riskAlarm.getAlarmNo(), riskAlarm.getAlarmType(), 1);
                // 记录推送标识，恢复报警需要判断是否已经推送过
                tableRiskPushRecordService.insert(riskAlarm.getAlarmNo());
                // 更新车辆报警所有等级
                subAssetsService.updateVehicleRiskStatusByVin(assetsVehicle.getVin(), vehicleRiskStatus>alarmLevel+1?vehicleRiskStatus:alarmLevel+1);
            }

        }
    }
    /**
     * 记录日志
     * @param relationKey
     * @param operateContent
     */
    private void saveLog(String relationKey, String operateContent) {
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(OperateTypeEnum.OPERATE_TYPE.getCode());
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }
}

