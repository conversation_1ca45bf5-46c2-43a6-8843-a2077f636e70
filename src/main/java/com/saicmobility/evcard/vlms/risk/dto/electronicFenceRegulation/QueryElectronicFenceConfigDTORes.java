package com.saicmobility.evcard.vlms.risk.dto.electronicFenceRegulation;

import com.saicmobility.evcard.md.mddataproxy.api.ElectronicFenceRegulationInfo;
import com.saicmobility.evcard.md.mddataproxy.api.QueryElectronicFenceConfigRes;
import com.saicmobility.evcard.vlms.risk.bo.ElectronicFenceRegulationBO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class QueryElectronicFenceConfigDTORes{

    private List<ElectronicFenceRegulationBO> list;

    private Long total;
    public QueryElectronicFenceConfigDTORes toRes(QueryElectronicFenceConfigRes queryElectronicFenceConfigRes) {
        List<ElectronicFenceRegulationInfo> electronicFenceRegulationInfoList = queryElectronicFenceConfigRes.getListList();
        QueryElectronicFenceConfigDTORes queryElectronicFenceConfigDTORes = new QueryElectronicFenceConfigDTORes();
        List<ElectronicFenceRegulationBO> electronicFenceRegulationBOS = new ArrayList<>();
        electronicFenceRegulationInfoList.forEach(electronicFenceRegulationInfo -> {
            ElectronicFenceRegulationBO electronicFenceRegulationBO = new ElectronicFenceRegulationBO();
            electronicFenceRegulationBO.setId(electronicFenceRegulationInfo.getId());
            electronicFenceRegulationBO.setVin(electronicFenceRegulationInfo.getVin());
            electronicFenceRegulationBO.setRegulationType(electronicFenceRegulationInfo.getRegulationType());
            electronicFenceRegulationBO.setVehicleNo(electronicFenceRegulationInfo.getVehicleNo());
            electronicFenceRegulationBO.setOrgId(electronicFenceRegulationInfo.getOrgId());
            electronicFenceRegulationBO.setOrgName(electronicFenceRegulationInfo.getOrgName());
            electronicFenceRegulationBO.setOperationOrgId(electronicFenceRegulationInfo.getOperationOrgId());
            electronicFenceRegulationBO.setOperationOrgName(electronicFenceRegulationInfo.getOperationOrgName());
            electronicFenceRegulationBO.setProductLine(electronicFenceRegulationInfo.getProductLine());
            electronicFenceRegulationBO.setBusinessLine(electronicFenceRegulationInfo.getBusinessLine());
            electronicFenceRegulationBO.setPenCityIdScope(electronicFenceRegulationInfo.getPenCityIdScope());
            electronicFenceRegulationBO.setPenCityScope(electronicFenceRegulationInfo.getPenCityScope());
            electronicFenceRegulationBOS.add(electronicFenceRegulationBO);
        });
        queryElectronicFenceConfigDTORes.setList(electronicFenceRegulationBOS);
        queryElectronicFenceConfigDTORes.setTotal(queryElectronicFenceConfigRes.getTotal());
        return queryElectronicFenceConfigDTORes;
    }
}
