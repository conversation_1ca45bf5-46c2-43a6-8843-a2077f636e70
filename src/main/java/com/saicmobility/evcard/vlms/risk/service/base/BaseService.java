package com.saicmobility.evcard.vlms.risk.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcelFactory;
import com.google.common.collect.Lists;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreDetailByIdReq;
import com.saicmobility.evcard.md.mdstoreservice.api.GetStoreDetailByIdRes;
import com.saicmobility.evcard.md.mdstoreservice.api.MdStoreService;
import com.saicmobility.evcard.vlms.risk.Global;
import com.saicmobility.evcard.vlms.risk.constant.RealFields;
import com.saicmobility.evcard.vlms.risk.database.TableAttachmentService;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.dto.VehicleOperateLog;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.error.ResultCode;
import com.saicmobility.evcard.vlms.risk.error.ServiceException;
import com.saicmobility.evcard.vlms.risk.excel.ExportErrorInfo;
import com.saicmobility.evcard.vlms.risk.model.Attachment;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.utils.BaseUtils;
import com.saicmobility.evcard.vlms.risk.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.*;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.AsyncCreateLifeCycleFileReq;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.AsyncCreateLifeCycleFileRes;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.UpdateAsyncLifeCycleFileStatusReq;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.VlmsOperationService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
@Component
@Slf4j
public abstract class BaseService {

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private TableAttachmentService tableAttachmentService;

    @Resource
    private TableOperateLogService tableOperateLogService;

    @Resource
    private MdStoreService mdStoreService;

    @Autowired
    public OutDescIdUtil outDescIdUtil;

    @Resource(name = "asyncServiceExecutor")
    private Executor asyncServiceExecutor;

    @Resource
    private VlmsOperationService vlmsOperationService;

    /**
     * 根据id查询门店名称
     * @return
     */
    public String getStoreName(long storeId){
        GetStoreDetailByIdReq.Builder getStoreDetail = GetStoreDetailByIdReq.newBuilder().setId(storeId).setStoreType(1);
        GetStoreDetailByIdRes store =  mdStoreService.getStoreDetailById(getStoreDetail.build());
        if(null != store){
            return store.getStoreName();
        }
        return null;
    }

    /**
     * 根据车架号查询车辆信息
     * @param vin
     * @return
     */
    public AssetsVehicle getVehicleInfoByVin(String vin){
        GetAssetsVehicleByVinRes res = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vin).build());
        List<AssetsVehicle> vehicles = res.getInfoList();
        if (CollectionUtil.isEmpty(vehicles)) {
            return null;
        }
        return vehicles.get(0);
    }

    // 创建错误信息
    public ExportErrorInfo createErrorInfo(String vin, String errorMsg) {
        ExportErrorInfo errorInfo = new ExportErrorInfo();
        errorInfo.setVin(vin);
        errorInfo.setError(errorMsg);
        return errorInfo;
    }

    /**
     * 新增操作日志
     * @param operateContent
     * @param relationKey
     * @param currentUser
     */
    public void saveOperateLog(String operateContent, String relationKey, Integer operateType, CurrentUser currentUser){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        if(null != currentUser){
            operateLog.setCreateOperName(currentUser.getNickName());
            operateLog.setCreateOperAccount(currentUser.getUserAccount());
        }else {
            operateLog.setCreateOperName("定时任务");
            operateLog.setCreateOperAccount("System");
        }
        tableOperateLogService.save(operateLog);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vinList
     * @param operateContent
     * @param currentUser
     */
    public void saveVehicleLog(List<String> vinList, String operateContent, CurrentUser currentUser){
        if (CollectionUtils.isEmpty(vinList)){
            return;
        }
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        for (String vin : vinList) {
            SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
            logList.add(log);
        }
        // 新增日志
        insertVehicleOperateLog(logList, currentUser);
    }

    /**
     * 新增车辆操作日志（车辆档案查看）
     * @param vin
     * @param operateContent
     * @param currentUser
     */
    public void saveVehicleLog(String vin, String operateContent, CurrentUser currentUser){
        List<SaveVehicleOperateLog> logList = new ArrayList<>();
        SaveVehicleOperateLog log = SaveVehicleOperateLog.newBuilder().setVin(vin).setRemark(operateContent).build();
        logList.add(log);
        // 新增日志
        insertVehicleOperateLog(logList, currentUser);
    }

    /**
     * 新增操作
     * @param logList
     * @param currentUser
     */
    public void insertVehicleOperateLog(List<SaveVehicleOperateLog> logList, com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser currentUser){
        com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser user = com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser
                .newBuilder()
                .setNickName(currentUser.getNickName())
                .setUserNo(currentUser.getUserNo())
                .setUserAccount(currentUser.getUserAccount()).build();
        // 保存车辆预风控记录
        SaveVehicleOperateLogRes res = vlmsAssetsService.saveVehicleOperateLog(SaveVehicleOperateLogReq.newBuilder().addAllLogs(logList).setBusinessType(3).setCurrentUser(user).build());
        if (res.getRetCode() != 0) {
            throw new ServiceException( "保存车辆风控操作日志失败");
        }
    }

    /**
     * 新增操作
     * @param vehicleOperateLogList
     * @param currentUser
     */
    public void addVehicleOperateLog(List<VehicleOperateLog> vehicleOperateLogList, com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser currentUser){
        SaveVehicleOperateLogReq.Builder builder = SaveVehicleOperateLogReq.newBuilder();
        if (CollectionUtil.isEmpty(vehicleOperateLogList)){
            return;
        }
        for (VehicleOperateLog vehicleOperateLog : vehicleOperateLogList) {
            builder.addLogs(SaveVehicleOperateLog.newBuilder().setVin(vehicleOperateLog.getVin()).setRemark(vehicleOperateLog.getLogContent()).build());
        }
        builder.setBusinessType(3).setCurrentUser(com.saicmobility.evcard.vlms.vlmsassetsservice.api.CurrentUser.newBuilder()
                .setNickName(currentUser.getNickName())
                .setUserNo(currentUser.getUserNo())
                .setUserAccount(currentUser.getUserAccount())
                .build());
        // 保存车辆预风控记录
        SaveVehicleOperateLogRes res = vlmsAssetsService.saveVehicleOperateLog(builder.build());
        if (res.getRetCode() != 0) {
            throw new ServiceException( "保存车辆风控操作日志失败");
        }
    }

    /**
     * 附件存表
     * @param attachmentInfo
     * @return
     */
    public Long uploadAttachment(com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo attachmentInfo){
        Attachment attachment = new Attachment();
        BeanUtils.copyProperties(attachmentInfo, attachment);
        tableAttachmentService.insert(attachment);
        return attachment.getId();
    }

    /**
     * 附件存表
     * @param attachmentInfoList
     * @param fileType 1-风控报警-解除 2-风控报警-升级 3-风控收车-其他附件 4-白名单配置
     *                 5-风控收车-验车单 6-风控收车-车辆清收音视频资料 7-风控收车-车内物品交接单 8-定位截图 9-水印照片 10 失控附件
     * @param relationId
     */
    public void batchUploadAttachment(List<com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo> attachmentInfoList, int fileType, String relationId,CurrentUser user){
        if (CollectionUtils.isEmpty(attachmentInfoList)){
            return;
        }
        List<Attachment> attachmentList = Lists.newArrayList();
        for (com.saicmobility.evcard.vlms.vlmsriskservice.api.AttachmentInfo attachmentInfo : attachmentInfoList) {
            Attachment attachment = new Attachment();
            BeanUtils.copyProperties(attachmentInfo, attachment);
            attachment.setFileType(fileType);
            attachment.setRelationId(relationId);
            attachment.setCreateOperAccount(user.getUserAccount());
            attachment.setCreateOperName(user.getNickName());
            attachmentList.add(attachment);
        }
        tableAttachmentService.batchInsert(attachmentList);
    }

    /**
     * 附件存表
     * @param attachmentUrlList
     * @param fileType 1-风控报警-解除 2-风控报警-升级 3-风控收车-其他附件 4-白名单配置
     *                 5-风控收车-验车单 6-风控收车-车辆清收音视频资料 7-风控收车-车内物品交接单 8-定位截图 9-水印照片 10 失控附件
     * @param relationId
     */
    public void batchUploadAttachmentUrl(List<String> attachmentUrlList, int fileType, String relationId, CurrentUser user){
        if (CollectionUtils.isEmpty(attachmentUrlList)){
            return;
        }
        List<Attachment> attachmentList = Lists.newArrayList();
        for (String attachmentUrl : attachmentUrlList) {
            Attachment attachment = new Attachment();
            attachment.setFilePath(attachmentUrl);
            attachment.setFileType(fileType);
            attachment.setRelationId(relationId);
            attachment.setCreateOperAccount(user.getUserAccount());
            attachment.setCreateOperName(user.getNickName());
            attachmentList.add(attachment);
        }
        tableAttachmentService.batchInsert(attachmentList);
    }


    /**
     * 查询附件
     * @param fileType 1-解除报警 2-报警升级
     * @param relationId
     */
    public List<Attachment> queryAttachment(int fileType, String relationId){
        return tableAttachmentService.queryAttachmentByFileType(fileType,relationId);
    }

    /**
     * 查询附件
     * @param fileType 1-解除报警 2-报警升级
     * @param relationId
     */
    public void deleteAttachmentByFileType(int fileType, String relationId){
         tableAttachmentService.delete(fileType,relationId);
    }


    /**
     * 计算时间差
     * @param gpsDateTime
     * @return
     */
    public long timestampDifference(long gpsDateTime) {
        // 给定的时间戳，单位是毫秒 将时间戳转换为Instant对象，代表的是UTC时间
        Instant givenInstant = Instant.ofEpochMilli(gpsDateTime);
        // 获取当前定时任务时间的Instant对象
        Instant now = Instant.now();
        // 计算两个时间点之间的差异，结果是Duration对象
        Duration duration = Duration.between(givenInstant, now);
        // 将持续时间转换为天数，这里可能涉及到精度丢失，因为Duration不直接提供获取天数的方法
        // 我们可以通过除以一天的毫秒数（24小时 * 60分钟 * 60秒 * 1000毫秒）来近似计算相差的天数
        long dayMillis = 24 * 60 * 60 * 1000;
        long days = duration.toMillis() / dayMillis;
        return days;
    }

    /**
     * 记录日志
     * @param relationKey
     * @param operateContent
     */
    public void saveLog(String relationKey, String operateContent) {
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(OperateTypeEnum.OPERATE_TYPE.getCode());
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");
        tableOperateLogService.save(operateLog);
    }
    public void asynchronousUpload(String fileName, int fileType, List<ExportErrorInfo> errorInfoList, CurrentUser currentUser){
        String suffixPath = BaseUtils.getFilePathSuffix(fileName, 2);
        String filePath = Global.instance.projectDownloadUrl + suffixPath;
        com.saicmobility.evcard.vlms.vlmsoperationservice.api.CurrentUser user = com.saicmobility.evcard.vlms.vlmsoperationservice.api.CurrentUser
                .newBuilder()
                .setUserAccount(currentUser.getUserAccount())
                .setOrgId(currentUser.getOrgId())
                .setUserNo(currentUser.getUserNo())
                .setNickName(currentUser.getNickName()).build();
        AsyncCreateLifeCycleFileRes res = vlmsOperationService.asyncCreateLifeCycleFile(AsyncCreateLifeCycleFileReq.newBuilder().setFileSystem(1)
                .setFilePath(filePath).setFileName(fileName).setFileSource(fileType).setCurrentUser(user).build());
        if (res.getRetCode() < 0) {
            throw new ServiceException(ResultCode.COMMON_FAIL, res.getRetMsg());
        }
        asyncServiceExecutor.execute(() -> {
            FileOutputStream fileOutputStream = null;
            // 文件id  主键
            int fileId = res.getFileId();
            // 文件状态 2=生成成功 3=生成失败
            int fileStatus = 2;
            try {
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                EasyExcelFactory.write(os, ExportErrorInfo.class).sheet(0).doWrite(errorInfoList);
                byte[] bytes = os.toByteArray();
                fileOutputStream = new FileOutputStream(Global.instance.mfsRootPath+suffixPath);
                fileOutputStream.write(bytes);
            } catch (FileNotFoundException e) {
                log.error("导出失败1 :", e);
                fileStatus = 3;
            } catch (IOException e) {
                log.error("导出失败2 :", e);
                fileStatus = 3;
            } finally {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    fileStatus = 3;
                    log.error("导出失败3 :", e);
                }
                vlmsOperationService.updateAsyncLifeCycleFileStatus(UpdateAsyncLifeCycleFileStatusReq.newBuilder().setCurrentUser(user).setFileId(fileId).setFileStatus(fileStatus).setRemark("处置车辆导出完成").build());
            }
        });
    }

}
