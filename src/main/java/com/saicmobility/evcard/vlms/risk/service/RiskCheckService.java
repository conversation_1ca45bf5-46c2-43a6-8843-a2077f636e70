package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface RiskCheckService {
    /**
     * 查询风控收车任务列表
     * @param queryRiskCheckReq
     * @return
     */
    QueryRiskCheckRes queryRiskCheck(QueryRiskCheckReq queryRiskCheckReq);

    /**
     * 查询风控收车任务详情
     * @param queryRiskCheckDetailReq
     * @return
     */
    QueryRiskCheckDetailRes queryRiskCheckDetail(QueryRiskCheckDetailReq queryRiskCheckDetailReq);

    /**
     * 升级失控
     * @param upgradeRiskCheckReq
     * @return
     */

    UpgradeRiskCheckRes upgradeRiskCheck(UpgradeRiskCheckReq upgradeRiskCheckReq);

    /**
     * 完成风控收车任务
     * @param completeRiskCheckReq
     * @return
     */
    CompleteRiskCheckRes completeRiskCheck(CompleteRiskCheckReq completeRiskCheckReq);

    /**
     * 取消风控收车任务
     * @param cancelRiskCheckReq
     * @return
     */
    CancelRiskCheckRes cancelRiskCheck(CancelRiskCheckReq cancelRiskCheckReq);

    /**
     * 创建收车任务逻辑
     */
    void autoCreateRiskCheck(RiskAlarm riskAlarm, CurrentUser currentUser);

    /**
     * 导出
     * @param queryRiskCheckReq
     * @return
     */
    QueryRiskCheckRes exportRiskCheck(QueryRiskCheckReq queryRiskCheckReq);

    /**
     * 完成任务-提交白杨审批
     */
    CompleteRiskCheckRes submitRiskCheckToBaiYang(CompleteRiskCheckReq completeRiskCheckReq);

    /**
     * 完成任务-白杨回调
     */
    SubmitRiskCheckApprovalResultRes submitRiskCheckApprovalResult(SubmitRiskCheckApprovalResultReq submitRiskCheckApprovalResultReq);

    /**
     * 完成任务-白杨审批通过
     * @param applicationId
     */
    void submitRiskCheckApprovalPass(String applicationId);

    /**
     * 完成任务-白杨审批拒绝
     * @param applicationId
     */
    void submitRiskCheckApprovalReject(String applicationId);
}
