package com.saicmobility.evcard.vlms.risk.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆风控白名单表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_risk_whitelist_config
 */
public class RiskWhitelistConfig implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   生效状态 1-进行中 2-已过期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.effective_status")
    private Integer effectiveStatus;

    /**
     * Database Column Remarks:
     *   报警类型（1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.alarm_type")
    private Integer alarmType;

    /**
     * Database Column Remarks:
     *   有效期截止日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.expiration_date")
    private Date expirationDate;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_account")
    private String updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_whitelist_config")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.effective_status")
    public Integer getEffectiveStatus() {
        return effectiveStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.effective_status")
    public void setEffectiveStatus(Integer effectiveStatus) {
        this.effectiveStatus = effectiveStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.alarm_type")
    public Integer getAlarmType() {
        return alarmType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.alarm_type")
    public void setAlarmType(Integer alarmType) {
        this.alarmType = alarmType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.expiration_date")
    public Date getExpirationDate() {
        return expirationDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.expiration_date")
    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_whitelist_config.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}