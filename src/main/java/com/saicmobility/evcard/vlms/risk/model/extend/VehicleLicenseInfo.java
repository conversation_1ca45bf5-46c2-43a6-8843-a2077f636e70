package com.saicmobility.evcard.vlms.risk.model.extend;

import javax.annotation.Generated;
import java.io.Serializable;
import java.util.Date;

/**
 * Database Table Remarks:
 *   车辆行驶证信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table vlms_assets.t_vehicle_license_info
 */
public class VehicleLicenseInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   自增主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_no")
    private String plateNo;

    /**
     * Database Column Remarks:
     *   使用性质 0-租赁，1-非营运 2-预约出租客运 3-营转非 4-营运 5-预约出租转非
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.usage_property")
    private Integer usageProperty;

    /**
     * Database Column Remarks:
     *   注册日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.register_date")
    private Date registerDate;

    /**
     * Database Column Remarks:
     *   发证日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.certificate_date")
    private Date certificateDate;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_date")
    private Date plateDate;

    /**
     * Database Column Remarks:
     *   检测有效期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.inspection_expire_date")
    private Date inspectionExpireDate;

    /**
     * Database Column Remarks:
     *   行驶证正页地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_front_page_url")
    private String drivingLicenseFrontPageUrl;

    /**
     * Database Column Remarks:
     *   行驶证附页地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_attached_page_url")
    private String drivingLicenseAttachedPageUrl;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_account")
    private String createOperAccount;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_account")
    private String updateOperAccount;

    /**
     * Database Column Remarks:
     *   修改人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_name")
    private String updateOperName;

    /**
     * Database Column Remarks:
     *   系统同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_update_time")
    private Date systemUpdateTime;

    /**
     * Database Column Remarks:
     *   系统同步编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_sync_code")
    private String systemSyncCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_vehicle_license_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_no")
    public String getPlateNo() {
        return plateNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_no")
    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo == null ? null : plateNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.usage_property")
    public Integer getUsageProperty() {
        return usageProperty;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.usage_property")
    public void setUsageProperty(Integer usageProperty) {
        this.usageProperty = usageProperty;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.register_date")
    public Date getRegisterDate() {
        return registerDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.register_date")
    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.certificate_date")
    public Date getCertificateDate() {
        return certificateDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.certificate_date")
    public void setCertificateDate(Date certificateDate) {
        this.certificateDate = certificateDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_date")
    public Date getPlateDate() {
        return plateDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.plate_date")
    public void setPlateDate(Date plateDate) {
        this.plateDate = plateDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.inspection_expire_date")
    public Date getInspectionExpireDate() {
        return inspectionExpireDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.inspection_expire_date")
    public void setInspectionExpireDate(Date inspectionExpireDate) {
        this.inspectionExpireDate = inspectionExpireDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_front_page_url")
    public String getDrivingLicenseFrontPageUrl() {
        return drivingLicenseFrontPageUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_front_page_url")
    public void setDrivingLicenseFrontPageUrl(String drivingLicenseFrontPageUrl) {
        this.drivingLicenseFrontPageUrl = drivingLicenseFrontPageUrl == null ? null : drivingLicenseFrontPageUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_attached_page_url")
    public String getDrivingLicenseAttachedPageUrl() {
        return drivingLicenseAttachedPageUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.driving_license_attached_page_url")
    public void setDrivingLicenseAttachedPageUrl(String drivingLicenseAttachedPageUrl) {
        this.drivingLicenseAttachedPageUrl = drivingLicenseAttachedPageUrl == null ? null : drivingLicenseAttachedPageUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_account")
    public String getCreateOperAccount() {
        return createOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_account")
    public void setCreateOperAccount(String createOperAccount) {
        this.createOperAccount = createOperAccount == null ? null : createOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_account")
    public String getUpdateOperAccount() {
        return updateOperAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_account")
    public void setUpdateOperAccount(String updateOperAccount) {
        this.updateOperAccount = updateOperAccount == null ? null : updateOperAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_update_time")
    public Date getSystemUpdateTime() {
        return systemUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_update_time")
    public void setSystemUpdateTime(Date systemUpdateTime) {
        this.systemUpdateTime = systemUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_sync_code")
    public String getSystemSyncCode() {
        return systemSyncCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_vehicle_license_info.system_sync_code")
    public void setSystemSyncCode(String systemSyncCode) {
        this.systemSyncCode = systemSyncCode == null ? null : systemSyncCode.trim();
    }
}