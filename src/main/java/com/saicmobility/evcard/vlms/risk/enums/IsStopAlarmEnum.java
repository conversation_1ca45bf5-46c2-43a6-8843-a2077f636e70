package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 是否暂停报警（1-是 0-否）
 * <AUTHOR>
 * @date 2024-01-31
 */
@AllArgsConstructor
@Getter
public enum IsStopAlarmEnum {
    OVER(2,"结束"),
    YES(1,"是"),
    NO(0,"否");

    Integer code;
    String value;
    public static String getValueByCode(Integer code){
        for (IsStopAlarmEnum item : IsStopAlarmEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
