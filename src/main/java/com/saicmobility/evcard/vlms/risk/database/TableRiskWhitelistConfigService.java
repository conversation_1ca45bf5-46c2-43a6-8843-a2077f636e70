package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.dto.whitelist.RiskWhitelistConfigData;
import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryWhiteListReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface TableRiskWhitelistConfigService {

    /**
     * 根据ID查询
     */
    RiskWhitelistConfig selectById(Long id);

    /**
     * 查询列表
     * @param queryWhiteListReq
     * @return
     */
    List<RiskWhitelistConfigData> selectList(QueryWhiteListReq queryWhiteListReq);

    /**
     * 查询总条数
     * @param queryWhiteListReq
     * @return
     */
    Long selectTotal(QueryWhiteListReq queryWhiteListReq);

    /**
     * 新增
     * @param riskWhitelistConfig
     */
    void insert(RiskWhitelistConfig riskWhitelistConfig);

    /**
     * 更新
     * @param riskWhitelistConfig
     */
    void updateById(RiskWhitelistConfig riskWhitelistConfig);


    /**
     * 查询
     * @param riskWhitelistConfig
     */
    RiskWhitelistConfig selectConfig(RiskWhitelistConfig riskWhitelistConfig);

    /**
     * 查询白名单
     */
    List<RiskWhitelistConfig> selectAll(RiskWhitelistConfig riskWhitelistConfig);
}
