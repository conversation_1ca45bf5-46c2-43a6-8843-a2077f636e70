package com.saicmobility.evcard.vlms.risk.mapper;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface RiskAlarmMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    BasicColumn[] selectList = BasicColumn.columnList(id, alarmNo, vin, orderSeq, orderType, alarmType, alarmLevel, alarmStatus, recoverMethod, recoverChannel, alarmSystem, alarmTime, lastLocation, lastLocationTime, isStopAlarm, stopAlarmDay, pauseDeadlineTime, recoveryDate, alarmDesc, upgradeDesc, exitFenceDate, isDeleted, miscDesc, createTime, createOperAccount, createOperName, updateTime, updateOperAccount, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<RiskAlarm> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="RiskAlarmResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="alarm_no", property="alarmNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="order_seq", property="orderSeq", jdbcType=JdbcType.VARCHAR),
        @Result(column="order_type", property="orderType", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_type", property="alarmType", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_level", property="alarmLevel", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_status", property="alarmStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="recover_method", property="recoverMethod", jdbcType=JdbcType.INTEGER),
        @Result(column="recover_channel", property="recoverChannel", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_system", property="alarmSystem", jdbcType=JdbcType.INTEGER),
        @Result(column="alarm_time", property="alarmTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="last_location", property="lastLocation", jdbcType=JdbcType.VARCHAR),
        @Result(column="last_location_time", property="lastLocationTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_stop_alarm", property="isStopAlarm", jdbcType=JdbcType.INTEGER),
        @Result(column="stop_alarm_day", property="stopAlarmDay", jdbcType=JdbcType.INTEGER),
        @Result(column="pause_deadline_time", property="pauseDeadlineTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="recovery_date", property="recoveryDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="alarm_desc", property="alarmDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="upgrade_desc", property="upgradeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="exit_fence_date", property="exitFenceDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_account", property="updateOperAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<RiskAlarm> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("RiskAlarmResult")
    Optional<RiskAlarm> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, riskAlarm, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, riskAlarm, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int insert(RiskAlarm row) {
        return MyBatis3Utils.insert(this::insert, row, riskAlarm, c ->
            c.map(alarmNo).toProperty("alarmNo")
            .map(vin).toProperty("vin")
            .map(orderSeq).toProperty("orderSeq")
            .map(orderType).toProperty("orderType")
            .map(alarmType).toProperty("alarmType")
            .map(alarmLevel).toProperty("alarmLevel")
            .map(alarmStatus).toProperty("alarmStatus")
            .map(recoverMethod).toProperty("recoverMethod")
            .map(recoverChannel).toProperty("recoverChannel")
            .map(alarmSystem).toProperty("alarmSystem")
            .map(alarmTime).toProperty("alarmTime")
            .map(lastLocation).toProperty("lastLocation")
            .map(lastLocationTime).toProperty("lastLocationTime")
            .map(isStopAlarm).toProperty("isStopAlarm")
            .map(stopAlarmDay).toProperty("stopAlarmDay")
            .map(pauseDeadlineTime).toProperty("pauseDeadlineTime")
            .map(recoveryDate).toProperty("recoveryDate")
            .map(alarmDesc).toProperty("alarmDesc")
            .map(upgradeDesc).toProperty("upgradeDesc")
            .map(exitFenceDate).toProperty("exitFenceDate")
            .map(isDeleted).toProperty("isDeleted")
            .map(miscDesc).toProperty("miscDesc")
            .map(createTime).toProperty("createTime")
            .map(createOperAccount).toProperty("createOperAccount")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperAccount).toProperty("updateOperAccount")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int insertSelective(RiskAlarm row) {
        return MyBatis3Utils.insert(this::insert, row, riskAlarm, c ->
            c.map(alarmNo).toPropertyWhenPresent("alarmNo", row::getAlarmNo)
            .map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(orderSeq).toPropertyWhenPresent("orderSeq", row::getOrderSeq)
            .map(orderType).toPropertyWhenPresent("orderType", row::getOrderType)
            .map(alarmType).toPropertyWhenPresent("alarmType", row::getAlarmType)
            .map(alarmLevel).toPropertyWhenPresent("alarmLevel", row::getAlarmLevel)
            .map(alarmStatus).toPropertyWhenPresent("alarmStatus", row::getAlarmStatus)
            .map(recoverMethod).toPropertyWhenPresent("recoverMethod", row::getRecoverMethod)
            .map(recoverChannel).toPropertyWhenPresent("recoverChannel", row::getRecoverChannel)
            .map(alarmSystem).toPropertyWhenPresent("alarmSystem", row::getAlarmSystem)
            .map(alarmTime).toPropertyWhenPresent("alarmTime", row::getAlarmTime)
            .map(lastLocation).toPropertyWhenPresent("lastLocation", row::getLastLocation)
            .map(lastLocationTime).toPropertyWhenPresent("lastLocationTime", row::getLastLocationTime)
            .map(isStopAlarm).toPropertyWhenPresent("isStopAlarm", row::getIsStopAlarm)
            .map(stopAlarmDay).toPropertyWhenPresent("stopAlarmDay", row::getStopAlarmDay)
            .map(pauseDeadlineTime).toPropertyWhenPresent("pauseDeadlineTime", row::getPauseDeadlineTime)
            .map(recoveryDate).toPropertyWhenPresent("recoveryDate", row::getRecoveryDate)
            .map(alarmDesc).toPropertyWhenPresent("alarmDesc", row::getAlarmDesc)
            .map(upgradeDesc).toPropertyWhenPresent("upgradeDesc", row::getUpgradeDesc)
            .map(exitFenceDate).toPropertyWhenPresent("exitFenceDate", row::getExitFenceDate)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", row::getIsDeleted)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createOperAccount).toPropertyWhenPresent("createOperAccount", row::getCreateOperAccount)
            .map(createOperName).toPropertyWhenPresent("createOperName", row::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateOperAccount).toPropertyWhenPresent("updateOperAccount", row::getUpdateOperAccount)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", row::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default Optional<RiskAlarm> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, riskAlarm, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default List<RiskAlarm> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, riskAlarm, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default List<RiskAlarm> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, riskAlarm, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default Optional<RiskAlarm> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, riskAlarm, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    static UpdateDSL<UpdateModel> updateAllColumns(RiskAlarm row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(alarmNo).equalTo(row::getAlarmNo)
                .set(vin).equalTo(row::getVin)
                .set(orderSeq).equalTo(row::getOrderSeq)
                .set(orderType).equalTo(row::getOrderType)
                .set(alarmType).equalTo(row::getAlarmType)
                .set(alarmLevel).equalTo(row::getAlarmLevel)
                .set(alarmStatus).equalTo(row::getAlarmStatus)
                .set(recoverMethod).equalTo(row::getRecoverMethod)
                .set(recoverChannel).equalTo(row::getRecoverChannel)
                .set(alarmSystem).equalTo(row::getAlarmSystem)
                .set(alarmTime).equalTo(row::getAlarmTime)
                .set(lastLocation).equalTo(row::getLastLocation)
                .set(lastLocationTime).equalTo(row::getLastLocationTime)
                .set(isStopAlarm).equalTo(row::getIsStopAlarm)
                .set(stopAlarmDay).equalTo(row::getStopAlarmDay)
                .set(pauseDeadlineTime).equalTo(row::getPauseDeadlineTime)
                .set(recoveryDate).equalTo(row::getRecoveryDate)
                .set(alarmDesc).equalTo(row::getAlarmDesc)
                .set(upgradeDesc).equalTo(row::getUpgradeDesc)
                .set(exitFenceDate).equalTo(row::getExitFenceDate)
                .set(isDeleted).equalTo(row::getIsDeleted)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createOperAccount).equalTo(row::getCreateOperAccount)
                .set(createOperName).equalTo(row::getCreateOperName)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
                .set(updateOperName).equalTo(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(RiskAlarm row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(alarmNo).equalToWhenPresent(row::getAlarmNo)
                .set(vin).equalToWhenPresent(row::getVin)
                .set(orderSeq).equalToWhenPresent(row::getOrderSeq)
                .set(orderType).equalToWhenPresent(row::getOrderType)
                .set(alarmType).equalToWhenPresent(row::getAlarmType)
                .set(alarmLevel).equalToWhenPresent(row::getAlarmLevel)
                .set(alarmStatus).equalToWhenPresent(row::getAlarmStatus)
                .set(recoverMethod).equalToWhenPresent(row::getRecoverMethod)
                .set(recoverChannel).equalToWhenPresent(row::getRecoverChannel)
                .set(alarmSystem).equalToWhenPresent(row::getAlarmSystem)
                .set(alarmTime).equalToWhenPresent(row::getAlarmTime)
                .set(lastLocation).equalToWhenPresent(row::getLastLocation)
                .set(lastLocationTime).equalToWhenPresent(row::getLastLocationTime)
                .set(isStopAlarm).equalToWhenPresent(row::getIsStopAlarm)
                .set(stopAlarmDay).equalToWhenPresent(row::getStopAlarmDay)
                .set(pauseDeadlineTime).equalToWhenPresent(row::getPauseDeadlineTime)
                .set(recoveryDate).equalToWhenPresent(row::getRecoveryDate)
                .set(alarmDesc).equalToWhenPresent(row::getAlarmDesc)
                .set(upgradeDesc).equalToWhenPresent(row::getUpgradeDesc)
                .set(exitFenceDate).equalToWhenPresent(row::getExitFenceDate)
                .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
                .set(createOperName).equalToWhenPresent(row::getCreateOperName)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
                .set(updateOperName).equalToWhenPresent(row::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int updateByPrimaryKey(RiskAlarm row) {
        return update(c ->
            c.set(alarmNo).equalTo(row::getAlarmNo)
            .set(vin).equalTo(row::getVin)
            .set(orderSeq).equalTo(row::getOrderSeq)
            .set(orderType).equalTo(row::getOrderType)
            .set(alarmType).equalTo(row::getAlarmType)
            .set(alarmLevel).equalTo(row::getAlarmLevel)
            .set(alarmStatus).equalTo(row::getAlarmStatus)
            .set(recoverMethod).equalTo(row::getRecoverMethod)
            .set(recoverChannel).equalTo(row::getRecoverChannel)
            .set(alarmSystem).equalTo(row::getAlarmSystem)
            .set(alarmTime).equalTo(row::getAlarmTime)
            .set(lastLocation).equalTo(row::getLastLocation)
            .set(lastLocationTime).equalTo(row::getLastLocationTime)
            .set(isStopAlarm).equalTo(row::getIsStopAlarm)
            .set(stopAlarmDay).equalTo(row::getStopAlarmDay)
            .set(pauseDeadlineTime).equalTo(row::getPauseDeadlineTime)
            .set(recoveryDate).equalTo(row::getRecoveryDate)
            .set(alarmDesc).equalTo(row::getAlarmDesc)
            .set(upgradeDesc).equalTo(row::getUpgradeDesc)
            .set(exitFenceDate).equalTo(row::getExitFenceDate)
            .set(isDeleted).equalTo(row::getIsDeleted)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createOperAccount).equalTo(row::getCreateOperAccount)
            .set(createOperName).equalTo(row::getCreateOperName)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateOperAccount).equalTo(row::getUpdateOperAccount)
            .set(updateOperName).equalTo(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_alarm")
    default int updateByPrimaryKeySelective(RiskAlarm row) {
        return update(c ->
            c.set(alarmNo).equalToWhenPresent(row::getAlarmNo)
            .set(vin).equalToWhenPresent(row::getVin)
            .set(orderSeq).equalToWhenPresent(row::getOrderSeq)
            .set(orderType).equalToWhenPresent(row::getOrderType)
            .set(alarmType).equalToWhenPresent(row::getAlarmType)
            .set(alarmLevel).equalToWhenPresent(row::getAlarmLevel)
            .set(alarmStatus).equalToWhenPresent(row::getAlarmStatus)
            .set(recoverMethod).equalToWhenPresent(row::getRecoverMethod)
            .set(recoverChannel).equalToWhenPresent(row::getRecoverChannel)
            .set(alarmSystem).equalToWhenPresent(row::getAlarmSystem)
            .set(alarmTime).equalToWhenPresent(row::getAlarmTime)
            .set(lastLocation).equalToWhenPresent(row::getLastLocation)
            .set(lastLocationTime).equalToWhenPresent(row::getLastLocationTime)
            .set(isStopAlarm).equalToWhenPresent(row::getIsStopAlarm)
            .set(stopAlarmDay).equalToWhenPresent(row::getStopAlarmDay)
            .set(pauseDeadlineTime).equalToWhenPresent(row::getPauseDeadlineTime)
            .set(recoveryDate).equalToWhenPresent(row::getRecoveryDate)
            .set(alarmDesc).equalToWhenPresent(row::getAlarmDesc)
            .set(upgradeDesc).equalToWhenPresent(row::getUpgradeDesc)
            .set(exitFenceDate).equalToWhenPresent(row::getExitFenceDate)
            .set(isDeleted).equalToWhenPresent(row::getIsDeleted)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createOperAccount).equalToWhenPresent(row::getCreateOperAccount)
            .set(createOperName).equalToWhenPresent(row::getCreateOperName)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateOperAccount).equalToWhenPresent(row::getUpdateOperAccount)
            .set(updateOperName).equalToWhenPresent(row::getUpdateOperName)
            .where(id, isEqualTo(row::getId))
        );
    }
}