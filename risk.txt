报警类型 1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失
报警状态 1、报警中 2、已恢复
恢复方式 1、人工 2、自动
车辆风险状态 0-无风险 1-风险车辆 2-风控车辆 3-失控车辆
处理状态 1-执行中 2-已执行
生效状态 1-进行中 2-已过期


// 车辆阶段 1 入库阶段(1.6K)、2 固资运营阶段(2W+)、3 固资退运阶段(600)、4 处置阶段(5W+)
        int vehiclePhase = req.getVehiclePhase();
        if (vehiclePhase == 1){
            qew.and(assetsVehicle.propertyStatus,isEqualTo(0));
        } else if (vehiclePhase == 2){
            qew.and(assetsVehicle.propertyStatus,isIn(1,6)).and(assetsVehicle.putInStatus,isEqualTo(2));
        } else if (vehiclePhase == 3){
            qew.and(assetsVehicle.propertyStatus,isIn(1,6)).and(assetsVehicle.putInStatus,isIn(1,3));
        } else if (vehiclePhase == 4){
            qew.and(assetsVehicle.propertyStatus,isIn(2,3,4,5,8));
        }