package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.dto.riskAlarmConfig.RiskAlarmConfigData;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskAlarmConfigReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-17
 */
public interface TableRiskAlarmConfigService {

    /**
     * 查询列表
     * @param req
     * @return
     */
    List<RiskAlarmConfigData> selectList(QueryRiskAlarmConfigReq req);

    /**
     * 查询总数
     * @param req
     * @return
     */
    long selectTotal(QueryRiskAlarmConfigReq req);

    /**
     * 报警配置编辑
     * @param riskAlarmConfig
     */
    void updateById(RiskAlarmConfig riskAlarmConfig);

    /**
     * id 查询
     * @param id
     */
    RiskAlarmConfig selectById(Long id);

    /**
     * 报警配置编辑
     * @param riskAlarmConfig
     */
    void updateRiskAlarmConfig(RiskAlarmConfig riskAlarmConfig);

    /**
     * 查询配置
     * @param riskAlarmConfig
     */
    RiskAlarmConfig selectRiskAlarmConfig(RiskAlarmConfig riskAlarmConfig);

    /**
     * 根据车架号修改配置
     * @param vin
     */
    void updateByVin(String vin);

    /**
     * 查询配置
     * @param vin
     * @param alarmType
     */
    RiskAlarmConfig selectRiskAlarmConfig(String vin, Integer alarmType);

    /**
     * 新增配置
     * @param riskAlarmConfig
     */
    void insert(RiskAlarmConfig riskAlarmConfig);

    /**
     * 根据车架号分页查询
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<RiskAlarmConfig> selectConfigGroupByVin(int pageNum, Integer pageSize);

    /**
     * 根据车架号删除
     * @param deleteVinList
     */
    void deleteByVinList(List<String> deleteVinList);
}
