package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class OperationVehicleDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_operation_vehicle")
    public static final OperationVehicle operationVehicle = new OperationVehicle();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.id")
    public static final SqlColumn<Long> id = operationVehicle.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.vin")
    public static final SqlColumn<String> vin = operationVehicle.vin;

    /**
     * Database Column Remarks:
     *   产品线 1:车管中心 2:长租 3:短租 4:公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.product_line")
    public static final SqlColumn<Integer> productLine = operationVehicle.productLine;

    /**
     * Database Column Remarks:
     *   子产品线 1:携程短租-短租 2:门店-短租 3.分时-短租 4.普通-长租 5.时行-长租
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.sub_product_line")
    public static final SqlColumn<Integer> subProductLine = operationVehicle.subProductLine;

    /**
     * Database Column Remarks:
     *   运营状态 1:待运营 2:运营中 3:审批中
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.operate_status")
    public static final SqlColumn<Integer> operateStatus = operationVehicle.operateStatus;

    /**
     * Database Column Remarks:
     *   审批类型 1投运 2停运 3调拨 4退运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.approval_type")
    public static final SqlColumn<Integer> approvalType = operationVehicle.approvalType;

    /**
     * Database Column Remarks:
     *   审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.approval_no")
    public static final SqlColumn<String> approvalNo = operationVehicle.approvalNo;

    /**
     * Database Column Remarks:
     *   总里程
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.total_mileage")
    public static final SqlColumn<Double> totalMileage = operationVehicle.totalMileage;

    /**
     * Database Column Remarks:
     *   风险状态 0.无风险 1.风险预警 2.风控收车 3.失控车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.risk_status")
    public static final SqlColumn<Integer> riskStatus = operationVehicle.riskStatus;

    /**
     * Database Column Remarks:
     *   车辆状态：0:待运 1:预定 2:租出 3:调度中 4:故障 5:事故 6:升级 7：重启 8：封存 9：停运 10：风控 11：退运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.service_status")
    public static final SqlColumn<Integer> serviceStatus = operationVehicle.serviceStatus;

    /**
     * Database Column Remarks:
     *   门店id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.store_id")
    public static final SqlColumn<Long> storeId = operationVehicle.storeId;

    /**
     * Database Column Remarks:
     *   门店编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.store_code")
    public static final SqlColumn<String> storeCode = operationVehicle.storeCode;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.is_deleted")
    public static final SqlColumn<Integer> isDeleted = operationVehicle.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.create_time")
    public static final SqlColumn<Date> createTime = operationVehicle.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.create_oper_account")
    public static final SqlColumn<String> createOperAccount = operationVehicle.createOperAccount;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.create_oper_name")
    public static final SqlColumn<String> createOperName = operationVehicle.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.update_time")
    public static final SqlColumn<Date> updateTime = operationVehicle.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = operationVehicle.updateOperAccount;

    /**
     * Database Column Remarks:
     *   修改人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.update_oper_name")
    public static final SqlColumn<String> updateOperName = operationVehicle.updateOperName;

    /**
     * Database Column Remarks:
     *   系统同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.system_update_time")
    public static final SqlColumn<Date> systemUpdateTime = operationVehicle.systemUpdateTime;

    /**
     * Database Column Remarks:
     *   系统同步编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_operation_vehicle.system_sync_code")
    public static final SqlColumn<String> systemSyncCode = operationVehicle.systemSyncCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_operation_vehicle")
    public static final class OperationVehicle extends AliasableSqlTable<OperationVehicle> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> subProductLine = column("sub_product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> operateStatus = column("operate_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> approvalType = column("approval_type", JDBCType.INTEGER);

        public final SqlColumn<String> approvalNo = column("approval_no", JDBCType.VARCHAR);

        public final SqlColumn<Double> totalMileage = column("total_mileage", JDBCType.DOUBLE);

        public final SqlColumn<Integer> riskStatus = column("risk_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> serviceStatus = column("service_status", JDBCType.INTEGER);

        public final SqlColumn<Long> storeId = column("store_id", JDBCType.BIGINT);

        public final SqlColumn<String> storeCode = column("store_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> systemUpdateTime = column("system_update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> systemSyncCode = column("system_sync_code", JDBCType.VARCHAR);

        public OperationVehicle() {
            super("vlms_assets.t_operation_vehicle", OperationVehicle::new);
        }
    }
}