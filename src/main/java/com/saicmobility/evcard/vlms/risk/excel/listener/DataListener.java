package com.saicmobility.evcard.vlms.risk.excel.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import krpc.trace.Trace;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel监听器
 *
 * <AUTHOR>
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class DataListener<T> extends AnalysisEventListener<T> {

    /**
     * 缓存的数据列表
     */
    private final List<T> dataList = new ArrayList<>();

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info(StrUtil.format("tId^{} 读取excel成功", Trace.currentTraceId()));
    }

}
