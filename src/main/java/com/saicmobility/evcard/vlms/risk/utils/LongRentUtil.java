package com.saicmobility.evcard.vlms.risk.utils;

import com.saicmobility.evcard.vlms.risk.enums.ProductLineEnum;

/**
 * 风控对接长租工具类
 */
public class LongRentUtil {

    /**
     * 梧桐风控工单同步至长租系统接口
     */
    private static final String NOTIFY_RISK_WORK_ORDER = "notifyRiskWorkOrder";

    /**
     * 梧桐关闭风控工单并同步至长租系统
     */
    private static final String NOTIFY_CANCELS_RISK_WARNING = "notifyCancelsRiskWarning";

    private LongRentUtil(){
    }






}
