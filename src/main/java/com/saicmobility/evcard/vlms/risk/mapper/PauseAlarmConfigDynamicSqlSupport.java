package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class PauseAlarmConfigDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_pause_alarm_config")
    public static final PauseAlarmConfig pauseAlarmConfig = new PauseAlarmConfig();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.id")
    public static final SqlColumn<Long> id = pauseAlarmConfig.id;

    /**
     * Database Column Remarks:
     *   报警类型（1、超出电子围栏 2、GPS信号离线 3、逾期未还车 4、车辆异动 5、年检临期 6、保险临期 7、车辆被扣 8、经营风险 9、车辆失联 10、车辆灭失）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.alarm_type")
    public static final SqlColumn<Integer> alarmType = pauseAlarmConfig.alarmType;

    /**
     * Database Column Remarks:
     *   暂停时长最长
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.max_pause_day")
    public static final SqlColumn<Integer> maxPauseDay = pauseAlarmConfig.maxPauseDay;

    /**
     * Database Column Remarks:
     *   报警开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.alarm_start_time")
    public static final SqlColumn<Date> alarmStartTime = pauseAlarmConfig.alarmStartTime;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.is_deleted")
    public static final SqlColumn<Integer> isDeleted = pauseAlarmConfig.isDeleted;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.misc_desc")
    public static final SqlColumn<String> miscDesc = pauseAlarmConfig.miscDesc;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.create_time")
    public static final SqlColumn<Date> createTime = pauseAlarmConfig.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.create_oper_account")
    public static final SqlColumn<String> createOperAccount = pauseAlarmConfig.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.create_oper_name")
    public static final SqlColumn<String> createOperName = pauseAlarmConfig.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.update_time")
    public static final SqlColumn<Date> updateTime = pauseAlarmConfig.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = pauseAlarmConfig.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_pause_alarm_config.update_oper_name")
    public static final SqlColumn<String> updateOperName = pauseAlarmConfig.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_pause_alarm_config")
    public static final class PauseAlarmConfig extends AliasableSqlTable<PauseAlarmConfig> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> alarmType = column("alarm_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> maxPauseDay = column("max_pause_day", JDBCType.INTEGER);

        public final SqlColumn<Date> alarmStartTime = column("alarm_start_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public PauseAlarmConfig() {
            super("t_pause_alarm_config", PauseAlarmConfig::new);
        }
    }
}