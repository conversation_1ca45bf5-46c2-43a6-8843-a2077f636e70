package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @description 子产品线
 **/
@AllArgsConstructor
@Getter
public enum SubProductLineEnum {
    SUB_PRODUCT_LINE_ENUM_1(1, "携程短租-短租"),
    SUB_PRODUCT_LINE_ENUM_2(2, "门店-短租"),
    SUB_PRODUCT_LINE_ENUM_3(3, "分时-短租"),
    SUB_PRODUCT_LINE_ENUM_4(4, "普通-长租"),
    SUB_PRODUCT_LINE_ENUM_5(5, "时行-长租"),
    SUB_PRODUCT_LINE_ENUM_6(6, "平台-长租"),
    SUB_PRODUCT_LINE_ENUM_7(7, "政企-长租"),
    SUB_PRODUCT_LINE_ENUM_8(8, "网约车-长租"),
    SUB_PRODUCT_LINE_ENUM_9(9, "公务用车-公务车"),
    ;

    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (SubProductLineEnum item : SubProductLineEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
