package com.saicmobility.evcard.vlms.risk.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class VehicleMovementDayStatisticsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_movement_day_statistics")
    public static final VehicleMovementDayStatistics vehicleMovementDayStatistics = new VehicleMovementDayStatistics();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.id")
    public static final SqlColumn<Long> id = vehicleMovementDayStatistics.id;

    /**
     * Database Column Remarks:
     *   统计日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.stat_date")
    public static final SqlColumn<String> statDate = vehicleMovementDayStatistics.statDate;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.vin")
    public static final SqlColumn<String> vin = vehicleMovementDayStatistics.vin;

    /**
     * Database Column Remarks:
     *   异动日里程
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.mileage_day")
    public static final SqlColumn<BigDecimal> mileageDay = vehicleMovementDayStatistics.mileageDay;

    /**
     * Database Column Remarks:
     *   删除状态 0-否,1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleMovementDayStatistics.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.create_time")
    public static final SqlColumn<Date> createTime = vehicleMovementDayStatistics.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.create_oper_account")
    public static final SqlColumn<String> createOperAccount = vehicleMovementDayStatistics.createOperAccount;

    /**
     * Database Column Remarks:
     *    创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleMovementDayStatistics.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.update_time")
    public static final SqlColumn<Date> updateTime = vehicleMovementDayStatistics.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = vehicleMovementDayStatistics.updateOperAccount;

    /**
     * Database Column Remarks:
     *   更新人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_movement_day_statistics.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleMovementDayStatistics.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_movement_day_statistics")
    public static final class VehicleMovementDayStatistics extends AliasableSqlTable<VehicleMovementDayStatistics> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> statDate = column("stat_date", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> mileageDay = column("mileage_day", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleMovementDayStatistics() {
            super("t_vehicle_movement_day_statistics", VehicleMovementDayStatistics::new);
        }
    }
}