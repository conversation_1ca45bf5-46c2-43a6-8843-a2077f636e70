package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.risk.dto.VehicleGPSData;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-02-24
 */
public interface RiskCommonService {

    /**
     * 判断车辆该报警类型是否在白名单中
     * @param vin
     * @return
     */
    public boolean isWhiteList(String vin, Integer alarmType);

    /**
     * 更新风险告警配置
     * @param vin
     * @param alarmType
     * @param date
     * @param pauseDay
     * @param pauseDeadlineTime
     * @param currentUser
     */
    public Long updateRiskAlarmConfig(String vin, Integer alarmType, Date date, Integer pauseDay, Date pauseDeadlineTime, CurrentUser currentUser);

    /**
     * 新增告警
     * @param assetsVehicle
     * @param vehicleGPSData
     * @param alarmLevel
     * @param alarmType
     */
    public void addRiskAlarm(SearchVehicleFileList assetsVehicle, VehicleGPSData vehicleGPSData, Integer alarmLevel, Integer alarmType, Integer vehicleRiskStatus);

    /**
     * 升级告警/恢复告警
     * @param flag true:升级，false:恢复
     * @param riskAlarm
     * @param vehicleGPSData
     */
    public void updateRiskAlarm(Boolean flag, RiskAlarm riskAlarm,VehicleGPSData vehicleGPSData,Integer productLine);

    /**
     * 获取车辆GPS数据
     * @param vin
     * @return
     */
    VehicleGPSData fetchGpsDate(String vin);


    /**
     * 查询最大的风险等级
     * @param vin
     */
    Integer queryMaxVehicleRiskStatus(String vin, String alarmNo);

    /**
     *  （1）车辆只有“GPS信号离线”或“超出电子围栏”一个预警任务，这条唯一的预警恢复后，该车如果有线下核查任务，则自动完成。
     *  （2）车辆有“逾期未还车”风险预警任务，不论是否有其他预警，“逾期未还车”预警恢复后，该车如果有线下核查任务，则自动完成。
     * @param riskAlarm
     */
    public void autoCompleteSingleVehiclePatrolTask(RiskAlarm riskAlarm);
}
