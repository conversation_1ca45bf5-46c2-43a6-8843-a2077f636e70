package com.saicmobility.evcard.vlms.risk.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class RiskCheckCompleteApproveDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    public static final RiskCheckCompleteApprove riskCheckCompleteApprove = new RiskCheckCompleteApprove();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.id")
    public static final SqlColumn<Long> id = riskCheckCompleteApprove.id;

    /**
     * Database Column Remarks:
     *   白杨审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.application_id")
    public static final SqlColumn<String> applicationId = riskCheckCompleteApprove.applicationId;

    /**
     * Database Column Remarks:
     *   审批状态 1-审批中 10-审批完成 20-审批退回 30-审批拒绝 40-撤回
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.approve_status")
    public static final SqlColumn<Integer> approveStatus = riskCheckCompleteApprove.approveStatus;

    /**
     * Database Column Remarks:
     *   请求no（提交审核时）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.request_no")
    public static final SqlColumn<String> requestNo = riskCheckCompleteApprove.requestNo;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.status")
    public static final SqlColumn<Short> status = riskCheckCompleteApprove.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.remark")
    public static final SqlColumn<String> remark = riskCheckCompleteApprove.remark;

    /**
     * Database Column Remarks:
     *   用户名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.user_name")
    public static final SqlColumn<String> userName = riskCheckCompleteApprove.userName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_time")
    public static final SqlColumn<Date> createTime = riskCheckCompleteApprove.createTime;

    /**
     * Database Column Remarks:
     *   创建人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_account")
    public static final SqlColumn<String> createOperAccount = riskCheckCompleteApprove.createOperAccount;

    /**
     * Database Column Remarks:
     *   创建人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.create_oper_name")
    public static final SqlColumn<String> createOperName = riskCheckCompleteApprove.createOperName;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_time")
    public static final SqlColumn<Date> updateTime = riskCheckCompleteApprove.updateTime;

    /**
     * Database Column Remarks:
     *   修改人域账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = riskCheckCompleteApprove.updateOperAccount;

    /**
     * Database Column Remarks:
     *   修改人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_risk_check_complete_approve.update_oper_name")
    public static final SqlColumn<String> updateOperName = riskCheckCompleteApprove.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_risk_check_complete_approve")
    public static final class RiskCheckCompleteApprove extends AliasableSqlTable<RiskCheckCompleteApprove> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> applicationId = column("application_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> approveStatus = column("approve_status", JDBCType.INTEGER);

        public final SqlColumn<String> requestNo = column("request_no", JDBCType.VARCHAR);

        public final SqlColumn<Short> status = column("status", JDBCType.DECIMAL);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> userName = column("user_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public RiskCheckCompleteApprove() {
            super("t_risk_check_complete_approve", RiskCheckCompleteApprove::new);
        }
    }
}