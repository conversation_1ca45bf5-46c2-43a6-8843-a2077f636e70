package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FileSourceEnum {

    FILE_SOURCE_15(15, "风控报警列表"),
    FILE_SOURCE_16(16, "风控收车列表"),
    FILE_SOURCE_17(17, "风控报警配置列表"),
    FILE_SOURCE_18(18, "电子围栏配置列表"),
    FILE_SOURCE_19(19, "风控白名单列表"),
    FILE_SOURCE_25(25, "车辆轨迹列表"),
    ;
    private final Integer source;
    private final String desc;

    public static FileSourceEnum getEnumByType(Integer type){
        for (FileSourceEnum item : FileSourceEnum.values()) {
            if (item.getSource().equals(type)) {
                return item;
            }
        }
        return null;
    }
}
