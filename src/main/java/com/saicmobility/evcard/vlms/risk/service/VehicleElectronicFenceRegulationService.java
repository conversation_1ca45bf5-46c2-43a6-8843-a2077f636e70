package com.saicmobility.evcard.vlms.risk.service;

import com.saicmobility.evcard.vlms.vlmsriskservice.api.*;

/**
 * <AUTHOR>
 * @date 2024-02-23
 */
public interface VehicleElectronicFenceRegulationService {
    /**
     * 查询电子围栏配置
     * @param queryElectronicFenceConfigReq
     * @return
     */
    QueryElectronicFenceConfigRes queryElectronicFenceConfig(QueryElectronicFenceConfigReq queryElectronicFenceConfigReq);

    /**
     * 查询城市
     * @param queryAllCityByProvinceReq
     * @return
     */
    QueryAllCityByProvinceRes queryAllCityByProvince(QueryAllCityByProvinceReq queryAllCityByProvinceReq);

    /**
     * 查询省份
     * @param queryAllProvinceReq
     * @return
     */
    QueryAllProvinceRes queryAllProvince(QueryAllProvinceReq queryAllProvinceReq);

    /**
     * 查询电子围栏日志
     * @param queryElectronicFenceDefaultRegulationLogReq
     * @return
     */
    QueryElectronicFenceDefaultRegulationLogRes queryElectronicFenceDefaultRegulationLog(QueryElectronicFenceDefaultRegulationLogReq queryElectronicFenceDefaultRegulationLogReq);

    /**
     * 删除电子围栏配置
     * @param deleteElectronicFenceConfigReq
     * @return
     */
    DeleteElectronicFenceConfigRes deleteElectronicFenceConfig(DeleteElectronicFenceConfigReq deleteElectronicFenceConfigReq);

    /**
     * 修改电子围栏配置
     * @param updateElectronicFenceConfigReq
     * @return
     */
    UpdateElectronicFenceConfigRes updateElectronicFenceConfig(UpdateElectronicFenceConfigReq updateElectronicFenceConfigReq);

    /**
     * 新增电子围栏配置
     * @param addElectronicFenceConfigListReq
     * @return
     */
    AddElectronicFenceConfigListRes addElectronicFenceConfigList(AddElectronicFenceConfigListReq addElectronicFenceConfigListReq);

    /**
     * 批量操作
     * @param insertOrUpdateBicycleRegulationReq
     * @return
     */
    InsertOrUpdateBicycleRegulationRes insertOrUpdateBicycleRegulation(InsertOrUpdateBicycleRegulationReq insertOrUpdateBicycleRegulationReq);

    /**
     * 导出电子围栏配置
     * @param queryElectronicFenceConfigReq
     * @return
     */
    QueryElectronicFenceConfigRes exportElectronicFenceConfig(QueryElectronicFenceConfigReq queryElectronicFenceConfigReq);

    /**
     * 初始化电子围栏配置信息
     * @param initElectronicFenceConfigReq
     * @return
     */
    InitElectronicFenceConfigRes initElectronicFenceConfig(InitElectronicFenceConfigReq initElectronicFenceConfigReq);

}
