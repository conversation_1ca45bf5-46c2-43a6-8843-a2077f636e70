package com.saicmobility.evcard.vlms.risk.service.sub;

import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.SearchVehicleFileList;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface SubAssetsService {

    SearchVehicleFileList searchAssetsVehicle(String vin);


    /**
     * 根据车辆阶段查询车辆信息
     * @param pageNum
     * @param pageSize
     * @param vehiclePhase
     * @return
     */
    List<SearchVehicleFileList> searchVehicleFileList(Integer pageNum, Integer pageSize, Integer vehiclePhase, String vin);


    /**
     * 根据车架号更新车辆风险状态
     * @param vin
     * @param vehicleRiskStatus
     * @return
     */
    void updateVehicleRiskStatusByVin(String vin, Integer vehicleRiskStatus);
}
