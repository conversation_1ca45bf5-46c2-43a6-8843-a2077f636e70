package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum AlarmSystemEnum {

    /**
     * 报警系统 1-梧桐系统 2-长租系统
     */

    WTXT(1, "梧桐系统"),
    CZXT(2, "长租系统");

    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (AlarmSystemEnum item : AlarmSystemEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
