package com.saicmobility.evcard.vlms.risk.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 投放的白杨审批主数据
 */
@Data
public class DeliverMasterRecord extends BaseMasterRecord {

    private String checkNo;

    private String createTimes;

    private String createOperNameAndAccount;

    private Integer vehicleRiskStatus;

    private String plateNo;
    private String vin;
    private String orgName;
    private String operateOrgName;
    private Integer propertyStatus;
    private Integer productLine;
    private String lastLocation;
    private String lastLocationTime;
    private Integer outControlType;
    private String outControlTime;
    private List<Map<String, Object>> outControlAttachment;
    private String outControlDesc;
    private String collectCarDatetime;
    private String collectCarPlace;
    private String collectCarPeople;
    private Integer collectCarType;
    private Integer collectCarRiskType;
    private List<Map<String, Object>> checkVehicleFormInfo;
    private List<Map<String, Object>> checkVehicleVideoInfo;
    private List<Map<String, Object>> vehicleItemInfo;
    private List<Map<String, Object>> attachmentPaths;
    private String vehicleCheckDesc;

    /**
     * 长租任务编号
     */
    private String rentTaskNo;

    /**
     * 1-是 2-否 自动完成长租收车
     */
    private Integer isAutoComplete;
}
