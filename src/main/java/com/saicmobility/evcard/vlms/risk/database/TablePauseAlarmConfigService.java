package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.model.PauseAlarmConfig;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryPauseAlarmDurationConfigReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-17
 */
public interface TablePauseAlarmConfigService {

    /**
     * 根据id查询
     * @param id
     * @return
     */
    PauseAlarmConfig selectById(Long id);

    /**
     * 根据alarmType查询
     * @param alarmType
     * @return
     */
    PauseAlarmConfig selectByAlarmType(Integer alarmType);

    /**
     * 查询列表
     * @param req
     * @return
     */
    List<PauseAlarmConfig> selectList(QueryPauseAlarmDurationConfigReq req);

    /**
     * 根据id更新
     * @param pauseAlarmConfig
     */
    void updateById(PauseAlarmConfig pauseAlarmConfig);
}
