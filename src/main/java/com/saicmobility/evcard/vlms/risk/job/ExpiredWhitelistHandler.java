package com.saicmobility.evcard.vlms.risk.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskWhitelistConfigService;
import com.saicmobility.evcard.vlms.risk.enums.EffectiveStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskWhitelistConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 定时查询过期白名单
 * <AUTHOR>
 * @date 2024-01-18
 */
@Slf4j
@JobHandler(value = "expiredWhitelist")
@Component
public class ExpiredWhitelistHandler extends IJobHandler {

    @Resource
    private TableRiskWhitelistConfigService tableRiskWhitelistConfigService;

    @Resource
    private TableOperateLogService tableOperateLogService;
    @Override
    public ReturnT<String> execute(String s) {
        log.info("定时查询过期白名单定时任务");
        RiskWhitelistConfig whitelistConfig = new RiskWhitelistConfig();
        whitelistConfig.setEffectiveStatus(EffectiveStatusEnum.EFFECTIVE_STATUS.getCode());
        List<RiskWhitelistConfig> riskWhitelistConfigs = tableRiskWhitelistConfigService.selectAll(whitelistConfig);
        if (CollectionUtil.isEmpty(riskWhitelistConfigs)){
            return ReturnT.SUCCESS;
        }
        for (RiskWhitelistConfig riskWhitelistConfig : riskWhitelistConfigs) {
            // 存的日期是 00:00:00 需要加1天
            DateTime expirationDate = DateUtil.offsetDay(riskWhitelistConfig.getExpirationDate(), 1);
            if (expirationDate.compareTo(new Date()) <= 0){
                riskWhitelistConfig.setEffectiveStatus(EffectiveStatusEnum.EFFECTIVE_RECOVER.getCode());
                tableRiskWhitelistConfigService.updateById(riskWhitelistConfig);

                // 日志
                saveOperateLog("有效期到期，自动过期白名单", riskWhitelistConfig.getId()+"", OperateTypeEnum.OPERATE_WHITE_LIST_CONFIG.getCode());
            }
        }
        return ReturnT.SUCCESS;
    }

    public void saveOperateLog(String operateContent, String relationKey, Integer operateType){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName("定时任务");
        operateLog.setCreateOperAccount("System");

        tableOperateLogService.save(operateLog);
    }
}
