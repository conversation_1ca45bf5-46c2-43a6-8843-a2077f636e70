package com.saicmobility.evcard.vlms.risk.database;

import com.saicmobility.evcard.vlms.risk.dto.check.RiskCheckData;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.model.RiskCheckExtendInfo;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.QueryRiskCheckReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface TableRiskCheckService {
    /**
     * 查询列表
     * @param req
     * @return
     */
    List<RiskCheckData> selectList(QueryRiskCheckReq req);

    /**
     * 查询总数
     * @param req
     * @return
     */
    long selectTotal(QueryRiskCheckReq req);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    RiskCheck selectById(long id);

    /**
     * 更新
     * @param riskCheck
     */
    void update(RiskCheck riskCheck);

    /**
     * 查询
     * @param riskCheck
     * @return
     */
    List<RiskCheck> selectBy(RiskCheck riskCheck);

    /**
     * 根据车架号查询
     * @param vin
     * @return
     */
    List<RiskCheck> selectByVin(String vin, Integer dealStatus);

    /**
     * 新增
     * @param riskCheck
     * @return
     */
    void insert(RiskCheck riskCheck);
    /**
     * 新增
     * @param riskCheckExtend
     * @return
     */
    void insertRiskCheckExtend(RiskCheckExtendInfo riskCheckExtend);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    RiskCheckExtendInfo selectRiskCheckExtendById(long id);

    /**
     * 更新
     * @param riskCheckExtend
     */
    void updateRiskCheckExtend(RiskCheckExtendInfo riskCheckExtend);
}
