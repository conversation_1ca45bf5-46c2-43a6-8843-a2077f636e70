package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 风险操作类型 1-新增 2-升级 3-解除
 * @create: 2020-03-24 10:37
 **/
@AllArgsConstructor
@Getter
public enum RiskOperateEnum {
    OPERATE_ADD(1, "新增"),
    OPERATE_UPGRADE(2, "升级"),
    OPERATE_REMOVE(3, "解除"),
    ;
    final Integer code;
    final String value;
    public static String getValueByCode(Integer code) {
        for (RiskOperateEnum operateTypeEnum : RiskOperateEnum.values()) {
            if (operateTypeEnum.getCode() == code) {
                return operateTypeEnum.getValue();
            }
        }
        return null;
    }

    public static RiskOperateEnum getEnumByCode(Integer code) {
        for (RiskOperateEnum operateTypeEnum : RiskOperateEnum.values()) {
            if (operateTypeEnum.getCode() == code) {
                return operateTypeEnum;
            }
        }
        return null;
    }
}
