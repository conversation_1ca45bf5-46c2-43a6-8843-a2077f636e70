package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 失控状态 1-失联车辆、2-灭失车辆 3-纠纷被扣、4-行政被扣
 * <AUTHOR>
 * @date 2024-01-16
 */
@AllArgsConstructor
@Getter
public enum OutControlTypeEnum {

    VEHICLE_LOSS(1, "失联车辆"),
    VEHICLE_MISSED(2, "灭失车辆"),
    DISPUTE_BE_DEDUCTED(3, "纠纷被扣"),
    ADMINISTRATIVE_BE_DEDUCTED(4, "行政被扣")
    ;

    Integer code;
    String value;

    public static String getValueByCode(Integer code){
        for (OutControlTypeEnum item : OutControlTypeEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
