package com.saicmobility.evcard.vlms.risk.service.sub.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.saicmobility.evcard.md.mddataproxy.api.MdDataProxy;
import com.saicmobility.evcard.md.mddataproxy.api.OperateContractInfo;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsReq;
import com.saicmobility.evcard.md.mddataproxy.api.QueryOperateContractInfoByVinsRes;
import com.saicmobility.evcard.vlms.risk.database.TableRiskAlarmService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskCheckService;
import com.saicmobility.evcard.vlms.risk.database.TableRiskPushRecordService;
import com.saicmobility.evcard.vlms.risk.enums.AlarmStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.DealStatusEnum;
import com.saicmobility.evcard.vlms.risk.enums.VehicleRiskStatusEnum;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarm;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import com.saicmobility.evcard.vlms.risk.model.RiskPushRecord;
import com.saicmobility.evcard.vlms.risk.service.sub.SubRiskAlaramService;
import com.saicmobility.evcard.vlms.risk.utils.RiskUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class SubRiskAlaramServiceImpl implements SubRiskAlaramService {

    @Resource
    private TableRiskPushRecordService tableRiskPushRecordService;

    @Resource
    private TableRiskCheckService tableRiskCheckService;

    @Resource
    private TableRiskAlarmService tableRiskAlarmService;

    @Resource
    private MdDataProxy mdDataProxy;
    @Override
    public void alarmRecoryNotify(String alarmNo) {
        // 通知长租
        RiskPushRecord riskPushRecord = tableRiskPushRecordService.query(alarmNo);
        if (riskPushRecord != null){
            RiskUtils.notifyCancelsRiskWarningToLongRent(alarmNo);
        }
    }

    @Override
    public String quertLongRentOrderByVin(String vin) {
        // 如果产品线是长租，且有进行中的长租订单 则推送长租
        QueryOperateContractInfoByVinsRes queryOperateContractInfoByVinsRes = mdDataProxy.queryOperateContractInfoByVins(QueryOperateContractInfoByVinsReq.newBuilder().addVinList(vin).build());
        if (queryOperateContractInfoByVinsRes == null || queryOperateContractInfoByVinsRes.getRetCode() != 0){
            log.error("查询长租订单合同信息失败! vin:{}", vin);
            return null;
        }
        List<OperateContractInfo> operateContractInfoList = queryOperateContractInfoByVinsRes.getInfoList();
        if (CollectionUtil.isEmpty(operateContractInfoList)){
            log.error("查询长租订单合同信息为空! vin:{}", vin);
            return null;
        }
        // 获取订单号
        OperateContractInfo operateContractInfo = operateContractInfoList.get(0);
        return operateContractInfo.getOrderNo();
    }

    @Override
    public boolean hasRiskCheckTask(String vin) {
        List<RiskCheck> riskChecks = tableRiskCheckService.selectByVin(vin, DealStatusEnum.DEAL_STATUS.getCode());
        if (CollectionUtil.isNotEmpty(riskChecks)){
            return true;
        }
        return false;
    }

    /**
     * 计算车辆风险等级
     * @param vin
     * @param alarmLevel
     * @param productLine
     * @param hasRiskCheck
     * @return
     */
    @Override
    public Integer getVehicleRiskStatus(String vin, Integer alarmLevel, Integer productLine, boolean hasRiskCheck) {

        Integer maxRiskLevel = this.queryMaxRiskLevel(vin, null);
        // 是否长短组
        boolean isLongShortGroup = RiskUtils.isLongShortGroup(productLine);
        // 是否收车任务
        boolean hasCollectionTask = hasRiskCheck ? hasRiskCheck : this.hasRiskCheckTask(vin);
        if (alarmLevel > maxRiskLevel){
            maxRiskLevel = alarmLevel;
        }
        return RiskUtils.getRiskStatus(isLongShortGroup, hasCollectionTask, maxRiskLevel);
    }


    /**
     * 查询最大的风险等级
     * @param vin
     * @param alarmNo 传alarmNo 则代表移除或者恢复
     */
    public Integer queryMaxRiskLevel(String vin, String alarmNo) {
        List<RiskAlarm> riskAlarmList = tableRiskAlarmService.selectAlarmVin(vin, AlarmStatusEnum.ALARM_STATUS.getCode(),2);
        int riskLevel = 0;
        for(RiskAlarm alarm : riskAlarmList){
            if (StringUtils.isNotBlank(alarmNo) && alarm.getAlarmNo().equals(alarmNo)){
                continue;
            }
            if(alarm.getAlarmLevel() > riskLevel){
                riskLevel = alarm.getAlarmLevel();
            }
        }
        return riskLevel;
    }

}
