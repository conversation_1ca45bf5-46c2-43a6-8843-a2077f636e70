package com.saicmobility.evcard.vlms.risk.mapper.extend;

import com.saicmobility.evcard.vlms.risk.dto.check.RiskCheckData;
import com.saicmobility.evcard.vlms.risk.mapper.RiskCheckMapper;
import com.saicmobility.evcard.vlms.risk.model.RiskCheck;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface RiskCheckExtendMapper extends RiskCheckMapper {

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SelectRiskCheckListResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="check_no", property="checkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="plate_no", property="plateNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="operation_org_id", property="operateOrgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="operation_org_name", property="operateOrgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="deal_status", property="dealStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="task_end_time", property="taskEndTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_oper_name", property="endOperName", jdbcType=JdbcType.VARCHAR),
            @Result(column="end_oper_account", property="endOperAccount", jdbcType=JdbcType.VARCHAR),
            @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_risk_status", property="vehicleRiskStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="out_control_type", property="outControlType", jdbcType=JdbcType.INTEGER),
            @Result(column="sub_product_line", property="subProductLine", jdbcType=JdbcType.INTEGER)
    })
    List<RiskCheckData> selectRiskCheckList(SelectStatementProvider selectStatementProvider);
}
