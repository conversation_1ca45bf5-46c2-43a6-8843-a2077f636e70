/**
 * 
 */
package com.saicmobility.evcard.vlms.risk.dto.terminal;

import lombok.Data;

import java.io.Serializable;

/**        
 * 项目名称：evcard-rts-api
 * 类名称：VehicleLocationDTO
 * 类描述：TODO
 * 创建人：ljh
 * 创建时间：2017年10月20日10:27:49
 * 修改备注：
 * @version1.0
 * 
 */
@Data
public class VehicleLocationDTO implements Serializable{
	// 纬度
	private float latitude;
	// 经度
	private float longitude; 
	// 纬度
	private float latitudeBD;
		// 经度
	private float longitudeBD; 
	// 定位
	private String fixedPosition; 
	// 位置
	private String positionName; 
	// 速度(WORD) 1/10km/h
	private float speed;
	// 方向(WORD) 0-359，正北为 0，顺时针
	private int direction;
	// 累计里程
	private String totalMileage;
	// 位置接收时间
	private String locationTime;
	// 创建时间
	private String timeStamp;
	
	/**
	 * 续航里程
	 */
	private Double endurance;
	/**
	 * 上传是否延迟.
	 */
	private Boolean isDelay;

	/**
	 * 更新时间.
	 */
	private String updatedTime;
}
