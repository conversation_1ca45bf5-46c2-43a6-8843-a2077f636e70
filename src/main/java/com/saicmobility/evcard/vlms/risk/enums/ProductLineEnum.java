package com.saicmobility.evcard.vlms.risk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @description 产品线
 **/
@AllArgsConstructor
@Getter
public enum ProductLineEnum {
    PRODUCT_LINE_ENUM_1(1, "车管中心"),
    PRODUCT_LINE_ENUM_2(2, "长租"),
    PRODUCT_LINE_ENUM_3(3, "短租"),
    PRODUCT_LINE_ENUM_4(4, "公务用车");

    private final Integer code;
    private final String value;

    public static String getValueByCode(Integer code){
        for (ProductLineEnum item : ProductLineEnum.values()) {
            if (item.getCode().equals(code)){
                return item.getValue();
            }
        }
        return StringUtils.EMPTY;
    }
}
