package com.saicmobility.evcard.vlms.risk.database.impl;

import com.saicmobility.evcard.vlms.risk.database.TableVehicleMovementDayStatisticsService;
import com.saicmobility.evcard.vlms.risk.mapper.VehicleMovementDayStatisticsMapper;
import com.saicmobility.evcard.vlms.risk.model.VehicleMovementDayStatistics;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static com.saicmobility.evcard.vlms.risk.mapper.RiskWhitelistConfigDynamicSqlSupport.riskWhitelistConfig;
import static com.saicmobility.evcard.vlms.risk.mapper.VehicleMovementDayStatisticsDynamicSqlSupport.vehicleMovementDayStatistics;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2024-02-21
 */
@Service
@Slf4j
public class TableVehicleMovementDayStatisticsServiceImpl implements TableVehicleMovementDayStatisticsService {

    @Resource
    private VehicleMovementDayStatisticsMapper vehicleMovementDayStatisticsMapper;

    @Override
    public List<VehicleMovementDayStatistics> queryVehicleMovementDayStatisticsByVin(String vin) {
        SelectStatementProvider render = select(
                vehicleMovementDayStatistics.allColumns())
                .from(vehicleMovementDayStatistics)
                .where()
                .and(vehicleMovementDayStatistics.vin, isEqualToWhenPresent(vin))
                .orderBy(riskWhitelistConfig.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleMovementDayStatisticsMapper.selectMany(render);
    }

    @Override
    public VehicleMovementDayStatistics queryDataByVinAndStatDate(String vin, String yesterdayStr) {
        SelectStatementProvider render = select(
                vehicleMovementDayStatistics.allColumns())
                .from(vehicleMovementDayStatistics)
                .where()
                .and(vehicleMovementDayStatistics.vin, isEqualToWhenPresent(vin))
                .and(vehicleMovementDayStatistics.statDate, isEqualToWhenPresent(yesterdayStr))
                .orderBy(riskWhitelistConfig.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleMovementDayStatisticsMapper.selectOne(render).orElse(null);
    }
}
