package com.saicmobility.evcard.vlms.risk.mapper.extend;

import com.saicmobility.evcard.vlms.risk.dto.riskalarm.RiskAlarmData;
import com.saicmobility.evcard.vlms.risk.mapper.RiskAlarmMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-08
 */
@Mapper
public interface RiskAlarmExtendMapper extends RiskAlarmMapper {


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SelectRiskAlarmListResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="alarm_no", property="alarmNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="alarm_type", property="alarmType", jdbcType=JdbcType.INTEGER),
            @Result(column="alarm_level", property="alarmLevelNum", jdbcType=JdbcType.INTEGER),
            @Result(column="plate_no", property="plateNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="operation_org_id", property="operateOrgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="operation_org_name", property="operateOrgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="alarm_status", property="alarmStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="recover_method", property="recoverMethod", jdbcType=JdbcType.INTEGER),
            @Result(column="alarm_system", property="alarmSystem", jdbcType=JdbcType.INTEGER),
            @Result(column="recover_channel", property="recoverChannel", jdbcType=JdbcType.INTEGER),
            @Result(column="alarm_time", property="alarmTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="last_location_time", property="lastLocationTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="last_location", property="lastLocation", jdbcType=JdbcType.VARCHAR),
            @Result(column="is_stop_alarm", property="isStopAlarm", jdbcType=JdbcType.INTEGER),
            @Result(column="stop_alarm_day", property="stopAlarmDay", jdbcType=JdbcType.INTEGER),
            @Result(column="pause_deadline_time", property="pauseDeadlineTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="recovery_date", property="recoveryDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_oper_account", property="createOperAccount", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_risk_status", property="vehicleRiskStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="sub_product_line", property="subProductLine", jdbcType=JdbcType.INTEGER)
    })
    List<RiskAlarmData> selectRiskAlarmList(SelectStatementProvider selectStatementProvider);
}
