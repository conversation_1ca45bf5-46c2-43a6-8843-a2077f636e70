package com.saicmobility.evcard.vlms.risk.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.dts.avro.AvroUtils;
import com.extracme.evcard.dts.avro.RdsModelProtobuf;
import com.extracme.evcard.dts.avro.TbField;
import com.saicmobility.evcard.vlms.risk.database.TableOperateLogService;
import com.saicmobility.evcard.vlms.risk.enums.AlarmTypeEnum;
import com.saicmobility.evcard.vlms.risk.enums.OperateTypeEnum;
import com.saicmobility.evcard.vlms.risk.model.OperateLog;
import com.saicmobility.evcard.vlms.risk.model.RiskAlarmConfig;
import com.saicmobility.evcard.vlms.risk.service.RiskAlarmConfigService;
import com.saicmobility.evcard.vlms.risk.utils.RdsDataHandler;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.AssetsVehicle;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.GetAssetsVehicleByVinReq;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.GetAssetsVehicleByVinRes;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.VlmsAssetsService;
import com.saicmobility.evcard.vlms.vlmsriskservice.api.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 监听vehicle_info表insert
 * <AUTHOR>
 * @date 2024-02-24
 */
@Slf4j
@Service
public class VehicleInfoInsertListener implements MessageListener {

    private static final String DELETE = "DELETE";
    private static final String INSERT = "INSERT";
    private static final String VEHICLE_INFO = "vehicle_info";

    @Resource
    private RiskAlarmConfigService riskAlarmConfigService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private TableOperateLogService tableOperateLogService;
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {

        log.info("vehicle_info消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        if(message.getBody() == null ){
            return Action.CommitMessage;
        }

        String tableName = message.getUserProperties("tableName");
        if(!VEHICLE_INFO.equals(tableName)) {
            return Action.CommitMessage;
        }
        String opt = message.getUserProperties("opt");
        RdsModelProtobuf rdsModelProtobuf = deserializeMessage(message.getBody());

        if (rdsModelProtobuf == null){
            return Action.CommitMessage;
        }
        List<TbField> fieldList = rdsModelProtobuf.getFieldList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return Action.CommitMessage;
        }
        Map<String, String> newDataMap = RdsDataHandler.returnMap(fieldList, opt);
        String vinCode = newDataMap.get("vin");
        if (StringUtils.isBlank(vinCode)){
            return Action.CommitMessage;
        }
        switch (opt) {
            case INSERT:
                //新增
                doInsertRiskAlarmConfig(vinCode);
                break;
            case DELETE:
                //删除
                break;
            default:
                break;
        }
        return Action.CommitMessage;
    }

    /**
     * 新增风控报警配置
     * @param vinCode
     */
    private void doInsertRiskAlarmConfig(String vinCode) {

        GetAssetsVehicleByVinRes res = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vinCode).build());
        List<AssetsVehicle> vehicles = res.getInfoList();
        if (CollectionUtil.isEmpty(vehicles)) {
            return;
        }
        AssetsVehicle vehicle = vehicles.get(0);
        Date now = new Date();
        for (AlarmTypeEnum alarmTypeEnum : AlarmTypeEnum.values()) {
            RiskAlarmConfig alarmConfig = buidRiskAlarmConfig(vehicle, alarmTypeEnum.getCode(), now);
            riskAlarmConfigService.insertRiskAlarmConfig(alarmConfig);
            saveOperateLog(StrUtil.format("新增车辆风控报警配置,  报警类型:【{}】", alarmTypeEnum.getValue()), alarmConfig.getId()+"", OperateTypeEnum.OPERATE_CONFIG.getCode(),
                    CurrentUser.newBuilder().setNickName("系统").setUserAccount("System").build());

        }
    }

    public void saveOperateLog(String operateContent, String relationKey, Integer operateType, CurrentUser currentUser){
        OperateLog operateLog = new OperateLog();
        operateLog.setForeignId(relationKey);
        operateLog.setOperateType(operateType);
        operateLog.setOperateContent(operateContent);
        operateLog.setCreateOperName(currentUser.getNickName());
        operateLog.setCreateOperAccount(currentUser.getUserAccount());
        tableOperateLogService.save(operateLog);
    }

    private RiskAlarmConfig buidRiskAlarmConfig(AssetsVehicle assetsVehicle, Integer alarmType, Date now) {
        RiskAlarmConfig riskAlarmConfig = new RiskAlarmConfig();
        riskAlarmConfig.setVin(assetsVehicle.getVin());
        riskAlarmConfig.setAlarmType(alarmType);
        riskAlarmConfig.setAlarmStartTime(now);
        riskAlarmConfig.setCreateOperName("系统");
        return riskAlarmConfig;
    }

    /**
     * 反序列化消息
     * @param message
     * @return
     */
    private static RdsModelProtobuf deserializeMessage(byte[] message){
        RdsModelProtobuf rdsModelProtobuf = null;
        try {
            rdsModelProtobuf = AvroUtils.deserialzeAvroFromByteArray(message);
        } catch (Exception e) {
            //消息解析异常
            e.printStackTrace();
        }
        return rdsModelProtobuf;
    }
}
