package com.saicmobility.evcard.vlms.risk.mapper.extend.assets;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import javax.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-03-05
 */
public final class AssetsVehicleDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    public static final AssetsVehicle assetsVehicle = new AssetsVehicle();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.id")
    public static final SqlColumn<Long> id = assetsVehicle.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vin")
    public static final SqlColumn<String> vin = assetsVehicle.vin;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.engine_no")
    public static final SqlColumn<String> engineNo = assetsVehicle.engineNo;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.color")
    public static final SqlColumn<String> color = assetsVehicle.color;

    /**
     * Database Column Remarks:
     *   资产状态 0  在建工程  1  固定资产  2  固定资产（待报废）3  报废  4  固定资产(待处置)  5  固定资产(已处置) 6以租代售 7库存商品 8 已处置（未过户）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_status")
    public static final SqlColumn<Integer> propertyStatus = assetsVehicle.propertyStatus;

    /**
     * Database Column Remarks:
     *   资产公司编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_id")
    public static final SqlColumn<String> propertyOrgId = assetsVehicle.propertyOrgId;

    /**
     * Database Column Remarks:
     *   资产公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.property_org_name")
    public static final SqlColumn<String> propertyOrgName = assetsVehicle.propertyOrgName;

    /**
     * Database Column Remarks:
     *   运营公司编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_id")
    public static final SqlColumn<String> operationOrgId = assetsVehicle.operationOrgId;

    /**
     * Database Column Remarks:
     *   运营公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.operation_org_name")
    public static final SqlColumn<String> operationOrgName = assetsVehicle.operationOrgName;

    /**
     * Database Column Remarks:
     *   交付状态 0 未交付 1 已交付
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.delivery_status")
    public static final SqlColumn<Integer> deliveryStatus = assetsVehicle.deliveryStatus;

    /**
     * Database Column Remarks:
     *   车辆型号id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = assetsVehicle.vehicleModelId;

    /**
     * Database Column Remarks:
     *   品牌型号id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.brand_model_id")
    public static final SqlColumn<Long> brandModelId = assetsVehicle.brandModelId;

    /**
     * Database Column Remarks:
     *   车辆渠道 0:普通 1 EVCARD 2 通用时行 4别克代步车 5松江大学城 6别克深度体验 7享道用车 8大众试乘试驾 9携程项目 10特殊车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_channel")
    public static final SqlColumn<Integer> vehicleChannel = assetsVehicle.vehicleChannel;

    /**
     * Database Column Remarks:
     *   车辆来源 0:本司采购  1外来租赁
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_source")
    public static final SqlColumn<Integer> vehicleSource = assetsVehicle.vehicleSource;

    /**
     * Database Column Remarks:
     *   投运状态 1:未投运 2:投运成功 3:投运审批中
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.put_in_status")
    public static final SqlColumn<Integer> putInStatus = assetsVehicle.putInStatus;

    /**
     * Database Column Remarks:
     *   终端id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.sys_term_id")
    public static final SqlColumn<Long> sysTermId = assetsVehicle.sysTermId;

    /**
     * Database Column Remarks:
     *   商业险到期日
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.commercial_insurance_expire")
    public static final SqlColumn<Date> commercialInsuranceExpire = assetsVehicle.commercialInsuranceExpire;

    /**
     * Database Column Remarks:
     *   交强险到期日
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.compulsory_insurance_expire")
    public static final SqlColumn<Date> compulsoryInsuranceExpire = assetsVehicle.compulsoryInsuranceExpire;

    /**
     * Database Column Remarks:
     *   转固时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_transfer_time")
    public static final SqlColumn<Date> vehicleTransferTime = assetsVehicle.vehicleTransferTime;

    /**
     * Database Column Remarks:
     *   2:长租 3:短租
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.profit_center")
    public static final SqlColumn<Integer> profitCenter = assetsVehicle.profitCenter;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.is_deleted")
    public static final SqlColumn<Integer> isDeleted = assetsVehicle.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_time")
    public static final SqlColumn<Date> createTime = assetsVehicle.createTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_account")
    public static final SqlColumn<String> createOperAccount = assetsVehicle.createOperAccount;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.create_oper_name")
    public static final SqlColumn<String> createOperName = assetsVehicle.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_time")
    public static final SqlColumn<Date> updateTime = assetsVehicle.updateTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_account")
    public static final SqlColumn<String> updateOperAccount = assetsVehicle.updateOperAccount;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.update_oper_name")
    public static final SqlColumn<String> updateOperName = assetsVehicle.updateOperName;

    /**
     * Database Column Remarks:
     *   系统同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_update_time")
    public static final SqlColumn<Date> systemUpdateTime = assetsVehicle.systemUpdateTime;

    /**
     * Database Column Remarks:
     *   系统同步编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.system_sync_code")
    public static final SqlColumn<String> systemSyncCode = assetsVehicle.systemSyncCode;

    /**
     * Database Column Remarks:
     *   车辆风险状态（1-无风险 2-风险车辆 3-风控车辆 4-失控车辆）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: vlms_assets.t_assets_vehicle.vehicle_risk_status")
    public static final SqlColumn<Integer> vehicleRiskStatus = assetsVehicle.vehicleRiskStatus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: vlms_assets.t_assets_vehicle")
    public static final class AssetsVehicle extends AliasableSqlTable<AssetsVehicle> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> engineNo = column("engine_no", JDBCType.VARCHAR);

        public final SqlColumn<String> color = column("color", JDBCType.VARCHAR);

        public final SqlColumn<Integer> propertyStatus = column("property_status", JDBCType.INTEGER);

        public final SqlColumn<String> propertyOrgId = column("property_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> propertyOrgName = column("property_org_name", JDBCType.VARCHAR);

        public final SqlColumn<String> operationOrgId = column("operation_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> operationOrgName = column("operation_org_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> deliveryStatus = column("delivery_status", JDBCType.INTEGER);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<Long> brandModelId = column("brand_model_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> vehicleChannel = column("vehicle_channel", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleSource = column("vehicle_source", JDBCType.INTEGER);

        public final SqlColumn<Integer> putInStatus = column("put_in_status", JDBCType.INTEGER);

        public final SqlColumn<Long> sysTermId = column("sys_term_id", JDBCType.BIGINT);

        public final SqlColumn<Date> commercialInsuranceExpire = column("commercial_insurance_expire", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> compulsoryInsuranceExpire = column("compulsory_insurance_expire", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> vehicleTransferTime = column("vehicle_transfer_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> profitCenter = column("profit_center", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createOperAccount = column("create_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateOperAccount = column("update_oper_account", JDBCType.VARCHAR);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> systemUpdateTime = column("system_update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> systemSyncCode = column("system_sync_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> vehicleRiskStatus = column("vehicle_risk_status", JDBCType.INTEGER);

        public AssetsVehicle() {
            super("vlms_assets.t_assets_vehicle", AssetsVehicle::new);
        }
    }
}